# Docker 配置
DOCKER_IMAGE := t-ai2:latest
DOCKER_CONTAINER := t-ai2-container

.PHONY: dev
.PHONY: run
.PHONY: debug_dev
.PHONY: reload_dev
.PHONY: docker-build
.PHONY: docker-run
.PHONY: docker-stop
.PHONY: docker-clean
.PHONY: docker-env-setup

# 检查 .env 文件是否存在，如果不存在则从 env.example 复制
check-env:
	@if [ ! -f .env ]; then \
		if [ -f env.example ]; then \
			cp env.example .env; \
			echo "✅ 已从 env.example 创建 .env 文件，请按实际情况填充环境变量值！！！"; \
		else \
			echo "⚠️  未找到 .env 或 env.example 文件，请手动创建 .env 文件"; \
		fi; \
	fi

# dev
# 目前dev脚本 为开启热重载以及debug(debugpy方案 如果是pycharm 可以考虑用pydev更简单)
dev: check-env
	DEBUG_MODE=true SERVER_RELOAD=true uv run -- python -Xfrozen_modules=off -m t_ai2.main

# debug_dev
debug_dev: check-env
	DEBUG_MODE=true uv run -- python -Xfrozen_modules=off -m t_ai2.main

# reload_dev
reload_dev: check-env
	SERVER_RELOAD=true uv run -- python -Xfrozen_modules=off -m t_ai2.main

# run
run: check-env
	uv run -m t_ai2.main

# docker-env-setup
# 从 env.example 创建 .env 文件
docker-env-setup:
	@if [ ! -f .env ]; then \
		if [ -f env.example ]; then \
			cp env.example .env; \
			echo "✅ 已创建 .env 文件，请按实际情况填充环境变量值！！！"; \
		else \
			echo "⚠️  未找到 env.example 文件"; \
		fi; \
	else \
		echo "⚠️  .env 文件已存在，请手动检查配置"; \
	fi

# docker-build
# 构建 Docker 镜像
docker-build:
	docker build -t $(DOCKER_IMAGE) .

# docker-run
# 运行 Docker 容器，请保证项目根目录下存在 .env 文件，文件中配置必要的环境变量
docker-run:
	docker run --rm -d --name $(DOCKER_CONTAINER) --env-file .env -p 8000:8000 -p 5678:5678 --cpus=2 --memory=4g $(DOCKER_IMAGE)

# docker-stop
# 停止 Docker 容器
docker-stop:
	docker stop $(DOCKER_CONTAINER) || true

# docker-clean
# 清理 Docker 镜像和容器
docker-clean: docker-stop
	docker rmi $(DOCKER_IMAGE) || true
