repos:
  # 1. 通用代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 基础文件检查
      - id: trailing-whitespace
        description: "移除行尾空白字符"
      - id: end-of-file-fixer
        description: "确保文件以换行符结尾"
      - id: check-yaml
        description: "检查 YAML 文件语法"
      - id: check-json
        description: "检查 JSON 文件语法"
      - id: check-toml
        description: "检查 TOML 文件语法"
      - id: check-merge-conflict
        description: "检查合并冲突标记"
      - id: check-case-conflict
        description: "检查文件名大小写冲突"
      - id: check-docstring-first
        description: "检查文档字符串位置"
      - id: check-ast
        description: "检查 Python 语法"

  # 2. Python 代码格式化
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        description: "Python 代码格式化"
        language_version: python3.12
        args: ['--line-length=120', '--target-version=py312']
        types: [python]

  # 3. Python 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        description: "Python 导入排序"
        args: [
          '--profile=black',
          '--line-length=120',
          '--multi-line=3',
          '--trailing-comma',
          '--force-grid-wrap=0',
          '--use-parentheses',
          '--ensure-newline-before-comments'
        ]

# 全局配置
default_language_version:
  python: python3.12

# 排除文件
exclude: |
  (?x)^(
      \.git/|
      \.venv/|
      \.pytest_cache/|
      \.tach/|
      \.ropeproject/|
      \.vscode/|
      __pycache__/|
      \.mypy_cache/|
      \.coverage|
      \.pytest_cache/|
      build/|
      dist/|
      \.eggs/|
      \.tox/|
      \.env|
      uv\.lock|
      \.bandit
  )$

# 提交前检查配置
fail_fast: false
