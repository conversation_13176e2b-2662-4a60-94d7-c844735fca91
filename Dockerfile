FROM registry.erda.cloud/retag/python:3.12-slim-bookworm

# tz
ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive

# Install uv.
COPY --from=registry.erda.cloud/retag/ghcr_io_astral-sh_uv:0.6.8 /uv /uvx /bin/

# Copy the application into the container.
COPY . /app

# Install the application dependencies.
WORKDIR /app

# sync dep using uv.lock and no cache
RUN uv sync --frozen --no-cache -v

# validation using tach
RUN uv run tach check-external

# http server default port
EXPOSE 8000
# http server debugger port
EXPOSE 5678

ENTRYPOINT ["bash","entrypoint.sh"]
