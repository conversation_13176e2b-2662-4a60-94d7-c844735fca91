version: "1.1"
name: ""
"on":
  push:
    branches:
      - release/develop
stages:
  - stage:
      - git-checkout:
          alias: repo
          description: 代码仓库克隆
          params:
            depth: 1
#  - stage:
#      - custom-script:
#          alias: 检查python版本依赖
#          version: "1.0"
#          image: registry.cn-hangzhou.aliyuncs.com/ceerdecy/python:tach-1.0.0
#          commands:
#            - cd ${repo}
#            - ls -l
#            - tach check-external
#          resources:
#            cpu: 1
#            mem: 1024
  - stage:
      - dockerfile:
          alias: build-app
          description: 针对自定义 dockerfile 打包，产出可运行镜像
          params:
            path: ./Dockerfile
            workdir: ${repo}
  - stage:
      - release:
          alias: release
          description: 用于打包完成时，向dicehub 提交完整可部署的dice.yml。用户若没在pipeline.yml里定义该action，CI会自动在pipeline.yml里插入该action
          params:
            dice_yml: ${repo}/dice.yml
            image:
              t-ai2: ${build-app:OUTPUT:image}
  - stage:
      - dice:
          alias: dice
          description: 用于 Erda 平台部署应用服务
          params:
            deploy_without_branch: true
            release_id: ${release:OUTPUT:releaseID}
