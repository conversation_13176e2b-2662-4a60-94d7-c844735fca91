import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger
from sqlalchemy.orm import Session
from t_ai_agent_ops.model.common import PaginatedResponse, PaginationParams
from t_ai_agent_ops.model.crud import EvaluationTaskCRUD
from t_ai_agent_ops.model.db_config import get_db
from t_ai_agent_ops.model.evaluation_model import (
    EvaluationTaskCreate,
    EvaluationTaskInDB,
    EvaluationTaskUpdate,
)
from t_ai_agent_ops.service.agent_service import AgentService
from t_ai_agent_ops.service.dataset_service import DatasetService
from t_ai_agent_ops.service.scoring_service import ScoringService

from ..utils.req_ctx_helper import get_current_team_id, get_current_user_id, get_current_user_name
from ..utils.timezone_utils import china_now

# 创建全局服务实例
_evaluation_service = None


def get_evaluation_service() -> "EvaluationService":
    """获取评估服务实例"""
    global _evaluation_service
    if _evaluation_service is None:
        _evaluation_service = EvaluationService()
    return _evaluation_service


class EvaluationService:
    def __init__(self):
        self.dataset_service = DatasetService()
        self.agent_service = AgentService()
        self.scoring_service = ScoringService()
        self._background_tasks = set()

    async def create_and_run_task(self, task_create: EvaluationTaskCreate) -> EvaluationTaskInDB:
        """创建评估任务并异步执行"""
        try:
            # 验证数据集是否存在
            logger.info(f"Validating dataset for evaluation task by ID: {task_create.dataset_id}")
            dataset = await self.dataset_service.get_dataset(task_create.dataset_id)

            if not dataset:
                logger.error(f"Dataset not found: {task_create.dataset_id}")
                return None

            # 获取数据库会话
            db = next(get_db())
            try:
                # 创建评估任务
                import json

                meta_info_dict = {
                    "evaluationType": task_create.evaluation_type,
                    "evaluationRounds": task_create.evaluation_rounds,
                    "executionTime": china_now().isoformat(),
                    "agentConfig": task_create.agent_config.dict() if task_create.agent_config else None,
                }
                # 从 ReqCtx 获取当前用户和团队信息
                current_user_id = get_current_user_id()
                current_user_name = get_current_user_name()
                current_team_id = get_current_team_id()

                task = EvaluationTaskCRUD.create_task(
                    db=db,
                    dataset_id=task_create.dataset_id,
                    name=task_create.name,
                    portal_key=task_create.portal_key,
                    agent_key=task_create.agent_key,
                    description=task_create.description,
                    meta_info=json.dumps(meta_info_dict),
                    created_by=current_user_id or current_user_name or task_create.created_by,
                    team_id=current_team_id or task_create.team_id,
                )

                # 创建后台任务异步执行评估
                background_task = asyncio.create_task(self._run_evaluation_background(task.id))
                self._background_tasks.add(background_task)
                background_task.add_done_callback(self._background_tasks.discard)

                # 立即返回任务信息
                return EvaluationTaskInDB.model_validate(task)
            finally:
                db.close()

        except ValueError as ve:
            logger.error(f"Validation error in create_and_run_task: {str(ve)}")
            return None
        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f"Error in create_and_run_task: {str(e)}")
            return None

    async def _run_evaluation_background(self, task_id: int) -> None:
        """在后台异步运行评估任务"""
        try:
            # 获取任务信息
            db = next(get_db())
            try:
                task = EvaluationTaskCRUD.get_task(db, task_id)
                if not task:
                    logger.error(f"Task not found: {task_id}")
                    return
            finally:
                db.close()

            # 执行评估
            await self.run_evaluation(task)

        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f"后台评估任务失败: {str(e)}")

            # 更新任务状态为失败，进度保持当前值
            try:
                db = next(get_db())
                try:
                    current_user_id = get_current_user_id()
                    current_user_name = get_current_user_name()
                    EvaluationTaskCRUD.update_task_status(
                        db=db,
                        task_id=task_id,
                        status="failed",
                        error_message=str(e),
                        updated_by=current_user_id or current_user_name,
                        # 不修改进度，保持当前进度值
                    )
                finally:
                    db.close()
            except Exception as db_error:
                logger.error(f"更新任务状态失败: {str(db_error)}")

    async def run_evaluation(self, task: EvaluationTaskInDB) -> EvaluationTaskInDB:
        """运行评测任务"""
        import json

        # 解析 meta_info JSON 字符串
        try:
            meta_info = json.loads(task.meta_info) if task.meta_info else {}
        except (json.JSONDecodeError, TypeError):
            logger.error("Invalid meta_info JSON format")
            return None

        if not meta_info.get("agentConfig"):
            logger.error("Agent config not provided")
            return None

        db = next(get_db())
        try:
            # 更新任务状态为运行中，进度设为0
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            task = EvaluationTaskCRUD.update_task_status(
                db=db, task_id=task.id, status="running", progress=0.0, updated_by=current_user_id or current_user_name
            )

            # 获取数据集项目
            dataset_items = await self.dataset_service.get_dataset_items(task.dataset_id)
            if not dataset_items:
                logger.error(f"Dataset {task.dataset_id} is empty")
                return None
            logger.debug(f"################ Dataset items length: {len(dataset_items)} detail: {dataset_items}")

            evaluation_results = []
            total_latency = 0
            total_tokens = 0
            error_count = 0
            success_count = 0
            agent_config = meta_info.get("agentConfig")
            evaluation_type = meta_info.get("evaluationType")
            evaluation_rounds = meta_info.get("evaluationRounds", 1)
            total_items = len(dataset_items)
            total_rounds = total_items * evaluation_rounds

            # 遍历数据集项目进行多轮评测
            current_round_index = 0
            for round_num in range(1, evaluation_rounds + 1):
                logger.info(f"开始第 {round_num} 轮评估，共 {evaluation_rounds} 轮")
                # 遍历数据集项目进行评测
                for index, item in enumerate(dataset_items):
                    try:
                        logger.info(f"Processing item {item['id']} in round {round_num}")

                        # 准备输入数据
                        input_data = item.get("input", "")

                        # 调用 Agent
                        start_time = china_now()
                        try:
                            session_id = str(uuid.uuid4())
                            result = await self.agent_service.call_agent_direct(
                                session_id=session_id,
                                agent_config=agent_config,
                                input_content=input_data,
                                portal_key=task.portal_key,
                            )
                            logger.debug(f"agent result: {result}")

                            # 评估结果
                            evaluation = None

                            evaluation = await self.scoring_service.evaluate_response(
                                actual_output=result.get("content", ""),
                                expected_output=item.get("expectedOutput"),
                                input_question=input_data,
                            )
                            success_count += 1

                            # 计算延迟
                            latency = (china_now() - start_time).total_seconds()
                            total_latency += latency
                            # 统计 token 使用
                            total_tokens += result.get("totalTokens", 0)

                            # 记录评测结果
                            evaluation_results.append(
                                {
                                    "datasetItemId": item["id"],
                                    "round": round_num,
                                    "input": input_data,
                                    "output": result.get("content"),
                                    "expectedOutput": item.get("expectedOutput"),
                                    "evaluation": evaluation,
                                    "latency": latency,
                                    "tokens": result.get("totalTokens", 0),
                                    "inputTokens": result.get("inputTokens", 0),
                                    "outputTokens": result.get("outputTokens", 0),
                                    "error": None,
                                }
                            )

                        except Exception as e:
                            import traceback

                            traceback.print_exc()
                            error_count += 1
                            logger.error(f"Error processing item {item['id']} in round {round_num}: {str(e)}")
                            evaluation_results.append(
                                {
                                    "datasetItemId": item["id"],
                                    "round": round_num,
                                    "input": input_data,
                                    "error": str(e),
                                    "latency": (china_now() - start_time).total_seconds(),
                                }
                            )

                        # 更新进度
                        current_round_index += 1
                        current_progress = current_round_index / total_rounds
                        current_user_id = get_current_user_id()
                        EvaluationTaskCRUD.update_task_progress(
                            db=db, task_id=task.id, progress=current_progress, updated_by=current_user_id
                        )
                        logger.info(f"Updated task {task.id} progress to {current_progress:.2%}")

                    except Exception as e:
                        import traceback

                        traceback.print_exc()
                        logger.error(f"Error in evaluation run for item: {str(e)}")
                        continue

            # 计算整体指标
            successful_evaluations = [r for r in evaluation_results if r.get("evaluation")]

            metrics = {
                "successRate": success_count / total_rounds if total_rounds > 0 else 0,
                "errorRate": error_count / total_rounds if total_rounds > 0 else 0,
                "avgLatency": total_latency / success_count if success_count > 0 else 0,
                "avgTokens": total_tokens / success_count if success_count > 0 else 0,
                "totalTokens": total_tokens,
                "totalItems": total_items,
                "totalRounds": evaluation_rounds,
                "totalEvaluations": total_rounds,
                "successCount": success_count,
                "errorCount": error_count,
            }

            # 添加评分指标
            if successful_evaluations:
                avg_overall_score = sum(r["evaluation"]["overallScore"] for r in successful_evaluations) / len(
                    successful_evaluations
                )
                metrics["avgOverallScore"] = avg_overall_score

                # 计算各个标准的平均分
                all_criteria = set()
                for r in successful_evaluations:
                    all_criteria.update(r["evaluation"]["scores"].keys())

                for criterion in all_criteria:
                    scores = [r["evaluation"]["scores"].get(criterion, 0) for r in successful_evaluations]
                    metrics[f"avgScore_{criterion}"] = sum(scores) / len(successful_evaluations)

            # 更新任务状态和结果，进度设为100%
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            updated_db_task = EvaluationTaskCRUD.update_task_status(
                db=db,
                task_id=task.id,
                status="completed",
                progress=1.0,
                result={"evaluationResults": evaluation_results, "metrics": metrics},
                updated_by=current_user_id or current_user_name,
            )

            logger.info(f"Evaluation task completed: {task.id}")

            return EvaluationTaskInDB.model_validate(updated_db_task) if updated_db_task else None

        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f"评测任务执行失败: {str(e)}")
            # 更新任务状态为失败
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            updated_db_task = EvaluationTaskCRUD.update_task_status(
                db=db,
                task_id=task.id,
                status="failed",
                error_message=str(e),
                updated_by=current_user_id or current_user_name,
            )
            return None
        finally:
            db.close()

    async def get_task(self, task_id: int) -> Optional[EvaluationTaskInDB]:
        """获取评估任务"""
        db = next(get_db())
        try:
            return EvaluationTaskCRUD.get_task(db, task_id)
        finally:
            db.close()

    async def list_tasks(
        self, pagination: PaginationParams, agent_key: Optional[str] = None
    ) -> PaginatedResponse[EvaluationTaskInDB]:
        """获取评估任务分页列表
        Args:
            pagination: 分页参数
            agent_key: 按 agent key 过滤
        """
        db = next(get_db())
        try:
            # 获取总数
            total = EvaluationTaskCRUD.get_tasks_count(
                db, agent_key=agent_key, team_id=pagination.team_id if hasattr(pagination, "team_id") else None
            )

            # 获取数据，支持排序
            db_tasks = EvaluationTaskCRUD.get_tasks(
                db,
                skip=pagination.skip,
                limit=pagination.limit,
                agent_key=agent_key,
                team_id=pagination.team_id if hasattr(pagination, "team_id") else None,
                sort_by=pagination.sortBy,
                sort_order=pagination.sortOrder,
            )

            # 将数据库模型转换为 Pydantic 模型
            tasks = [EvaluationTaskInDB.model_validate(db_task) for db_task in db_tasks]

            return PaginatedResponse.create(tasks, total, pagination.pageNumber, pagination.pageSize)
        finally:
            db.close()

    async def get_evaluation_report(self, task: EvaluationTaskInDB) -> Dict[str, Any]:
        """获取评测报告"""
        if not task.result:
            return {
                "taskId": task.id,
                "taskName": task.name,
                "status": task.status,
                "progress": task.progress,
                "errorMessage": task.error_message,
            }

        metrics = task.result.get("metrics", {})
        evaluation_results = task.result.get("evaluationResults", [])

        report = {
            "taskId": task.id,
            "taskName": task.name,
            "evaluationType": task.evaluation_type,
            "datasetId": task.dataset_id,
            "status": task.status,
            "progress": task.progress,
            "metrics": metrics,
            "createdAt": task.created_at.isoformat(),
            "completedAt": task.completed_at.isoformat() if task.completed_at else None,
            "totalItems": len(evaluation_results),
            "successRate": metrics.get("successRate", 0),
            "errorRate": metrics.get("errorRate", 0),
            "avgLatency": metrics.get("avgLatency", 0),
            "avgTokens": metrics.get("avgTokens", 0),
            "totalTokens": metrics.get("totalTokens", 0),
            "avgOverallScore": metrics.get("avgOverallScore", 0),
        }

        # 添加详细的评估结果
        if evaluation_results:
            report["evaluationResults"] = evaluation_results

        return report

    async def update_task(self, task_id: str, task_update: EvaluationTaskUpdate) -> Optional[EvaluationTaskInDB]:
        """更新评测任务"""
        try:
            # 转换 task_id 为整数
            task_id_int = int(task_id)

            # 获取数据库会话
            db = next(get_db())
            try:
                # 准备更新数据
                update_data = {}

                # 处理可能的字段更新
                if task_update.name is not None:
                    update_data["name"] = task_update.name
                if task_update.evaluation_type is not None:
                    update_data["evaluation_type"] = task_update.evaluation_type
                if task_update.dataset_id is not None:
                    update_data["dataset_id"] = task_update.dataset_id
                if task_update.agent_key is not None:
                    update_data["agent_key"] = task_update.agent_key
                if task_update.agent_config is not None:
                    update_data["agent_config"] = task_update.agent_config.dict() if task_update.agent_config else None
                if task_update.langfuse_trace_id is not None:
                    update_data["langfuse_trace_id"] = task_update.langfuse_trace_id
                if task_update.langfuse_metadata is not None:
                    update_data["langfuse_metadata"] = task_update.langfuse_metadata
                if task_update.evaluation_results is not None:
                    update_data["evaluation_results"] = task_update.evaluation_results
                if task_update.metrics is not None:
                    update_data["metrics"] = task_update.metrics

                # 处理用户和团队信息
                current_user_id = get_current_user_id()
                current_user_name = get_current_user_name()
                current_team_id = get_current_team_id()

                if task_update.updated_by is not None:
                    update_data["updated_by"] = task_update.updated_by
                elif current_user_id or current_user_name:
                    update_data["updated_by"] = current_user_id or current_user_name

                if task_update.team_id is not None:
                    update_data["team_id"] = task_update.team_id
                elif current_team_id:
                    update_data["team_id"] = current_team_id

                # 调用 CRUD 更新方法
                updated_db_task = EvaluationTaskCRUD.update_task(db, task_id_int, **update_data)
                if updated_db_task is None:
                    return None
                # 将数据库模型转换为 Pydantic 模型
                return EvaluationTaskInDB.model_validate(updated_db_task)

            finally:
                db.close()

        except ValueError as e:
            logger.error(f"Invalid task_id: {task_id}")
            raise ValueError(f"Invalid task_id: {task_id}")
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {str(e)}")
            raise

    async def delete_task(self, task_id: str) -> bool:
        """删除评测任务"""
        try:
            # 转换 task_id 为整数
            task_id_int = int(task_id)

            # 获取数据库会话
            db = next(get_db())
            try:
                # 先检查任务是否存在
                task = EvaluationTaskCRUD.get_task(db, task_id_int)
                if not task:
                    return False

                # 删除任务
                db.delete(task)
                db.commit()
                return True

            finally:
                db.close()

        except ValueError:
            logger.error(f"Invalid task_id: {task_id}")
            return False
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {str(e)}")
            return False
