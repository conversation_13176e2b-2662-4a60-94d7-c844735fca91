from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from ..utils.timezone_utils import china_now


class AgentConfig(BaseModel):
    """Agent配置模型"""

    key: str = Field(..., description="Agent Key")
    name: str = Field(..., description="Agent名称")
    type: str = Field(..., description="Agent类型")
    props: Dict[str, Any] = Field(..., description="Agent属性配置")


class EvaluationTaskBase(BaseModel):
    """评估任务基础模型"""

    name: str = Field(..., description="任务名称")
    evaluation_type: str | None = Field(None, alias="evaluationType", description="评估类型")
    dataset_id: int = Field(..., alias="datasetId", description="测试数据集ID")
    agent_key: str = Field(..., alias="agentKey", description="Agent Key")
    agent_config: Optional[AgentConfig] = Field(None, alias="agentConfig", description="Agent 配置")

    model_config = ConfigDict(populate_by_name=True)


class EvaluationTaskCreate(BaseModel):
    """创建评估任务的请求模型"""

    name: str = Field(..., description="任务名称")
    evaluation_type: str | None = Field(None, alias="evaluationType", description="评估类型")
    description: Optional[str] = Field(None, description="任务描述")
    dataset_id: int = Field(..., alias="datasetId", description="测试数据集ID")
    agent_key: Optional[str] = Field(None, alias="agentKey", description="Agent Key")
    agent_config: Optional[Dict[str, Any]] = Field(None, alias="agentConfig", description="Agent 配置")
    evaluation_rounds: int = Field(
        1, alias="evaluationRounds", ge=1, le=100, description="评估轮次，默认为1，最大100轮"
    )
    referer: Optional[str] = Field(None, alias="referer", description="Referer")
    portal_key: Optional[str] = Field(None, alias="portalKey", description="Portal Key")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class EvaluationTaskUpdate(BaseModel):
    """更新评估任务的请求模型"""

    name: Optional[str] = Field(None, description="任务名称")
    evaluation_type: Optional[str] = Field(None, alias="evaluationType", description="评估类型")
    description: Optional[str] = Field(None, description="任务描述")
    dataset_id: Optional[int] = Field(None, alias="datasetId", description="测试数据集ID")
    agent_key: Optional[str] = Field(None, alias="agentKey", description="Agent Key")
    agent_config: Optional[Dict[str, Any]] = Field(None, alias="agentConfig", description="Agent 配置")
    langfuse_trace_id: Optional[str] = Field(None, alias="langfuseTraceId", description="Langfuse追踪ID")
    langfuse_metadata: Optional[Dict[str, Any]] = Field(
        None, alias="langfuseMetadata", description="Langfuse集成元数据"
    )
    evaluation_results: Optional[List[Dict[str, Any]]] = Field(None, alias="evaluationResults", description="评测结果")
    metrics: Optional[Dict[str, float]] = Field(None, description="评测指标")
    status: Optional[str] = None
    progress: Optional[float] = Field(None, description="任务进度，0.0-1.0之间的浮点数")
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = Field(None, alias="errorMessage", description="错误信息")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class EvaluationTaskInDB(EvaluationTaskBase):
    """数据库中的评估任务模型"""

    id: int = Field(..., description="任务ID")
    status: str = Field(default="pending", description="任务状态")
    progress: float = Field(default=0.0, description="任务进度，0.0-1.0之间的浮点数")
    result: Optional[Dict[str, Any]] = Field(None, description="评估结果")
    error_message: Optional[str] = Field(None, alias="errorMessage", description="错误信息")
    meta_info: Optional[str] = Field(None, alias="metaInfo", description="任务元数据")
    portal_key: Optional[str] = Field(None, alias="portalKey", description="Portal Key")
    created_at: datetime = Field(default_factory=china_now, alias="createdAt")
    updated_at: datetime = Field(default_factory=china_now, alias="updatedAt")
    completed_at: Optional[datetime] = Field(None, alias="completedAt")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")
    version: int = Field(default=1, description="版本号")

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)
