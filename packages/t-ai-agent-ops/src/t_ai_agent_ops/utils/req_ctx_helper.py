from typing import Optional

from t_ai_app.ctx import ReqCtx


def get_current_team_id() -> Optional[str]:
    """从 ReqCtx 中获取当前请求的 team_id"""
    header = ReqCtx.get_header()
    if header and header.t_ai_source_team:
        return header.t_ai_source_team
    else:
        raise ValueError("Team id is required")


def get_current_user_id() -> Optional[str]:
    """从 ReqCtx 中获取当前请求的用户ID"""
    header = ReqCtx.get_header()
    if header and header.t_ai_user_id:
        return header.t_ai_user_id
    return None


def get_current_user_name() -> Optional[str]:
    """从 ReqCtx 中获取当前请求的用户名"""
    header = ReqCtx.get_header()
    if header and header.t_ai_user_name:
        return header.t_ai_user_name
    return None


def get_current_tenant_id() -> Optional[str]:
    """从 ReqCtx 中获取当前请求的租户ID"""
    header = ReqCtx.get_header()
    if header and header.t_ai_source_tenant_id:
        return header.t_ai_source_tenant_id
    return None
