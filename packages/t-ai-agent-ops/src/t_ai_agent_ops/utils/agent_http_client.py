import json
import os
import uuid
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx
from loguru import logger
from t_ai_app.ctx import ReqCtx


class AgentHttpClient:
    """Agent HTTP 客户端"""

    def __init__(self, referer: str = None):
        """初始化 Agent HTTP 客户端

        Args:
            referer: HTTP 请求的 referer，用于动态生成 base URL
        """
        self.referer = referer
        self.base_url = (
            self._extract_base_url_from_referer(referer)
            if referer
            else os.getenv("AGENT_API_BASE_URL", "https://t-erp-portal-dev.app.terminus.io")
        )
        self.request_id = os.getenv("TERMINUS_REQUEST_ID", str(uuid.uuid4()))

        logger.info(f"Initializing AgentHttpClient with base_url: {self.base_url} (from referer: {referer})")

    def _extract_base_url_from_referer(self, referer: str) -> str:
        """从 referer 中提取 base URL

        Args:
            referer: HTTP 请求的 referer

        Returns:
            str: 提取的 base URL
        """
        try:
            from urllib.parse import urlparse

            parsed_url = urlparse(referer)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            logger.debug(f"Extracted base URL from referer '{referer}': {base_url}")
            return base_url
        except Exception as e:
            logger.warning(f"Failed to extract base URL from referer '{referer}': {str(e)}")
            return os.getenv("AGENT_API_BASE_URL", "https://t-erp-portal-dev.app.terminus.io")

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        # 使用传入的 referer 或默认值，确保不为 None
        referer = self.referer or ""

        headers = {
            "sec-ch-ua-platform": '"macOS"',
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "accept": "text/event-stream",
            "terminus-request-id": self.request_id,
            "Content-Type": "application/json",
        }

        # 只有当 referer 不为空时才添加 Referer 头
        if referer:
            headers["Referer"] = referer

        logger.debug(f"生成的请求头: {headers}")
        return headers

    async def call_agent(
        self,
        agent_key: str,
        session_id: str,
        user_content: str,
        attachments: Optional[list] = None,
        application_scene: int = 1,
    ) -> Dict[str, Any]:
        """调用 Agent API

        Args:
            agent_key: Agent 的 key，如 'AI$erp_main_agent'
            session_id: 会话ID
            user_content: 用户输入内容
            attachments: 附件列表，默认为空列表
            application_scene: 应用场景，默认为1

        Returns:
            Dict[str, Any]: 包含响应内容和 token 信息的字典
        """
        try:
            url = f"{self.base_url}/api/trantor/agent/execute/{agent_key}"

            # 构建请求数据
            request_data = {
                "params": {
                    "sessionId": session_id,
                    "userContent": user_content,
                    "attachments": attachments or [],
                    "applicationScene": application_scene,
                }
            }

            logger.debug(f"Calling agent API: {url}")
            logger.debug(f"Request data: {json.dumps(request_data, ensure_ascii=False)}")

            cookie = ReqCtx.get_header().t_ai_source_cookie

            # 调试信息：检查对象类型和方法
            logger.debug(f"AgentHttpClient 对象类型: {type(self)}")
            logger.debug(f"AgentHttpClient 对象方法: {[method for method in dir(self) if not method.startswith('_')]}")
            logger.debug(f"AgentHttpClient 对象所有属性: {dir(self)}")

            try:
                headers = self._get_headers()
                logger.debug(f"成功调用 _get_headers 方法")
            except AttributeError as e:
                logger.error(f"AgentHttpClient 对象没有 _get_headers 方法: {e}")
                logger.error(f"可用的方法: {[method for method in dir(self) if 'header' in method.lower()]}")
                raise

            # 确保 cookie 不为 None 且不为空字符串时才添加到请求头
            if cookie and cookie.strip():
                headers["Cookie"] = cookie

            # 最终检查：确保所有请求头的值都不为 None
            headers = {k: v for k, v in headers.items() if v is not None}

            logger.debug(f"request agent api url: {url}")
            logger.debug(f"request agent api headers: {headers}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(url=url, json=request_data, headers=headers)

                logger.debug(f"Response status: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"Agent API error: {response.status_code}")
                    logger.error(f"Response text: {response.text}")
                    raise Exception(f"Agent API 调用失败: {response.status_code} - {response.text}")

                # 解析 SSE 响应
                parsed_result = await self._parse_sse_response(response)

                # 返回解析后的结果，包含真实的 token 信息
                return {
                    "content": parsed_result["content"],
                    "totalTokens": parsed_result["tokenUsage"]["totalTokens"],
                    "inputTokens": parsed_result["tokenUsage"]["inputTokens"],
                    "outputTokens": parsed_result["tokenUsage"]["outputTokens"],
                    "cachedTokens": parsed_result["tokenUsage"]["cachedTokens"],
                    "reasoningTokens": parsed_result["tokenUsage"]["reasoningTokens"],
                    "costTime": parsed_result["costTime"],
                }

        except Exception as e:
            logger.error(f"调用 Agent API 失败: {str(e)}")
            raise ValueError(f"调用 Agent API 失败: {str(e)}")

    async def _parse_sse_response(self, response: httpx.Response) -> Dict[str, Any]:
        """解析 SSE (Server-Sent Events) 响应

        Args:
            response: HTTP 响应对象

        Returns:
            Dict[str, Any]: 包含解析后的内容和 token 信息的字典
        """
        content_parts = []
        token_usage = {"inputTokens": 0, "cachedTokens": 0, "outputTokens": 0, "reasoningTokens": 0, "totalTokens": 0}
        cost_time = 0.0

        logger.debug(f"开始解析 SSE 响应...")
        async for line in response.aiter_lines():
            line = line.strip()
            if not line:
                continue

            logger.debug(f"处理 SSE 行: {line}")

            # 处理 data: 开头的行
            if line.startswith("data:"):
                data = line[5:]  # 移除 "data: " 前缀
                try:
                    # 尝试解析 JSON 数据
                    json_data = json.loads(data)
                    logger.debug(f"解析到 SSE 数据: {json_data}")

                    if not isinstance(json_data, dict):
                        logger.debug(f"跳过非字典类型数据: {type(json_data)}")
                        continue

                    # 处理不同类型的消息
                    content = json_data.get("content", {})
                    content_type = content.get("type", "")

                    logger.debug(f"内容类型: {content_type}")

                    # 处理文本内容
                    if content_type == "text":
                        text_content = content.get("text", "").strip()
                        if text_content:
                            # 如果前一个内容也是文本类型，则直接拼接，不换行
                            if content_parts and not content_parts[-1].startswith(
                                ("👉 转交至", "Function Call:", "Function Call Arguments:", "Function Call Output:")
                            ):
                                content_parts[-1] += text_content
                            else:
                                content_parts.append(text_content)
                            logger.debug(f"添加文本内容: {text_content[:100]}...")

                    # 处理 Agent 切换
                    elif content_type == "agent_handoff":
                        target_agent = content.get("targetAgent", {})
                        agent_key = target_agent.get("key", "")
                        agent_name = target_agent.get("name", "")
                        handoff_text = content.get("text", "").strip()

                        if handoff_text or agent_key or agent_name:
                            output_parts = []
                            if handoff_text:
                                output_parts.append(handoff_text)
                            if agent_key:
                                output_parts.append(f"Target Agent: {agent_name} ({agent_key})")

                            output = " ".join(output_parts)
                            content_parts.append(output)
                            logger.debug(f"添加 Agent 切换信息: {output}")

                    # 处理函数调用输出
                    elif content_type == "function_call":
                        function_key = content.get("key", "")
                        function_name = content.get("name", "")
                        function_args = content.get("arguments", "")
                        content_parts.append(f"Function Call: {function_name} ({function_key})")
                        content_parts.append(f"Function Call Arguments: {function_args}")

                    # 处理函数调用输出
                    elif content_type == "function_call_arguments":
                        function_args = content.get("arguments", "")
                        # 删除 content_parts 中最后一个元素，即默认的 Function Call Arguments，再添加参数映射出来的 Function Call Arguments
                        content_parts.pop()
                        content_parts.append(f"Function Call Arguments: {function_args}")

                    # 处理函数调用输出
                    elif content_type == "function_call_output":
                        output = content.get("output", "").strip()
                        content_parts.append(f"Function Call Output: {output}")

                    # 处理 Token 使用信息
                    elif content_type == "tokens":
                        usage = content.get("usage", {})
                        if usage:
                            token_usage.update(
                                {
                                    "inputTokens": usage.get("inputTokens", 0),
                                    "cachedTokens": usage.get("cachedTokens", 0),
                                    "outputTokens": usage.get("outputTokens", 0),
                                    "reasoningTokens": usage.get("reasoningTokens", 0),
                                    "totalTokens": usage.get("totalTokens", 0),
                                }
                            )
                            cost_time = content.get("costTime", 0.0)
                            logger.debug(f"更新 Token 使用信息: {token_usage}")

                    # 处理直接返回的文本内容（不包含在 content 对象中）
                    elif "text" in json_data:
                        text_content = json_data.get("text", "").strip()
                        if text_content:
                            content_parts.append(text_content)
                            logger.debug(f"添加直接文本内容: {text_content[:100]}...")

                except json.JSONDecodeError as e:
                    logger.debug(f"无法解析的 SSE 数据: {data}, 错误: {e}")
                    continue
                except Exception as e:
                    logger.warning(f"处理 SSE 数据时发生错误: {e}, 数据: {data}")
                    continue

        # 合并所有文本内容，不同类型的消息之间用换行分隔
        final_content = "\n".join(content_parts)

        return {"content": final_content, "tokenUsage": token_usage, "costTime": cost_time}

    async def batch_call_agent(
        self, agent_key: str, input_batch: List[Dict[str, Any]], max_concurrent: int = 5
    ) -> List[Dict[str, Any]]:
        """批量调用 Agent API

        Args:
            agent_key: Agent 的 key
            input_batch: 输入批次列表
            max_concurrent: 最大并发数

        Returns:
            List[Dict[str, Any]]: 批量调用结果
        """
        import asyncio
        import time

        semaphore = asyncio.Semaphore(max_concurrent)

        async def call_with_semaphore(input_data: Dict[str, Any], index: int) -> Dict[str, Any]:
            async with semaphore:
                # 为每个请求生成唯一的session_id
                session_id = f"eval_{int(time.time())}_{index}"
                user_content = input_data.get("question", str(input_data))

                try:
                    start_time = time.time()

                    # 调用 Agent API
                    result = await self.call_agent(
                        agent_key=agent_key, session_id=session_id, user_content=user_content
                    )

                    latency = time.time() - start_time

                    return {
                        "content": result.get("content", ""),
                        "totalTokens": result.get("totalTokens", 0),
                        "inputTokens": result.get("inputTokens", 0),
                        "outputTokens": result.get("outputTokens", 0),
                        "cachedTokens": result.get("cachedTokens", 0),
                        "reasoningTokens": result.get("reasoningTokens", 0),
                        "costTime": result.get("costTime", 0.0),
                        "latency": latency,
                        "sessionId": session_id,
                    }

                except Exception as e:
                    logger.error(f"调用 Agent API 失败 (session {session_id}): {str(e)}")
                    return {"error": str(e), "latency": 0, "sessionId": session_id}

        tasks = [call_with_semaphore(input_data, i) for i, input_data in enumerate(input_batch)]

        return await asyncio.gather(*tasks)
