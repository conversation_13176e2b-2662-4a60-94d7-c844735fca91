[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "t-ai-agent-ops"
version = "1.0.0"
authors = [
    { name="Your Name", email="<EMAIL>" },
]
description = "T-AI Agent Operations API for evaluation tasks and test datasets"
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
dependencies = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "pydantic>=2.0.0",
    "loguru>=0.6.0",
    "httpx>=0.24.0",
    "aiohttp>=3.8.0",
    # 实际使用的依赖
    "sqlalchemy>=2.0.0",
    "pymysql>=1.0.0",
    "alembic>=1.8.0",  # 用于数据库迁移
    "t-ai-web",
    "t-ai-app",
    "t-ai-agent",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml"]

# 添加workspace sources配置以解析内部依赖
[tool.uv.sources]
t-ai-agent = { workspace = true }
