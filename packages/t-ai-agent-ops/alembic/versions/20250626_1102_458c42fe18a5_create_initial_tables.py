"""create initial tables

Revision ID: 458c42fe18a5
Revises:
Create Date: 2025-06-26 11:02:28.882744+00:00

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "458c42fe18a5"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "datasets",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False, comment="数据集名称"),
        sa.Column("description", sa.Text(), nullable=True, comment="数据集描述"),
        sa.Column("meta_info", sa.JSON(), nullable=True, comment="数据集元数据"),
        sa.Column("created_at", sa.DateTime(), nullable=True, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=True, comment="更新时间"),
        sa.Primary<PERSON>ey<PERSON>onstraint("id"),
    )
    op.create_table(
        "dataset_items",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("dataset_id", sa.Integer(), nullable=False, comment="所属数据集ID"),
        sa.Column("input_data", sa.JSON(), nullable=False, comment="输入数据"),
        sa.Column("expected_output", sa.JSON(), nullable=True, comment="期望输出"),
        sa.Column("meta_info", sa.JSON(), nullable=True, comment="元数据"),
        sa.Column("created_at", sa.DateTime(), nullable=True, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=True, comment="更新时间"),
        sa.ForeignKeyConstraint(
            ["dataset_id"],
            ["datasets.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "evaluation_tasks",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("dataset_id", sa.Integer(), nullable=False, comment="关联数据集ID"),
        sa.Column("name", sa.String(length=255), nullable=False, comment="任务名称"),
        sa.Column("description", sa.Text(), nullable=True, comment="任务描述"),
        sa.Column("status", sa.String(length=50), nullable=False, comment="任务状态"),
        sa.Column("meta_info", sa.JSON(), nullable=True, comment="任务元数据"),
        sa.Column("result", sa.JSON(), nullable=True, comment="评估结果"),
        sa.Column("error_message", sa.Text(), nullable=True, comment="错误信息"),
        sa.Column("created_at", sa.DateTime(), nullable=True, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=True, comment="更新时间"),
        sa.ForeignKeyConstraint(
            ["dataset_id"],
            ["datasets.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("evaluation_tasks")
    op.drop_table("dataset_items")
    op.drop_table("datasets")
    # ### end Alembic commands ###
