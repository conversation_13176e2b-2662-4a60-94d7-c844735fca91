"""添加portal_key字段到evaluation_tasks表

Revision ID: 359f2f7cdcee
Revises: 45fb6a7aa6e5
Create Date: 2025-07-15 05:54:34.092133+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "359f2f7cdcee"
down_revision = "45fb6a7aa6e5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "evaluation_tasks", sa.Column("portal_key", sa.String(length=255), nullable=True, comment="Portal Key")
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("evaluation_tasks", "portal_key")
    # ### end Alembic commands ###
