"""添加评估任务进度字段

Revision ID: 45fb6a7aa6e5
Revises: 20250626_1103
Create Date: 2025-06-27 08:49:14.361798+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "45fb6a7aa6e5"
down_revision = "20250626_1103"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 移除对不存在的 agent_key 字段的操作
    # op.alter_column('datasets', 'agent_key',
    #            existing_type=mysql.VARCHAR(length=255),
    #            nullable=False,
    #            comment='关联的 agent key')
    op.add_column(
        "evaluation_tasks",
        sa.Column("progress", sa.Float(), nullable=False, default=0.0, comment="任务进度，0.0-1.0之间的浮点数"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("evaluation_tasks", "progress")
    # 移除对不存在的 agent_key 字段的操作
    # op.alter_column('datasets', 'agent_key',
    #            existing_type=mysql.VARCHAR(length=255),
    #            nullable=True,
    #            comment=None,
    #            existing_comment='关联的 agent key')
    # ### end Alembic commands ###
