"""为所有表添加created_by_updated_by_team_id_version字段

Revision ID: 9d8966f60d49
Revises: d7b8fda936d9
Create Date: 2025-07-16 11:34:20.957357+00:00

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "9d8966f60d49"
down_revision = "d7b8fda936d9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 为 datasets 表添加新字段
    op.add_column("datasets", sa.Column("created_by", sa.String(255), nullable=True, comment="创建者"))
    op.add_column("datasets", sa.Column("updated_by", sa.String(255), nullable=True, comment="更新者"))
    op.add_column("datasets", sa.Column("team_id", sa.String(255), nullable=True, comment="团队ID"))
    op.add_column("datasets", sa.Column("version", sa.Integer(), nullable=False, server_default="1", comment="版本号"))

    # 为 dataset_items 表添加新字段
    op.add_column("dataset_items", sa.Column("created_by", sa.String(255), nullable=True, comment="创建者"))
    op.add_column("dataset_items", sa.Column("updated_by", sa.String(255), nullable=True, comment="更新者"))
    op.add_column("dataset_items", sa.Column("team_id", sa.String(255), nullable=True, comment="团队ID"))
    op.add_column(
        "dataset_items", sa.Column("version", sa.Integer(), nullable=False, server_default="1", comment="版本号")
    )

    # 为 evaluation_tasks 表添加新字段
    op.add_column("evaluation_tasks", sa.Column("created_by", sa.String(255), nullable=True, comment="创建者"))
    op.add_column("evaluation_tasks", sa.Column("updated_by", sa.String(255), nullable=True, comment="更新者"))
    op.add_column("evaluation_tasks", sa.Column("team_id", sa.String(255), nullable=True, comment="团队ID"))
    op.add_column(
        "evaluation_tasks", sa.Column("version", sa.Integer(), nullable=False, server_default="1", comment="版本号")
    )


def downgrade() -> None:
    # 移除 datasets 表的新字段
    op.drop_column("datasets", "version")
    op.drop_column("datasets", "team_id")
    op.drop_column("datasets", "updated_by")
    op.drop_column("datasets", "created_by")

    # 移除 dataset_items 表的新字段
    op.drop_column("dataset_items", "version")
    op.drop_column("dataset_items", "team_id")
    op.drop_column("dataset_items", "updated_by")
    op.drop_column("dataset_items", "created_by")

    # 移除 evaluation_tasks 表的新字段
    op.drop_column("evaluation_tasks", "version")
    op.drop_column("evaluation_tasks", "team_id")
    op.drop_column("evaluation_tasks", "updated_by")
    op.drop_column("evaluation_tasks", "created_by")
