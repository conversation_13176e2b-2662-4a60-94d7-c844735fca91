"""修复dataset_items表字段类型不匹配问题

Revision ID: 4329dabb9b22
Revises: 359f2f7cdcee
Create Date: 2025-07-15 08:28:19.276262+00:00

"""

import json

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "4329dabb9b22"
down_revision = "359f2f7cdcee"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 获取数据库连接
    connection = op.get_bind()

    # 首先，我们需要处理现有的数据，将非JSON格式的文本转换为JSON格式
    # 查询所有 dataset_items 记录
    result = connection.execute(sa.text("SELECT id, input_data, expected_output, meta_info FROM dataset_items"))
    items = result.fetchall()

    for item in items:
        item_id, input_data, expected_output, meta_info = item

        # 处理 input_data
        if input_data is not None:
            try:
                # 尝试解析为JSON
                json.loads(input_data)
                # 如果成功，说明已经是JSON格式，不需要修改
            except (json.JSONDecodeError, TypeError):
                # 如果不是有效的JSON，包装为 {"text": input_data}
                new_input_data = json.dumps({"text": input_data})
                connection.execute(
                    sa.text("UPDATE dataset_items SET input_data = :input_data WHERE id = :id"),
                    {"input_data": new_input_data, "id": item_id},
                )

        # 处理 expected_output
        if expected_output is not None:
            try:
                json.loads(expected_output)
            except (json.JSONDecodeError, TypeError):
                new_expected_output = json.dumps({"text": expected_output})
                connection.execute(
                    sa.text("UPDATE dataset_items SET expected_output = :expected_output WHERE id = :id"),
                    {"expected_output": new_expected_output, "id": item_id},
                )

        # 处理 meta_info
        if meta_info is not None:
            try:
                json.loads(meta_info)
            except (json.JSONDecodeError, TypeError):
                new_meta_info = json.dumps({"text": meta_info})
                connection.execute(
                    sa.text("UPDATE dataset_items SET meta_info = :meta_info WHERE id = :id"),
                    {"meta_info": new_meta_info, "id": item_id},
                )

    # 现在修改字段类型为JSON
    # 注意：MySQL中JSON类型字段的修改需要特殊处理
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN input_data JSON NOT NULL COMMENT '输入数据'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN expected_output JSON NULL COMMENT '期望输出'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN meta_info JSON NULL COMMENT '元数据'")


def downgrade() -> None:
    # 回滚：将JSON字段改回TEXT类型
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN input_data TEXT NOT NULL COMMENT '输入数据'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN expected_output TEXT NULL COMMENT '期望输出'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN meta_info TEXT NULL COMMENT '元数据'")
