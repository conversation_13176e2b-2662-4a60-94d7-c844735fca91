"""确保evaluation_tasks表的meta_info字段为TEXT类型

Revision ID: d7b8fda936d9
Revises: 165c88a57d67
Create Date: 2025-07-16 05:34:37.532668+00:00

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d7b8fda936d9"
down_revision = "165c88a57d67"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 确保 evaluation_tasks 表的 meta_info 字段为 TEXT 类型
    # 如果字段已经是 TEXT 类型，这个操作不会产生任何影响
    op.execute("ALTER TABLE evaluation_tasks MODIFY COLUMN meta_info TEXT NULL COMMENT '任务元数据'")


def downgrade() -> None:
    # 回滚：将 TEXT 字段改回 JSON 类型（如果需要的话）
    # 注意：这个操作可能会丢失数据，因为 TEXT 到 JSON 的转换可能不安全
    pass
