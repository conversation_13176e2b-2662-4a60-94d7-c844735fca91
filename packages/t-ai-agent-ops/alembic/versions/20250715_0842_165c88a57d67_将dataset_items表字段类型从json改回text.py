"""将dataset_items表字段类型从JSON改回TEXT

Revision ID: 165c88a57d67
Revises: 4329dabb9b22
Create Date: 2025-07-15 08:42:19.276262+00:00

"""

import json

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "165c88a57d67"
down_revision = "4329dabb9b22"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 获取数据库连接
    connection = op.get_bind()

    # 首先，我们需要处理现有的数据，将JSON格式转换为纯文本
    # 查询所有 dataset_items 记录
    result = connection.execute(sa.text("SELECT id, input_data, expected_output, meta_info FROM dataset_items"))
    items = result.fetchall()

    for item in items:
        item_id, input_data, expected_output, meta_info = item

        # 处理 input_data
        if input_data is not None:
            try:
                # 尝试解析为JSON
                parsed_data = json.loads(input_data)
                if isinstance(parsed_data, dict):
                    # 如果是字典，优先提取 text 字段，否则转为字符串
                    new_input_data = parsed_data.get("text", str(parsed_data))
                else:
                    new_input_data = str(parsed_data)
            except (json.JSONDecodeError, TypeError):
                # 如果不是有效的JSON，直接使用原字符串
                new_input_data = str(input_data)

            # 确保字符串是有效的
            if new_input_data is None:
                new_input_data = ""

            try:
                connection.execute(
                    sa.text("UPDATE dataset_items SET input_data = :input_data WHERE id = :id"),
                    {"input_data": new_input_data, "id": item_id},
                )
            except Exception as e:
                print(f"Error updating input_data for item {item_id}: {e}")
                # 如果更新失败，尝试使用一个安全的默认值
                connection.execute(
                    sa.text("UPDATE dataset_items SET input_data = :input_data WHERE id = :id"),
                    {"input_data": str(input_data) if input_data else "", "id": item_id},
                )

        # 处理 expected_output
        if expected_output is not None:
            try:
                parsed_data = json.loads(expected_output)
                if isinstance(parsed_data, dict):
                    new_expected_output = parsed_data.get("text", str(parsed_data))
                else:
                    new_expected_output = str(parsed_data)
            except (json.JSONDecodeError, TypeError):
                new_expected_output = str(expected_output)

            if new_expected_output is None:
                new_expected_output = ""

            try:
                connection.execute(
                    sa.text("UPDATE dataset_items SET expected_output = :expected_output WHERE id = :id"),
                    {"expected_output": new_expected_output, "id": item_id},
                )
            except Exception as e:
                print(f"Error updating expected_output for item {item_id}: {e}")
                connection.execute(
                    sa.text("UPDATE dataset_items SET expected_output = :expected_output WHERE id = :id"),
                    {"expected_output": str(expected_output) if expected_output else "", "id": item_id},
                )

        # 处理 meta_info
        if meta_info is not None:
            try:
                parsed_data = json.loads(meta_info)
                if isinstance(parsed_data, dict):
                    new_meta_info = parsed_data.get("text", str(parsed_data))
                else:
                    new_meta_info = str(parsed_data)
            except (json.JSONDecodeError, TypeError):
                new_meta_info = str(meta_info)

            if new_meta_info is None:
                new_meta_info = ""

            try:
                connection.execute(
                    sa.text("UPDATE dataset_items SET meta_info = :meta_info WHERE id = :id"),
                    {"meta_info": new_meta_info, "id": item_id},
                )
            except Exception as e:
                print(f"Error updating meta_info for item {item_id}: {e}")
                connection.execute(
                    sa.text("UPDATE dataset_items SET meta_info = :meta_info WHERE id = :id"),
                    {"meta_info": str(meta_info) if meta_info else "", "id": item_id},
                )

    # 现在修改字段类型为TEXT
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN input_data TEXT NOT NULL COMMENT '输入数据'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN expected_output TEXT NULL COMMENT '期望输出'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN meta_info TEXT NULL COMMENT '元数据'")


def downgrade() -> None:
    # 回滚：将TEXT字段改回JSON类型
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN input_data JSON NOT NULL COMMENT '输入数据'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN expected_output JSON NULL COMMENT '期望输出'")
    op.execute("ALTER TABLE dataset_items MODIFY COLUMN meta_info JSON NULL COMMENT '元数据'")
