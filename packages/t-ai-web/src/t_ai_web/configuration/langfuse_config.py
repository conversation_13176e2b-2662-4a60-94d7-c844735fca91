import os

from loguru import logger


def setup_langfuse():
    """
    设置 Langfuse 配置
    """
    try:
        # 从环境变量获取 Langfuse 配置
        langfuse_public_key = os.getenv("LANGFUSE_PUBLIC_KEY")
        langfuse_secret_key = os.getenv("LANGFUSE_SECRET_KEY")
        langfuse_host = os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com")

        if not langfuse_public_key or not langfuse_secret_key:
            logger.warning("Langfuse credentials not found in environment variables")
            return False

        # 设置全局变量
        from t_ai_app import G

        G.LANGFUSE_PUBLIC_KEY = langfuse_public_key
        G.LANGFUSE_SECRET_KEY = langfuse_secret_key
        G.LANGFUSE_HOST = langfuse_host

        # 验证配置 - 使用 Langfuse 2.x 版本的 API
        try:
            from langfuse.openai import openai

            openai.langfuse_public_key = langfuse_public_key
            openai.langfuse_secret_key = langfuse_secret_key
            openai.langfuse_host = langfuse_host
            openai.langfuse_auth_check()
            logger.info("Langfuse initialized successfully")
            return True
        except ImportError as import_error:
            logger.warning(f"Langfuse import failed: {import_error}. Continuing without Langfuse.")
            return False
        except Exception as langfuse_error:
            logger.warning(f"Langfuse configuration failed: {langfuse_error}. Continuing without Langfuse.")
            return False
    except Exception as e:
        logger.error(f"Error initializing Langfuse: {e}")
        return False
