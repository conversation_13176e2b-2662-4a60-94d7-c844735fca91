from phoenix.otel import register


def setup_tracing():
    from t_ai_app import G

    if not G.APP_SETTING.tracing_settings.enabled:
        return
    api_key = G.APP_SETTING.tracing_settings.api_key
    endpoint = G.APP_SETTING.tracing_settings.endpoint

    register(
        project_name="t-ai2-agent",
        auto_instrument=True,  # Auto-instrument your app based on installed dependencies
        endpoint=endpoint,
        headers={"Authorization": f"Bearer {api_key}"},  # auth
        batch=True,
    )
