import debugpy
from loguru import logger
from t_ai_app import G


def setup_debugger():
    debug_config = G.APP_SETTING.debug_settings
    if not debug_config.enabled:
        return

    logger.info(f"Debugger starting on {debug_config.host}:{debug_config.port}")
    debugpy.listen((debug_config.host, debug_config.port))

    if debug_config.wait_client:
        logger.info("Waiting for debug client...")
        debugpy.wait_for_client()
