from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response
from t_ai_app.ctx import TRACE_ID_HEADER_NAME, ReqCtx


class ReqCtxMiddleWare(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        ReqCtx.set_from_request(request)
        try:
            res = await call_next(request)
            res.headers[TRACE_ID_HEADER_NAME] = ReqCtx.get_trace_id()
            return res
        finally:
            ReqCtx.reset()
