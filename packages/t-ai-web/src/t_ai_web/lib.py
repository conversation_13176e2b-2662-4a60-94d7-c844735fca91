import uvicorn
from fastapi import FastAP<PERSON>
from loguru import logger
from t_ai_agent_ops.api.dataset import router as dataset_router
from t_ai_agent_ops.api.evaluation import router as evaluation_router
from t_ai_app import G
from t_ai_web.middlewares.req_ctx_middleware import ReqCtxMiddleWare

from .routers.agent import agent_router
from .routers.attachment import attachment_router
from .routers.chat import chat_router
from .routers.docx import doc_router
from .routers.knowledge_base import knowledge_base_router
from .routers.mcp import mcp_router
from .routers.memory import memory_router

app = FastAPI()

app.include_router(agent_router, prefix="/api/ai")
app.include_router(mcp_router, prefix="/api/ai")
app.include_router(chat_router, prefix="/api/ai")
app.include_router(knowledge_base_router, prefix="/api/ai")
app.include_router(attachment_router, prefix="/api/ai")
app.include_router(memory_router, prefix="/api/ai")

# agent-ops 路由
app.include_router(evaluation_router, prefix="/api/agent-ops")
app.include_router(dataset_router, prefix="/api/agent-ops")

# docx api 用于测试doc相关能力
app.include_router(doc_router, prefix="/api/doc")

app.add_middleware(ReqCtxMiddleWare)


@app.get("/")
async def hello():
    return "Hello,World"


def web_start():
    server_config = G.APP_SETTING.server

    # 构建 uvicorn 运行参数
    run_kwargs = {
        "app": "t_ai_web.lib:app",
        "host": server_config.host,
        "port": server_config.port,
        "reload": server_config.reload,
    }

    uvicorn.run(**run_kwargs)
