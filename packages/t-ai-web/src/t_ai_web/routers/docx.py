from fastapi import APIRouter
from t_ai_docx.service.models import DocGenrateReq, DocGenrateRes
from t_ai_docx.service.service import (
    do_docx_generate,
    get_generate_docx_progress,
    stop_docx_generate,
)

doc_router = APIRouter()


@doc_router.post("/generate_from_template")
async def chat_bi(req: DocGenrateReq) -> DocGenrateRes:
    return await do_docx_generate(req)


@doc_router.get("/stop_generate/{task_id}")
async def stop_generate(task_id: str):
    return await stop_docx_generate(task_id)


@doc_router.get("/generate_progress/{task_id}")
async def generate_progress(task_id: str):
    return await get_generate_docx_progress(task_id)
