from typing import Any, List

from fastapi import APIRouter, HTTPException, Request
from loguru import logger
from pydantic import BaseModel, Field
from t_ai_knowledge_base.common.config import EmbeddingSettings, MilvusSettings
from t_ai_knowledge_base.model.agent_knowledge_base_dsl import KnowledgeBaseRetriever
from t_ai_knowledge_base.model.knowledge_base_dsl import KnowledgeBase, KnowledgeBaseDocument
from t_ai_knowledge_base.model.vectorization import SplitResult
from t_ai_knowledge_base.service.knowledge_base_service import KnowledgeBaseService

knowledge_base_router = APIRouter(prefix="/knowledge-base")


def get_knowledge_base_service() -> KnowledgeBaseService:
    """
    获取知识库服务实例（延迟初始化）

    这样设计的好处：
    1. 避免在工程启动时就尝试连接 Milvus
    2. 只有在真正需要时才建立连接
    3. 如果 Milvus 连接失败，不会影响整个工程启动
    """
    milvus_settings = MilvusSettings()
    embedding_settings = EmbeddingSettings()

    return KnowledgeBaseService(milvus_settings=milvus_settings, embedding_settings=embedding_settings)


# 定义请求模型，让FastAPI自动处理验证
class SplitRequest(BaseModel):
    key: str  # 知识库的key（作为collection_name）
    document: KnowledgeBaseDocument


class VectorizationResponse(BaseModel):
    """向量化响应模型"""

    split_result: SplitResult
    vectorization_result: dict


class RetrievalRequest(BaseModel):
    query: str
    knowledge_bases: list[dict] = Field([], alias="knowledgeBases")
    retrieval_config: dict = Field(default=None, alias="retrievalConfig")


class DeleteDocumentRequest(BaseModel):
    """删除文档请求模型"""

    key: str = Field(..., description="知识库的key")
    document: KnowledgeBaseDocument = Field(..., description="要删除的文档")


@knowledge_base_router.post("/split", response_model=VectorizationResponse)
async def split(request: SplitRequest):
    """文档切分并向量化接口"""
    try:
        logger.info(f"开始处理文档切分请求: 知识库key={request.key}, 文档key={request.document.key}")
        logger.info(f"接收到的文档数据: {request.document}")

        # 延迟初始化知识库服务
        knowledge_base_service = get_knowledge_base_service()

        # 执行文档切分，使用文档的key作为document_key
        split_result: SplitResult = knowledge_base_service.document_split(request.document.key, request.document)

        logger.info(f"文档切分完成: {split_result.total_chunks}个块")

        # 将切分结果存入向量数据库，使用知识库key作为collection_name
        vectorization_result = knowledge_base_service.vectorization(split_result, collection_name=request.key)

        logger.info(f"向量化完成: {vectorization_result}")

        return VectorizationResponse(split_result=split_result, vectorization_result=vectorization_result)

    except Exception as e:
        logger.error(f"处理文档切分和向量化时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理文档时出错: {str(e)}")


@knowledge_base_router.post("/retrieval")
async def retrieval(request: RetrievalRequest):
    """文档检索接口"""
    try:
        logger.info(f"开始文档检索: query={request.query[:50]}...")

        # 延迟初始化知识库服务
        knowledge_base_service = get_knowledge_base_service()

        # 解析检索配置
        retrieval_config = KnowledgeBaseRetriever.model_validate(request.retrieval_config)

        # 解析知识库数据
        knowledge_bases = []
        for i, knowledge_base_data in enumerate(request.knowledge_bases, 1):
            knowledge_base = KnowledgeBase.from_dict(knowledge_base_data)
            knowledge_bases.append(knowledge_base)

        result = knowledge_base_service.multi_knowledge_base_retrieval(request.query, knowledge_bases, retrieval_config)

        logger.info(f"检索完成: 返回{len(result)}个文档")
        return result

    except Exception as e:
        logger.error(f"文档检索时出错: {e}")
        raise HTTPException(status_code=500, detail=f"检索文档时出错: {str(e)}")


@knowledge_base_router.delete("/delete-document")
async def delete_document(request: DeleteDocumentRequest):
    """删除文档接口"""
    try:
        logger.info(f"开始删除文档: 知识库key={request.key}, 文档key={request.document.key}")

        # 延迟初始化知识库服务
        knowledge_base_service = get_knowledge_base_service()

        # 删除文档
        result = knowledge_base_service.delete_document(document_key=request.document.key, collection_name=request.key)

        if result:
            logger.info(f"文档删除成功: {request.document.key}")
            return {"status": "success", "message": f"文档 {request.document.key} 已成功删除"}
        else:
            logger.warning(f"文档删除失败或不存在: {request.document.key}")
            return {"status": "warning", "message": f"文档 {request.document.key} 可能不存在或删除失败"}

    except Exception as e:
        logger.error(f"删除文档时出错: {e}")
        raise HTTPException(status_code=500, detail=f"删除文档时出错: {str(e)}")


@knowledge_base_router.get("/health")
async def health_check():
    """知识库系统健康检查接口"""
    try:
        logger.info("开始执行知识库系统健康检查")

        # 延迟初始化知识库服务
        knowledge_base_service = get_knowledge_base_service()

        # 执行健康检查
        health_result = knowledge_base_service.vector_store_manager.health_check()

        logger.info(f"健康检查完成: {health_result['overall_status']}")

        # 根据健康状态设置HTTP状态码
        if health_result["overall_status"] == "healthy":
            return health_result
        else:
            raise HTTPException(status_code=503, detail=health_result)

    except Exception as e:
        logger.error(f"健康检查时出错: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@knowledge_base_router.get("/test-connection")
async def test_connection():
    """测试Milvus连接的接口"""
    try:
        logger.info("开始测试Milvus连接")

        # 延迟初始化知识库服务
        knowledge_base_service = get_knowledge_base_service()

        # 测试连接
        connection_result = knowledge_base_service.vector_store_manager.test_connection()

        logger.info(f"连接测试完成: {connection_result['status']}")

        # 根据连接状态设置HTTP状态码
        if connection_result["status"] == "success":
            return connection_result
        else:
            raise HTTPException(status_code=503, detail=connection_result)

    except Exception as e:
        logger.error(f"连接测试时出错: {e}")
        raise HTTPException(status_code=500, detail=f"连接测试失败: {str(e)}")
