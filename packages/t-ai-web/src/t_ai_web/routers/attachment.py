import asyncio
from typing import List, Optional

from fastapi import APIRouter
from pydantic import BaseModel
from t_ai_agent.ai_proxy_client_factory import AiProxyClientFactory
from t_ai_agent.utils.attachment_handler import create_client, create_file, fetch_attachment

ai_proxy_client_factory = AiProxyClientFactory()

attachment_router = APIRouter(prefix="/attachment")


class AttachmentInfo(BaseModel):
    url: str
    name: str


class AttachmentAnalyzeRequest(BaseModel):
    attachments: List[AttachmentInfo]
    prompt: str
    analysis_type: Optional[str] = None


# 路由处理函数
@attachment_router.post("/analyze")
async def analyze_attachment(req: AttachmentAnalyzeRequest):
    """
    分析附件 URL 列表，返回 {文件名: 分析结果文本} 的 map
    """
    client = await create_client("2449b943-e9a4-4fc2-98a0-45eaa334eb04")

    tasks = [process_url(attachment.url, client, req.prompt, attachment.name) for attachment in req.attachments]
    results = await asyncio.gather(*tasks)

    result_map = {filename: result for filename, result in results if result is not None}
    return result_map


async def process_url(url: str, client, prompt: str, filename: str) -> tuple:
    """处理单个URL的附件分析"""
    try:
        file_data = await fetch_attachment(url)
        if not file_data:
            return None

        file_extension = filename.split(".")[-1].lower()

        if file_extension in ["txt", "csv", "md"]:
            content = file_data.decode("utf-8")
            result_text = await analyze_with_ai(client, content, prompt)
            return filename, result_text
        else:
            file_object = await create_file(client, "file-extract", file_data)
            if not file_object:
                return filename, "文件上传失败，无法获取文件对象"

            content = f"fileid://{file_object.id}"
            result_text = await analyze_with_ai(client, content, prompt)
            return filename, result_text
    except Exception as e:
        return filename, f"分析失败: {str(e)}"


async def analyze_with_ai(client, content: str, prompt: str) -> str:
    """使用AI分析内容"""
    completion = client.chat.completions.create(
        model="qwen-long",
        messages=[{"role": "system", "content": content}, {"role": "user", "content": prompt or "分析下文件里的内容"}],
        stream=True,
    )
    return "".join(
        chunk.choices[0].delta.content for chunk in completion if chunk.choices and chunk.choices[0].delta.content
    )
