import json

import httpx
import httpx_sse
from loguru import logger
from t_ai_web import app

mcp_agent_dsl_request = (
    agent_dsl
) = r"""{
    "agentType": "MULTI",
    "t-ai-t_ai_agent": {
        "type": "MultiAgent",
        "key": "multi_agent",
        "name": "ERP系统顾问",
        "props": {
            "type": "MultiAgentProperties"
        },
        "children": [
            {
                "type": "SingleAgent",
                "key": "main_agent",
                "name": "ERP系统顾问",
                "props": {
                    "type": "SingleAgentProperties",
                    "providerType": "gpt",
                    "modelName": "gpt-4o",
                    "modelType": "text_to_text",
                    "systemPrompt": "你是ERP系统的主管顾问。你的工作是了解用户需求并将他们引导至合适的专业顾问。请根据以下明确的指引决定如何处理用户需求：\n    1. 产品销售定价类问题：\n       - 示例：\"分析并给出大米01到大米09的建议销售价格\"、\"分析并给出如下商品建议销售价格 物料：95号汽油 98号汽油 0号柴油\"\n       - 操作：请转交\"商品销售定价顾问\"\n    2. 一般问题：\n       - 示例：\"你是谁\"、\"你能做什么\"等\n       - 操作：直接回答客户\n    3. 其他问题：\n      - 除“销售定价”相关问题以外的ERP系统的相关问题，请答复客户:\"对应的专业顾问不在工位，我可以先帮您解决其他问题。\"\n    重要规则：\n    - 请严格按照上述分类选择适当的交接顾问\n    - 不要过度解读客户意图，根据客户明确表达的需求选择专业的顾问\n    - 如果不确定，先询问更多信息，而不是急于交接"
                }
            },
            {
                "type": "RefAgent",
                "key": "product_sale_price_agent",
                "name": "商品销售定价顾问",
                "props": {
                    "type": "RefAgentProperties",
                    "refAgentKey": "AI$product_sale_price_agent",
                    "refAgentName": "商品销售定价顾问",
                    "handoffDescription": "用于分析出合理的商品类目销售定价以及生成价格维护单，帮助用户解决商品销售定价相关的问题。"
                },
                "targetAgent": {
                    "type": "SingleAgent",
                    "key": "product_sale_price_agent",
                    "name": "商品销售定价顾问",
                    "props": {
                        "type": "SingleAgentProperties",
                        "providerType": "gpt",
                        "modelName": "gpt-4o",
                        "modelType": "text_to_text",
                        "systemPrompt": "你是ERP系统的商品销售定价顾问。您的工作是:\n    根据客户的问题，分析出合理的销售定价，并生成价格维护单，大致流程是：\n    1. 查询出商品物料类目的价格弹性系数\n    2. 查询出商品物料类目的采购价格\n    3. 根据类目的采购价格和弹性系数查询商品销售定价\n      - 当弹性系数>1时，表明是高弹性消费品，则查询商品动态需求定价\n      - 当弹性系数<1时，表明是低弹性消费品，则查询商品成本加成定价\n      - 当弹性系数=0时，表明是跟随市场的大宗商品，无弹性，则查询商品市场锚定定价\n    4. 根据物料ID和商品销售定价，生成价格维护单，\n      - 如果没有获取到商品销售定价，则提示客户即可，不要生成价格维护单\n      - 价格维护单成功生成后，把\"价格维护单详情\"返回给客户，详情链接:\"https://t-erp-portal-staging.app.terminus.io/TERP_PORTAL-TERP/TERP_PORTAL/TERP_PORTAL$N9oV1Y/page?_tab_id=9qgOnMBhs0oX&action=show&recordId={id}&sceneKey=ERP_GEN%24GEN_PRICE_ADJ_VIEW_NEW&viewKey=ERP_GEN%24GEN_PRICE_ADJ_VIEW_NEW%3Adetail\",详情链接中有{id}变量，请使用价格维护单id进行替换\n    请注意:如果有需要优先从tools或handoffs来获取相关信息，因为工具返回的结果往往是另一个工具的入参，也可能是返回结果\n\n    重要规则：\n    - 请严格按照上述分类选择适当的交接顾问\n    - 不要过度解读客户意图，根据客户明确表达的需求选择专业的顾问\n    - 如果不确定，先询问更多信息，而不是急于交接",
                        "skillTools": [
                            {
                                "type": "service",
                                "key": "AI$calculate_material_cost_markup_pricing",
                                "name": "计算物料成本加成定价",
                                "desc": "根据公式计算得出物料的成本加成定价",
                                "input": [
                                    {
                                        "fieldKey": "material_name",
                                        "fieldAlias": "material_name",
                                        "fieldName": "物料名称",
                                        "fieldType": "Text"
                                    },
                                    {
                                        "fieldKey": "purchase_price",
                                        "fieldAlias": "purchase_price",
                                        "fieldName": "物料默认采购价格",
                                        "fieldType": "Text"
                                    }
                                ]
                            },
                            {
                                "type": "service",
                                "key": "AI$get_material_category_price_elasticity",
                                "name": "获取物料类目价格弹性系数",
                                "desc": "根据物料名称获取物料类目价格弹性系数",
                                "input": [
                                    {
                                        "fieldKey": "material_name",
                                        "fieldAlias": "material_name",
                                        "fieldName": "material_name",
                                        "fieldType": "Text"
                                    }
                                ]
                            },
                            {
                                "type": "service",
                                "key": "AI$get_default_purchase_price_for_material",
                                "name": "获取物料默认采购价格",
                                "desc": "根据物料名称获取物料默认采购价格",
                                "input": [
                                    {
                                        "fieldKey": "material_name",
                                        "fieldAlias": "material_name",
                                        "fieldName": "material_name",
                                        "fieldType": "Text"
                                    }
                                ]
                            },
                            {
                                "type": "service",
                                "key": "AI$material_price_maintenance_create_service",
                                "name": "生成价格维护单",
                                "desc": "根据物料id和销售定价生成价格维护单",
                                "input": [
                                    {
                                        "fieldKey": "request",
                                        "fieldAlias": "request",
                                        "fieldName": "物料价格信息",
                                        "fieldType": "Array",
                                        "required": true,
                                        "element": {
                                            "fieldKey": "element",
                                            "fieldAlias": "element",
                                            "fieldName": "element",
                                            "fieldType": "Object",
                                            "elements": [
                                                {
                                                    "fieldKey": "matId",
                                                    "fieldAlias": "matId",
                                                    "fieldName": "物料id",
                                                    "fieldType": "Number"
                                                },
                                                {
                                                    "fieldKey": "price",
                                                    "fieldAlias": "price",
                                                    "fieldName": "价格",
                                                    "fieldType": "Number"
                                                },
                                                {
                                                    "fieldKey": "unitId",
                                                    "fieldAlias": "unitId",
                                                    "fieldName": "计量单位id",
                                                    "fieldType": "Text"
                                                }
                                            ]
                                        }
                                    }
                                ]
                            },
                            {
                                "type": "mcp_tools",
                                "name": "search-tools",
                                "desc": "获取各种价格信息,比如油价,米价等",
                                "extendProperties": {
                                    "modelName": "gpt-4o",
                                    "mcpServerVersion": "0.0.1",
                                    "instructions": "你有一组工具，你需要根据提示信息和各个工具的说明，调用最相关的工具并返回对应结果,不需要额外的信息。在调用工具时 如果某个工具要求的参数 用户没有提供 请使用工具说明中提供的默认值"
                                },
                                "tools": [
                                    {
                                    "type": "mcp_tools",
                                    "key": "search-oil-price",
                                    "name": "search-oil-price",
                                    "desc": "Give me a parameter of `gasoline-type` to get the price of gasoline in the province，province must use default",
                                    "input": [
                                        {
                                        "fieldKey": "gasoline-type",
                                        "fieldAlias": "gasoline-type",
                                        "fieldName": "gasoline-type",
                                        "fieldType": "Text",
                                        "description": "Type of gasoline, e.g., 95, 98",
                                        "required":true
                                        },
                                        {
                                        "fieldKey": "province",
                                        "fieldAlias": "province",
                                        "fieldName": "province",
                                        "fieldType": "Text",
                                        "description": "The province to search for",
                                        "defaultValue": "江苏",
                                        "required":true
                                        }
                                    ]
                                    }
                                ]
                                },{
  "type": "mcp_tools",
  "name": "html-generator",
  "modelName": "gpt-4o",
  "desc": "根据用户提供的信息生成HTML网页",
  "extendProperties": {
    "mcpServerVersion": "2.0.1",
    "instructions": "你是一个html生成器，你需要根据用户的要求生成html，请按照以下步骤: \n1. 使用init_html初始化页面；\n2. 使用set_title设置页面标题；\n3. 根据用户的要求调用若干次create_echarts_element；\n4. 根据用户的要求调用若干次create_text_element；\n5. 根据用户的要求调用若干次create_table_element；\n6. 执行build_final_html生成最终的html链接"
  },
  "tools": [
    {
      "type": "mcp_tools",
      "key": "build_final_html",
      "name": "build_final_html",
      "desc": "在确定元素都已添加完成后，生成最终的HTML",
      "input": [
        {
          "fieldKey": "id",
          "fieldAlias": "id",
          "fieldName": "id",
          "fieldType": "Text",
          "description": "HTML文件id, id是由`init_html`返回的",
          "required": true
        }
      ]
    },
    {
      "type": "mcp_tools",
      "key": "create_echarts_element",
      "name": "create_echarts_element",
      "desc": "根据数据生成交互式ECharts图表，支持折线图、柱状图、饼图等多种图表类型，并自动配置响应式布局和主题样式",
      "input": [
        {
          "fieldKey": "id",
          "fieldAlias": "id",
          "fieldName": "id",
          "fieldType": "Text",
          "description": "HTML文件id, id是由`init_html`返回的",
          "required": true
        },
        {
          "fieldKey": "x_axis_type",
          "fieldAlias": "x_axis_type",
          "fieldName": "X轴类型",
          "fieldType": "Text",
          "description": "横坐标数据字段，用于显示X轴的数据维度",
          "required": true
        },
        {
          "fieldKey": "y_axis_type",
          "fieldAlias": "y_axis_type",
          "fieldName": "Y轴类型",
          "fieldType": "Text",
          "description": "纵坐标数据字段，用于显示Y轴的数据维度",
          "required": true
        },
        {
          "fieldKey": "series_type",
          "fieldAlias": "series_type",
          "fieldName": "数值字段",
          "fieldType": "Text",
          "description": "数值字段，用于显示图表中的具体数值",
          "required": true
        },
        {
          "fieldKey": "data_values",
          "fieldAlias": "data_values",
          "fieldName": "图表数据",
          "fieldType": "JSON",
          "description": "图表数据，需要提供JSON格式的数据数组，包含x_axis_type、y_axis_type和series_type对应的字段",
          "required": true
        },
        {
          "fieldKey": "chart_type",
          "fieldAlias": "chart_type",
          "fieldName": "图表类型",
          "fieldType": "Text",
          "description": "图表类型，支持line(折线图)、bar(柱状图)、pie(饼图)等，默认为折线图",
          "required": false
        },
        {
          "fieldKey": "other_option",
          "fieldAlias": "other_option",
          "fieldName": "额外配置",
          "fieldType": "Text",
          "description": "额外的图表配置要求，如：添加数据筛选器、设置特定颜色主题、添加数据标签等",
          "required": false
        }
      ]
    },
    {
      "type": "mcp_tools",
      "key": "create_text_element",
      "name": "create_text_element",
      "desc": "将用户提供的文本数据分析总结并转换为的HTML内容，支持标题、段落、列表、代码块等多种HTML元素，并自动应用美观的CSS样式",
      "input": [
        {
          "fieldKey": "id",
          "fieldAlias": "id",
          "fieldName": "id",
          "fieldType": "Text",
          "description": "HTML文件id, id是由`init_html`返回的",
          "required": true
        },
        {
          "fieldKey": "data",
          "fieldAlias": "data",
          "fieldName": "原始文本内容",
          "fieldType": "Text",
          "description": "需要转换为HTML的原始文本内容，可以是纯文本、Markdown格式或结构化的数据",
          "required": true
        }
      ]
    },
    {
      "type": "mcp_tools",
      "key": "init_html",
      "name": "init_html",
      "desc": "初始化HTML文件，并返回文件id",
      "input": []
    },
    {
      "type": "mcp_tools",
      "key": "set_title",
      "name": "set_title",
      "desc": "设置HTML标题, 成功后返回文件id",
      "input": [
        {
          "fieldKey": "id",
          "fieldAlias": "id",
          "fieldName": "id",
          "fieldType": "Text",
          "description": "HTML文件id, id是由`init_html`返回的",
          "required": true
        },
        {
          "fieldKey": "title",
          "fieldAlias": "title",
          "fieldName": "HTML标题",
          "fieldType": "Text",
          "description": "要设置的HTML标题",
          "required": true
        }
      ]
    }
  ]
}
                        ],
                        "handoffDescription": "用于分析出合理的商品类目销售定价以及生成价格维护单，帮助用户解决商品销售定价相关的问题。"
                    }
                }
            },
            {
                "type": "SingleAgent",
                "key": "else",
                "name": "闲聊顾问",
                "props": {
                    "type": "SingleAgentProperties",
                    "providerType": "gpt",
                    "modelName": "gpt-4o-mini",
                    "modelType": "text_to_text",
                    "systemPrompt": "你的职责回答一些和ERP系统无关的问题，示例：\"你是谁\"、\"你能做什么\"等\n       - 操作：直接回答客户",
                    "handoffDescription": "回答一些和ERP系统无关的问题"
                }
            }
        ],
        "handoffRefs": [
            {
                "fromRef": "main_agent",
                "toRef": "product_sale_price_agent"
            },
            {
                "fromRef": "main_agent",
                "toRef": "else"
            }
        ]
    },
    "arguments": {
        "attributes": {
            "serviceKey": "AI$erp_main_agent",
            "moduleKey": "AI"
        },
        "teamId": 1,
        "payload": {
            "userContent": "我想分析并给出95号汽油的销售价格,但是先不生成销售价格维护单,先查询下它的浙江省，江苏省的外部市场价格，并调用工具生成可视化的交互兼具文字说明和图标的html页面"
        }
    }
}
"""

headers = {
    "Content-Type": "application/json",
    "T-AI-CALLBACK": "https://t-erp-portal-staging.app.terminus.io",
    "T-AI-SOURCE-COOKIE": """taid=b4ee6552-5467-40cf-94b6-dd691a2d9191; emp_cookie=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMzIzZjcyNDEzZGQ0NDM3OTg0YjIwZWFhNjg2NGM2YjQiLCJleHBpcmUiOjEyMDk2MDAsInBhdGgiOiIvIiwiZG9tYWluIjoidGVybWludXMuaW8iLCJodHRwT25seSI6dHJ1ZSwic2VjdXJlIjp0cnVlLCJpc3MiOiJpYW0oMi41LjI0LjExMzAuMC1TTkFQU0hPVCkiLCJzdWIiOiJpYW0gdXNlciIsImV4cCI6MTc0NjMzNzg1NywibmJmIjoxNzQ1MTI4MjU3LCJpYXQiOjE3NDUxMjgyNTcsImp0aSI6ImRiYTQ3ZDAyMWVmNjQ1ZDZhNTlmNWQxY2M3NThkNTAwIn0.WXSkQ29HwZH0P0C_1Jc3tjVZvJysxIe21HWYGDwVVF4; trantor_v2_lng=zh-CN; t_iam_staging=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiNWUwMTRhZDYzYzc4NGIyN2JhNTdkOWUyYjViMmFiZGEiLCJleHBpcmUiOjI1OTIwMCwicGF0aCI6Ii8iLCJkb21haW4iOiJ0ZXJtaW51cy5pbyIsImh0dHBPbmx5Ijp0cnVlLCJzZWN1cmUiOmZhbHNlLCJpc3MiOiJpYW0oMi41LjI1LjAxMzAuMC1TTkFQU0hPVCkiLCJzdWIiOiJpYW0gdXNlciIsImV4cCI6MTc0NjI1MjY2NCwibmJmIjoxNzQ1OTkzNDY0LCJpYXQiOjE3NDU5OTM0NjQsImp0aSI6IjJjYzc5ZGQ0OWFlMDQwNzlhZTkyMDAyNjhkYWYyZjdhIn0.UudOWy7xAvqwXRmEF0x1ZyW_tE2gokA9elVS8rJIFUQ; Trantor2-ORIGIN-ORG-ID=""",
    "T-AI-SOURCE-REFERER": """https://t-erp-portal-staging.app.terminus.io/TERP_PORTAL-TERP/TERP_PORTAL/TERP_PORTAL$Y6OAt5/page?_tab_id=Ii29W3KQeiER&sceneKey=AI%24ai_price_elasticity_md_scene&viewKey=AI%24ai_price_elasticity_md_scene%3Alist""",
}


async def test_simple_agent():
    async with httpx.AsyncClient(
        transport=httpx.ASGITransport(app=app),
        base_url="http://test",
        headers=headers,
    ) as sse_client:
        async with httpx_sse.aconnect_sse(
            sse_client,
            "POST",
            "/api/ai/agent/run_streamed",
            json=json.loads(mcp_agent_dsl_request),
        ) as event_source:
            context = ""
            async for sse in event_source.aiter_sse():
                if sse.data:
                    # logger.info(f"sse.data: {sse.data}")
                    context += sse.data
            logger.info(f"context: {context}")
            assert context.find("") != -1
