[project]
name = "t-ai-agent"
version = "0.1.0"
description = "t-ai-agent package 主要是agent相关的应用"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    #internal
    "t-ai-common",
    "t-ai-app",
    "t-ai-mcp",
    "t-ai-knowledge-base",

    #external
    # "mcp[cli]==1.9.0",
    "sse-starlette>=2.3.5",
    "openai-agents>=0.2.5",
    "loguru>=0.7.3",
    "httpx>=0.28.1",
    "httpx_sse>=0.4.1",
    "openai>=1.96.1",
    "pydantic>=2.11.4",
    "requests>=2.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_agent"]
