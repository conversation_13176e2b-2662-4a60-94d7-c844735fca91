"""
测试对话历史功能
"""

import json

import pytest
from t_ai_agent.model.agent_context import AgentExecutionContext
from t_ai_agent.utils.conversation_helper import ConversationHelper


def test_conversation_history_basic():
    """测试基本的对话历史功能"""
    # 创建测试上下文
    context = AgentExecutionContext()

    # 模拟对话历史数据
    test_input_items = [
        {"role": "user", "content": json.dumps([{"type": "text", "text": "你好，我想了解AI技术"}])},
        {"role": "assistant", "content": json.dumps({"type": "text", "text": "您好！我很乐意为您介绍AI技术。"})},
        {"role": "user", "content": json.dumps([{"type": "text", "text": "请详细说明机器学习的原理"}])},
    ]

    # 设置对话历史
    context.set_conversation_history(test_input_items)

    # 测试获取对话历史
    history = context.get_conversation_history()
    assert len(history) == 3
    assert history[0]["role"] == "user"
    assert history[1]["role"] == "assistant"
    assert history[2]["role"] == "user"


def test_conversation_helper_user_messages():
    """测试获取用户消息"""
    context = AgentExecutionContext()

    test_input_items = [
        {"role": "user", "content": json.dumps([{"type": "text", "text": "第一条用户消息"}])},
        {"role": "assistant", "content": json.dumps({"type": "text", "text": "助手回复"})},
        {"role": "user", "content": json.dumps([{"type": "text", "text": "第二条用户消息"}])},
    ]

    context.set_conversation_history(test_input_items)

    # 测试获取用户消息
    user_messages = ConversationHelper.get_user_messages(context)
    assert len(user_messages) == 2
    assert user_messages[0] == "第一条用户消息"
    assert user_messages[1] == "第二条用户消息"

    # 测试获取最后一条用户消息
    last_message = ConversationHelper.get_last_user_message(context)
    assert last_message == "第二条用户消息"


def test_conversation_helper_assistant_messages():
    """测试获取助手消息"""
    context = AgentExecutionContext()

    test_input_items = [
        {"role": "user", "content": json.dumps([{"type": "text", "text": "用户消息"}])},
        {"role": "assistant", "content": json.dumps({"type": "text", "text": "第一条助手回复"})},
        {"role": "assistant", "content": json.dumps({"type": "text", "text": "第二条助手回复"})},
    ]

    context.set_conversation_history(test_input_items)

    # 测试获取助手消息
    assistant_messages = ConversationHelper.get_assistant_messages(context)
    assert len(assistant_messages) == 2
    assert assistant_messages[0] == "第一条助手回复"
    assert assistant_messages[1] == "第二条助手回复"


def test_conversation_helper_role_counts():
    """测试角色消息统计"""
    context = AgentExecutionContext()

    test_input_items = [
        {"role": "user", "content": "用户消息1"},
        {"role": "assistant", "content": "助手回复1"},
        {"role": "user", "content": "用户消息2"},
        {"role": "user", "content": "用户消息3"},
        {"role": "system", "content": "系统消息"},
    ]

    context.set_conversation_history(test_input_items)

    # 测试角色统计
    role_counts = ConversationHelper.count_messages_by_role(context)
    assert role_counts["user"] == 3
    assert role_counts["assistant"] == 1
    assert role_counts["system"] == 1


def test_conversation_helper_summary():
    """测试对话摘要"""
    context = AgentExecutionContext()

    test_input_items = [
        {
            "role": "user",
            "content": json.dumps(
                [
                    {
                        "type": "text",
                        "text": "这是一条很长的用户消息，用来测试摘要功能是否能够正确截断长文本内容，确保摘要的可读性和简洁性",
                    }
                ]
            ),
        },
        {"role": "assistant", "content": json.dumps({"type": "text", "text": "简短回复"})},
    ]

    context.set_conversation_history(test_input_items)

    # 测试对话摘要
    summary = ConversationHelper.get_conversation_summary(context, max_messages=5)
    assert "user:" in summary
    assert "assistant:" in summary
    assert "..." in summary  # 长文本应该被截断


def test_empty_conversation_history():
    """测试空对话历史"""
    context = AgentExecutionContext()

    # 测试空历史
    history = context.get_conversation_history()
    assert len(history) == 0

    user_messages = ConversationHelper.get_user_messages(context)
    assert len(user_messages) == 0

    last_message = ConversationHelper.get_last_user_message(context)
    assert last_message is None

    role_counts = ConversationHelper.count_messages_by_role(context)
    assert len(role_counts) == 0


def test_malformed_content():
    """测试格式错误的内容"""
    context = AgentExecutionContext()

    test_input_items = [
        {"role": "user", "content": "非JSON格式的内容"},
        {"role": "assistant", "content": "另一条非JSON内容"},
    ]

    context.set_conversation_history(test_input_items)

    # 应该能够处理非JSON格式的内容
    user_messages = ConversationHelper.get_user_messages(context)
    assert len(user_messages) == 1
    assert user_messages[0] == "非JSON格式的内容"

    assistant_messages = ConversationHelper.get_assistant_messages(context)
    assert len(assistant_messages) == 1
    assert assistant_messages[0] == "另一条非JSON内容"


def test_full_conversation_history():
    """测试完整对话历史功能"""
    context = AgentExecutionContext()

    # 设置input_items历史
    input_items = [{"role": "user", "content": json.dumps([{"type": "text", "text": "用户输入消息"}])}]
    context.set_conversation_history(input_items)

    # 模拟Runner上下文（完整对话历史）
    class MockMessage:
        def __init__(self, role, content):
            self.role = role
            self.content = content

    class MockRunnerContext:
        def __init__(self):
            self.messages = [
                MockMessage("user", "用户输入消息"),
                MockMessage("assistant", "助手回复1"),
                MockMessage("user", "用户新消息"),
                MockMessage("assistant", "助手回复2"),
            ]

    mock_runner_context = MockRunnerContext()
    context.set_runner_context(mock_runner_context)

    # 测试获取完整对话历史
    full_history = context.get_full_conversation_history()
    assert len(full_history) == 4
    assert full_history[0]["role"] == "user"
    assert full_history[1]["role"] == "assistant"
    assert full_history[2]["role"] == "user"
    assert full_history[3]["role"] == "assistant"

    # 测试input_items历史仍然正常
    input_history = context.get_conversation_history()
    assert len(input_history) == 1


def test_conversation_helper_with_full_history():
    """测试ConversationHelper使用完整历史的功能"""
    context = AgentExecutionContext()

    # 设置input_items历史
    input_items = [{"role": "user", "content": json.dumps([{"type": "text", "text": "历史用户消息"}])}]
    context.set_conversation_history(input_items)

    # 模拟完整对话历史
    class MockMessage:
        def __init__(self, role, content):
            self.role = role
            self.content = content

    class MockRunnerContext:
        def __init__(self):
            self.messages = [
                MockMessage("user", "历史用户消息"),
                MockMessage("assistant", "助手回复"),
                MockMessage("user", "新用户消息"),
            ]

    context.set_runner_context(MockRunnerContext())

    # 测试使用input_items历史
    user_messages_input = ConversationHelper.get_user_messages(context, use_full_history=False)
    assert len(user_messages_input) == 1
    assert user_messages_input[0] == "历史用户消息"

    # 测试使用完整历史
    user_messages_full = ConversationHelper.get_user_messages(context, use_full_history=True)
    assert len(user_messages_full) == 2
    assert user_messages_full[0] == "历史用户消息"
    assert user_messages_full[1] == "新用户消息"

    # 测试助手消息
    assistant_messages_input = ConversationHelper.get_assistant_messages(context, use_full_history=False)
    assert len(assistant_messages_input) == 0  # input_items中没有助手消息

    assistant_messages_full = ConversationHelper.get_assistant_messages(context, use_full_history=True)
    assert len(assistant_messages_full) == 1
    assert assistant_messages_full[0] == "助手回复"


def test_fallback_to_input_items():
    """测试当无法获取完整历史时回退到input_items"""
    context = AgentExecutionContext()

    # 只设置input_items历史，不设置runner上下文
    input_items = [{"role": "user", "content": json.dumps([{"type": "text", "text": "用户消息"}])}]
    context.set_conversation_history(input_items)

    # 测试完整历史回退到input_items
    full_history = context.get_full_conversation_history()
    input_history = context.get_conversation_history()

    assert len(full_history) == len(input_history)
    assert full_history == input_history


if __name__ == "__main__":
    pytest.main([__file__])
