from t_ai_agent.mcp_agent.mcp_client_based_agent import MCPClientBasedAgent
from t_ai_app import G


async def test_create_and_as_tool():
    agent = await MCPClientBasedAgent(
        mcp_server_key="search-tools",
        mcp_server_name="search-tools",
        model="gpt-4o-mini",
        ai_proxy_admin_client=G.AI_PROXY_ADMIN_CLIENT,
        choiced_tools=[],
    )
    assert agent is not None
    assert agent.model is not None
