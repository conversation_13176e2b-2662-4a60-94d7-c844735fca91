from loguru import logger
from t_ai_agent.mcp_agent.agent_compatible_mcp_client import AgentCompatibleMCPClient
from t_ai_app import G


async def get_mcp_server_info():
    return await G.AI_PROXY_ADMIN_CLIENT.get_mcp_server_info(mcp_server_name="search-tools", mcp_server_version="0.0.1")


async def test_basic_functions():
    mcp_server_info = await get_mcp_server_info()
    assert mcp_server_info is not None
    agent_compatible_mcp_client: AgentCompatibleMCPClient = AgentCompatibleMCPClient(
        endpoint=mcp_server_info.endpoint,
        tools=mcp_server_info.tools,
    )
    assert agent_compatible_mcp_client is not None
    agent_tools = agent_compatible_mcp_client.get_openai_agent_tools()
    assert len(agent_tools) > 0


async def test_call_tool():
    mcp_server_info = await get_mcp_server_info()
    assert mcp_server_info is not None
    agent_compatible_mcp_client: AgentCompatibleMCPClient = AgentCompatibleMCPClient(
        endpoint=mcp_server_info.endpoint,
        tools=mcp_server_info.tools,
    )
    assert agent_compatible_mcp_client is not None
    result = await agent_compatible_mcp_client.call_tool("search-oil-price", {"province": "江苏"})
    logger.info(f"result: {result}")
    assert result is not None
