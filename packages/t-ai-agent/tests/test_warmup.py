#!/usr/bin/env python3
"""
Agent预热功能测试脚本

使用方法:
python test_warmup.py [--test-data] [--specific-agents agent1,agent2]
"""

import argparse
import asyncio
import json
import sys
from typing import List
from unittest.mock import patch

from src.t_ai_agent.agent_warmup import (
    AgentWarmupService,
    WarmupConfig,
    get_warmup_config,
    startup_agent_warmup,
    warmup_specific_agents,
)

# 测试数据
TEST_AGENT_DATA = [
    {
        "type": "Agent",
        "key": "test_agent_1",
        "name": "测试Agent 1",
        "props": {
            "model": {
                "modelPublisher": "Azure",
                "name": "gpt-4.1",
                "setting": {
                    "temperature": 0.0,
                    "topP": 1,
                    "maxTokens": 4096,
                    "frequencyPenalty": 0.0,
                    "presencePenalty": 0.0,
                },
            },
            "systemPrompt": "你是一个测试助手",
            "skillTools": [],
            "avatar": None,
        },
    },
    {
        "type": "Agent",
        "key": "test_agent_2",
        "name": "测试Agent 2",
        "props": {
            "model": {
                "modelPublisher": "OpenAI",
                "name": "gpt-4o-mini",
                "setting": {
                    "temperature": 0.5,
                    "topP": 1,
                    "maxTokens": 2048,
                    "frequencyPenalty": 0.0,
                    "presencePenalty": 0.0,
                },
            },
            "systemPrompt": "你是另一个测试助手",
            "skillTools": [],
            "avatar": None,
        },
    },
    {
        "type": "Agent",
        "key": "test_agent_3",
        "name": "测试Agent 3",
        "props": {
            "model": {
                "modelPublisher": "Azure",
                "name": "gpt-4.1",
                "setting": {
                    "temperature": 0.2,
                    "topP": 0.9,
                    "maxTokens": 1024,
                    "frequencyPenalty": 0.1,
                    "presencePenalty": 0.1,
                },
            },
            "systemPrompt": "你是第三个测试助手",
            "skillTools": [],
            "avatar": None,
        },
    },
]


class MockHTTPXResponse:
    """模拟httpx响应"""

    def __init__(self, data, status_code=200):
        self._data = data
        self.status_code = status_code

    def json(self):
        return self._data

    def raise_for_status(self):
        if self.status_code >= 400:
            raise Exception(f"HTTP {self.status_code}")


class MockHTTPXClient:
    """模拟httpx客户端"""

    def __init__(self, test_data=None):
        self.test_data = test_data or TEST_AGENT_DATA

    async def get(self, url, **kwargs):
        print(f"Mock GET request to: {url}")

        if url.endswith("/metadata"):
            # 获取所有agent
            return MockHTTPXResponse({"data": self.test_data})
        elif "/metadata/" in url:
            # 获取单个agent
            agent_key = url.split("/metadata/")[-1]
            agent = next((a for a in self.test_data if a["key"] == agent_key), None)
            if agent:
                return MockHTTPXResponse({"data": agent})
            else:
                return MockHTTPXResponse({"error": "Not found"}, 404)
        else:
            return MockHTTPXResponse({"error": "Unknown endpoint"}, 404)

    async def aclose(self):
        pass


async def test_full_warmup(use_test_data=False):
    """测试完整预热流程"""
    print("=== 测试完整预热流程 ===")

    if use_test_data:
        # 使用模拟数据
        with patch("httpx.AsyncClient", return_value=MockHTTPXClient()):
            stats = await startup_agent_warmup()
            print(f"预热统计: {stats}")
    else:
        # 使用真实API（需要配置有效的URL）
        stats = await startup_agent_warmup()
        print(f"预热统计: {stats}")


async def test_specific_agents(agent_keys: List[str], use_test_data=False):
    """测试特定Agent预热"""
    print(f"=== 测试特定Agent预热: {agent_keys} ===")

    if use_test_data:
        # 使用模拟数据
        with patch("httpx.AsyncClient", return_value=MockHTTPXClient()):
            await warmup_specific_agents(agent_keys)
    else:
        # 使用真实API
        await warmup_specific_agents(agent_keys)


async def test_service_directly(use_test_data=False):
    """直接测试服务类"""
    print("=== 直接测试AgentWarmupService ===")

    warmup_config = WarmupConfig(
        agent_metadata_url="http://test-api/agents/metadata",
        agent_metadata_headers={"Content-Type": "application/json"},
        agent_metadata_timeout=10,
        warmup_enabled=True,
        warmup_batch_size=2,
        warmup_delay=0.1,
        warmup_retry_count=1,
        warmup_retry_delay=0.5,
        log_level="DEBUG",
    )

    if use_test_data:
        # 使用模拟数据
        with patch("httpx.AsyncClient", return_value=MockHTTPXClient()):
            service = AgentWarmupService(warmup_config)

            # 测试预热所有Agent
            stats = await service.warmup_all_agents()
            print(f"所有Agent预热统计: {stats}")

            # 测试预热单个Agent
            result = await service.warmup_agent_by_key("test_agent_1")
            print(f"单个Agent预热结果: {result}")
    else:
        service = AgentWarmupService(warmup_config)

        # 测试预热所有Agent
        stats = await service.warmup_all_agents()
        print(f"所有Agent预热统计: {stats}")


async def test_error_scenarios():
    """测试错误场景"""
    print("=== 测试错误场景 ===")

    # 测试连接超时
    print("1. 测试连接超时")
    from t_ai_app.settings import AgentWarmupSettings

    config = AgentWarmupSettings()

    try:
        await startup_agent_warmup(config)
    except Exception as e:
        print(f"预期的连接错误: {e}")

    # 测试禁用预热
    print("2. 测试禁用预热")
    from t_ai_app.settings import AgentWarmupSettings

    disabled_config = AgentWarmupSettings()

    stats = await startup_agent_warmup(disabled_config)
    print(f"禁用预热的统计: {stats}")


def print_test_data():
    """打印测试数据"""
    print("=== 测试数据 ===")
    print(json.dumps(TEST_AGENT_DATA, indent=2, ensure_ascii=False))


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Agent预热功能测试")
    parser.add_argument("--test-data", action="store_true", help="使用模拟测试数据而不是真实API")
    parser.add_argument("--specific-agents", type=str, help="测试特定Agent，用逗号分隔，例如: agent1,agent2")
    parser.add_argument("--print-data", action="store_true", help="打印测试数据")
    parser.add_argument("--error-test", action="store_true", help="测试错误场景")

    args = parser.parse_args()

    if args.print_data:
        print_test_data()
        return

    print("开始Agent预热功能测试...")
    print(f"使用测试数据: {args.test_data}")

    try:
        if args.error_test:
            await test_error_scenarios()
        elif args.specific_agents:
            agent_keys = [key.strip() for key in args.specific_agents.split(",")]
            await test_specific_agents(agent_keys, args.test_data)
        else:
            # 运行所有测试
            await test_full_warmup(args.test_data)
            await test_service_directly(args.test_data)

            # 测试特定Agent
            await test_specific_agents(["test_agent_1", "test_agent_2"], args.test_data)

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)

    print("测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
