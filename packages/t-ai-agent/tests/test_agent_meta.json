{"type": "Agent", "id": null, "key": "ERP_SCM$test_main_helper", "name": "企业AI顾问", "desc": null, "props": {"type": "AgentProperties", "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "greetings": "iy你好，我是你的万能助手，请问我有什么可以帮助你的吗？", "userQuestions": ["销售价格是什么", "什么是ERP系统"], "systemPrompt": "你是一个主控租售，有很强的沟通引导能力，你的工作是深入了解用户需求，并依据需求将他们引导至合适的专业助手，为用户提供精准、高效的服务\n 比如：统计分析下大米01、大米02的销售价格合理性,毛利回报率趋势，以及查询链接等，引导至‘数据统计分析助手’,\n 其他问题则引导'测试版万能助手'处", "skillTools": []}, "handoffs": [{"id": null, "agentKey": "test_multi_session", "handoffDescription": "适用于从网络上查询各种信息，帮助用户解答各种问题"}, {"id": null, "agentKey": "data_analysis_hepler", "handoffDescription": "适用于据数据统计分析，帮助用户进行数据统计分析"}, {"id": null, "agentKey": "html_generate_hepler", "handoffDescription": "用于生成html页面"}], "children": [{"type": "ReferenceAgent", "id": null, "key": "test_multi_session", "name": "测试版万能助手", "desc": null, "props": {"type": "ReferenceAgentProperties", "refAgentKey": "ERP_SCM$test_multi_session", "refAgentName": "测试版万能助手"}, "targetAgent": {"type": "Agent", "id": null, "key": "ERP_SCM$test_multi_session", "name": "测试多轮回话", "desc": null, "props": {"type": "AgentProperties", "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "greetings": "你好，我是你的万能助手，请问我有什么可以帮助你的吗？", "userQuestions": ["销售价格是什么", "什么是ERP系统"], "systemPrompt": "你是一个万能助手，可以帮助用户解答你所理解的问题。比如：统计分析下大米01、大米02的销售价格合理性。", "skillTools": [{"key": "ai_app_build$save_service_dsl", "name": "保存编排服务DSL", "desc": "保存编排服务DSL", "input": null, "type": "service", "visible": true, "tools": null, "related_models": null}, {"id": null, "key": "html-generator", "name": "html-generator", "desc": "html-generator 是一个简单的html生成工具，可以帮助用户生成图表等", "input": null, "extendProperties": {"instructions": "你有一组工具，你需要根据提示信息和各个工具的说明，调用最相关的工具并返回对应结果,不要输出任何额外的信息", "mcpServerEndpoint": "https://html-generator-v2-a4b9f776.daily.terminus.io/sse", "mcpServerVersion": "2.0.0", "modelName": "gpt-4.1", "modelProvider": "Azure", "modelProviderType": "Azure"}, "type": "mcp", "visible": true, "tools": [{"id": null, "key": "create_html", "name": "create_html", "desc": "根据提供的数据生成HTML文件，支持多种HTML元素，支持图表展示", "input": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Text", "description": "需要依赖的数据，以及要求说明", "required": true}], "extendProperties": null}]}, {"id": null, "key": "search-engine", "name": "search-engine", "desc": "search-engine 是一个为LLM提供联网搜索的工具", "input": null, "extendProperties": {"instructions": "你有一组工具，你需要根据提示信息和各个工具的说明，调用最相关的工具并返回对应结果,不要输出任何额外的信息", "mcpServerEndpoint": "https://search-engine-7f027242.daily.terminus.io/sse", "mcpServerVersion": "1.0.0", "modelName": "gpt-4.1", "modelProvider": "Azure", "modelProviderType": "Azure"}, "type": "mcp", "visible": true, "tools": [{"id": null, "key": "search", "name": "search", "desc": "search information from internet", "input": [{"id": null, "fieldKey": "need_fetch", "fieldAlias": "need_fetch", "fieldName": "need_fetch", "fieldType": "Boolean", "description": "need fetch the urls to summarize the information detail", "required": false}, {"id": null, "fieldKey": "query", "fieldAlias": "query", "fieldName": "query", "fieldType": "Text", "description": "query information from internet", "required": true}], "extendProperties": null}]}]}}}, {"type": "Agent", "id": null, "key": "data_analysis_hepler", "name": "数据统计分析助手", "desc": null, "props": {"type": "AgentProperties", "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "greetings": "", "userQuestions": [], "systemPrompt": "你是一个数据统计分析助手，能够精准分析用户需求，能够从工具中查询出统计数据，并且严格按照统一的格式进行输出。\n统一的输出格式是：<TRANTOR_Chart>JSON_DATA</TRANTOR_Chart>\n示列如下：\n<TRANTOR_Chart>\n{\n  xAxis: {\n    type: 'category',\n    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n  },\n  yAxis: {\n    type: 'value'\n  },\n  series: [\n    {\n      data: [150, 230, 224, 218, 135, 147, 260],\n      type: 'line'\n    }\n  ]\n}\n</TRANTOR_Chart>\n目前支持的图表有：\n- bar: 柱状图\n- line: 折线图\n- pie: 饼图\n- scatter: 散点图\n- funnel: 漏斗图\n- radar: 雷达图\n 所有的图表输出后，都需要在输出详情链接如: [查看物料详情](https://t-project-portal-dev.app.terminus.io/TERP_PORTAL-PJ0724/TERP_PORTAL/TERP_PORTAL$4a0eb711-d4fe-4365-b81c-4d3f138d2130/page?_tab_id=DeU2OO2mNoJQ&recordId=【物料详情ID】&sceneKey=ERP_SCM%24GEN_MAT_NEW_VIEW&viewKey=ERP_SCM%24GEN_MAT_NEW_VIEW%3ANAelHwsbofCYIspS3L-As)\n注意：链接中的recordId参数需要替换，替换的值需要通过工具进行查询\n 如果您认为用户的需求还没有完成，且您也无法处理，请转交您的下级agent或者转交给您的上级agent", "skillTools": [{"id": null, "key": "ERP_SCM$material_price_bi", "name": "查询各种物料价格合理性统计分析数据", "desc": "查询各种物料价格合理性统计分析数据", "input": [{"id": null, "fieldKey": "<PERSON><PERSON><PERSON>", "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "物料名称", "fieldType": "Text"}], "extendProperties": null, "type": "service", "visible": false, "tools": null}, {"id": null, "key": "ERP_SCM$material_gross_profit_return_trend", "name": "查询各种物料毛利回报率趋势统计分析数据", "desc": "查询各种物料毛利回报率趋势统计分析数据", "input": [{"id": null, "fieldKey": "<PERSON><PERSON><PERSON>", "fieldAlias": "<PERSON><PERSON><PERSON>", "fieldName": "物料名称", "fieldType": "Text"}], "extendProperties": null, "type": "service", "visible": false, "tools": null}, {"id": null, "key": "ERP_SCM$query_material_details", "name": "根据物料名称查询物料详情", "desc": "根据物料名称查询物料详情", "input": [{"id": null, "fieldKey": "material_name", "fieldAlias": "material_name", "fieldName": "物料名称", "fieldType": "Text"}], "extendProperties": null, "type": "service", "visible": true, "tools": null}]}, "handoffs": [{"id": null, "agentKey": "ERP_SCM$test_main_helper", "handoffDescription": "当用户指明需要返回主agent时，进行交接"}]}, {"type": "Agent", "id": null, "key": "html_generate_hepler", "name": "html生成助手", "desc": null, "props": {"type": "AgentProperties", "model": {"providerType": "Azure", "name": "gpt-4.1", "type": "text_to_text", "setting": {"frequencyPenalty": 0.0, "presencePenalty": 0.0, "temperature": 0.0, "topP": 1, "chatRounds": 10, "maxTokens": 4096}}, "greetings": "", "userQuestions": [], "systemPrompt": "你是一个网页html生成助手", "skillTools": [{"id": null, "key": "html-generator", "name": "html-generator", "desc": "html-generator 是一个简单的html生成工具，可以帮助用户生成图表等", "input": null, "extendProperties": {"instructions": "你有一组工具，你需要根据提示信息和各个工具的说明，调用最相关的工具并返回对应结果,不要输出任何额外的信息", "mcpServerEndpoint": "https://html-generator-dd1dfda9.daily.terminus.io/sse", "mcpServerVersion": "1.0.0"}, "type": "mcp", "visible": true, "tools": [{"id": null, "key": "create_html", "name": "create_html", "desc": "根据提供的数据生成HTML文件，支持多种HTML元素，支持图表展示", "input": [{"id": null, "fieldKey": "data", "fieldAlias": "data", "fieldName": "data", "fieldType": "Text", "description": "需要生成HTML页面的数据，以及要求说明", "required": true}], "extendProperties": null}]}]}}]}