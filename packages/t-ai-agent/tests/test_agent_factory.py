import asyncio
import json
import os

from t_ai_agent.agent_factory import convert_to_params_json_schema, create_agent
from t_ai_agent.model import AgentExecutionContext
from t_ai_agent.model.agent_dsl import AgentMeta, FieldMeta, McpTool, ServiceTool


async def test_create_agent():
    # Load agent metadata from test_agent_meta.json file
    test_dir = os.path.dirname(os.path.abspath(__file__))
    agent_meta_path = os.path.join(test_dir, "test_agent_meta.json")

    with open(agent_meta_path, "r", encoding="utf-8") as f:
        agent_meta = json.load(f)

    agent_meta_class = AgentMeta.from_dict(agent_meta)
    agent_ctx = AgentExecutionContext()
    agent, ctx = await create_agent(agent_meta_class, agent_ctx)
    assert agent is not None


async def test_create_agent_meta_class():
    test_dir = os.path.dirname(os.path.abspath(__file__))
    agent_meta_path = os.path.join(test_dir, "test_agent_meta.json")

    with open(agent_meta_path, "r", encoding="utf-8") as f:
        agent_meta = json.load(f)

    agent_meta_class = AgentMeta.from_dict(agent_meta)

    assert agent_meta_class is not None

    assert isinstance(agent_meta_class.children[0].target_agent.props.skill_tools[0], ServiceTool)


async def test_convert_to_params_json_schema():
    test_dir = os.path.dirname(os.path.abspath(__file__))
    input_fields = os.path.join(test_dir, "input_fields.json")

    with open(input_fields, "r", encoding="utf-8") as f:
        input_array = json.load(f)

    # Convert JSON data to FieldMeta objects
    field_meta_list = [FieldMeta.model_validate(field) for field in input_array]

    json_schema = convert_to_params_json_schema(field_meta_list)

    print(json.dumps(json_schema))

    assert json_schema is not None
    # Add more specific assertions to verify the schema structure
    assert "type" in json_schema
    assert json_schema["type"] == "object"
    assert "properties" in json_schema
    assert "required" in json_schema


if __name__ == "__main__":
    asyncio.run(test_convert_to_params_json_schema())
