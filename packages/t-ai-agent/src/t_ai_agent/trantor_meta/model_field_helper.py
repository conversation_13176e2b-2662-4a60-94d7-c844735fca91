import json
from typing import Any, Dict, List, Optional

from loguru import logger
from t_ai_agent.model.agent_dsl import FieldMeta

from .meta_data import fetch_model_meta


def build_fields_prompt_by_model_key(model_key: str, metadata: list[FieldMeta]):
    model_keys = [model_key]
    model_meta = fetch_model_meta(model_keys)
    return {"input_fields": [field.model_dump() for field in metadata], "model_meta": model_meta}


# 辅助函数：自动构建模型中的模型字段，包含子孙模型字段，最多递归3层
def build_fields_prompt(metadata: list[FieldMeta]):
    # 从元数据中提取模型类型字段及其对应的模型键
    # 返回字段名到模型键的映射字典

    # 暂时简化处理，不进行深层递归，只记录相关模型信息
    # 这样可以避免复杂的类型转换问题
    try:
        model_keys = []
        model_meta = {}

        logger.info(f"build_fields_prompt metadata: {metadata}")

        model_keys = get_model_keys(metadata)

        if len(model_keys) > 0:
            model_meta = fetch_model_meta(model_keys)

        return {"input_fields": [field.model_dump() for field in metadata], "model_meta": model_meta}
    except Exception as e:
        logger.exception(f"build_model_fields error: {e}")
        return []


def build_pageable_prompt(pageable: dict):
    return {"pageable": pageable}


def build_condition_items_prompt(condition_items: dict):
    return {"condition_items": condition_items}


def get_model_keys(fields: list[FieldMeta]):
    if not fields:
        return []
    model_keys = []
    for field in fields:
        if isinstance(field, dict):
            field = FieldMeta.model_validate(field)
        if field.field_type == "Model":
            model_key = field.related_model.model_key
            if model_key.startswith("${"):
                continue
            model_keys.append(model_key)
        if (
            field.field_type == "Pageable"
            and field.elements
            and len(field.elements) > 1
            and field.elements[1].elements
            and len(field.elements[1].elements) > 0
        ):
            model_keys.append(field.elements[1].elements[0].related_model.model_key)
        if field.field_type == "ConditionItems" and field.elements and len(field.elements) > 0:
            model_keys.append(field.elements[0].related_model.model_key)
        if field.field_type == "Object":
            sub_model_keys = get_model_keys(field.elements)
            model_keys.extend(sub_model_keys)
        if field.field_type == "Array":
            sub_model_keys = get_model_keys([field.element])
            model_keys.extend(sub_model_keys)
    return model_keys
