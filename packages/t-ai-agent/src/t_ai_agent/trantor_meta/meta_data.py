import requests
from loguru import logger
from t_ai_app.ctx import ReqCtx

from ..utils.trantor_helper import (
    get_callback_url,
    get_source_cookie_from_header,
    get_source_origin_org_id_from_header,
    get_source_referer_from_header,
)


def fetch_model_meta(model_keys: list[str]):
    base_url = get_callback_url()
    url = f"{base_url}/api/trantor/struct-node/list-simple-by-keys"
    params = {
        "keywords": model_keys,
        "cascadeDepth": 2,
        "includeSystemFields": True,
        "includeFields": ["name", "alias", "relKey", "relName", "dictProps", "type"],
        "exactMatch": True,
        "filterRelModelNameAndCode": True,
    }

    # 使用trantor_helper中的方法获取header信息
    source_cookie = get_source_cookie_from_header()
    source_referer = get_source_referer_from_header()
    source_origin_org_id = get_source_origin_org_id_from_header()

    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/json",
    }

    # 添加Referer
    headers["Referer"] = source_referer

    # 添加Origin
    if "Referer" in headers:
        from urllib.parse import urlparse

        parsed_url = urlparse(headers["Referer"])
        headers["Origin"] = f"{parsed_url.scheme}://{parsed_url.netloc}"

    # 准备cookies
    cookies = None
    if source_cookie:
        from http.cookies import SimpleCookie

        cookie = SimpleCookie()
        cookie.load(source_cookie)
        cookies = {k: v.value for k, v in cookie.items()}

    # 如果有origin org id，添加到headers
    if source_origin_org_id:
        headers["Trantor2-ORIGIN-ORG-ID"] = source_origin_org_id

    response = requests.post(url, headers=headers, cookies=cookies, params=params, json=params)

    if response.ok:
        return response.json()
    else:
        response.raise_for_status()
