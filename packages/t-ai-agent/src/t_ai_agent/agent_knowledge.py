"""
Agent知识库集成模块

提供Agent与知识库系统的集成功能，包括：
- 知识库搜索工具创建
- 服务实例管理
- 搜索结果处理
"""

import json
import traceback
from typing import Any, Dict, List, Optional

from agents import FunctionTool
from agents.tool_context import ToolContext
from loguru import logger
from t_ai_knowledge_base.common.config import EmbeddingSettings, MilvusSettings
from t_ai_knowledge_base.model.agent_knowledge_base_dsl import KnowledgeBaseRetriever
from t_ai_knowledge_base.model.knowledge_base_dsl import KnowledgeBase
from t_ai_knowledge_base.service.knowledge_base_service import KnowledgeBaseService

# 全局知识库服务实例 (懒加载)
_knowledge_base_service = None


def get_knowledge_base_service() -> KnowledgeBaseService:
    """
    获取知识库服务实例（单例模式，懒加载）

    Returns:
        KnowledgeBaseService: 知识库服务实例
    """
    global _knowledge_base_service
    if _knowledge_base_service is None:
        milvus_settings = MilvusSettings()
        embedding_settings = EmbeddingSettings()
        _knowledge_base_service = KnowledgeBaseService(
            milvus_settings=milvus_settings, embedding_settings=embedding_settings
        )
        logger.info("知识库服务已初始化")
    return _knowledge_base_service


def create_knowledge_base_tool(agent_know_knowledge_base) -> FunctionTool:
    """
    创建知识库搜索工具

    Args:
        agent_know_knowledge_base: 知识库配置对象 (AgentKnowKnowledgeBase)

    Returns:
        FunctionTool: 知识库搜索工具
    """

    async def knowledge_base_search(context: ToolContext, query: str) -> str:
        """
        在知识库中搜索相关信息

        Args:
            context: 执行Tool系统上下文
            query: 搜索查询文本

        Returns:
            str: JSON格式的搜索结果
        """
        try:
            logger.info(f"收到原始查询参数: '{query}'")

            # 检查query是否是JSON字符串，如果是则解析出实际查询内容
            actual_query = query
            if query.strip().startswith("{") and query.strip().endswith("}"):
                try:
                    parsed_query = json.loads(query)
                    if isinstance(parsed_query, dict) and "query" in parsed_query:
                        actual_query = parsed_query["query"]
                        logger.info(f"从JSON参数中解析出实际查询: '{actual_query}'")
                except json.JSONDecodeError:
                    logger.warning(f"无法解析JSON查询参数，使用原始查询: '{query}'")

            logger.info(f"开始知识库搜索，查询: '{actual_query[:100]}...'")

            # 获取知识库服务实例（复用全局实例）
            knowledge_base_service = get_knowledge_base_service()

            # 获取知识库列表
            knowledge_bases_data = agent_know_knowledge_base.knowledge_bases
            if not knowledge_bases_data:
                return '{"error": "未配置知识库", "results": []}'

            # 构建知识库对象列表
            knowledge_bases = []
            for knowledge_base_data in knowledge_bases_data:
                knowledge_base = KnowledgeBase.from_dict(knowledge_base_data)
                knowledge_bases.append(knowledge_base)

            # 获取检索器配置
            retriever_config = agent_know_knowledge_base.retriever or KnowledgeBaseRetriever()

            # 调用知识库服务进行搜索
            search_results = knowledge_base_service.multi_knowledge_base_retrieval(
                query=actual_query, knowledge_bases=knowledge_bases, retriever_config=retriever_config
            )

            # 转换结果格式
            formatted_results = []
            for result in search_results:
                formatted_result = {
                    "content": result.page_content,  # Document对象使用page_content属性
                    "metadata": result.metadata or {},
                    "source": getattr(result, "source", ""),
                }
                formatted_results.append(formatted_result)

            response = {"query": actual_query, "total_results": len(formatted_results), "results": formatted_results}

            logger.info(f"知识库搜索完成，找到 {len(formatted_results)} 个结果")

            return json.dumps(response, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"知识库搜索出错: {e}")
            logger.error(f"详细错误信息: {traceback.format_exc()}")

            error_response = {
                "error": f"搜索失败: {str(e)}",
                "query": query,  # 错误情况下保留原始query用于调试
                "results": [],
            }
            return json.dumps(error_response, ensure_ascii=False, indent=2)

    # 创建工具
    params_schema = {
        "type": "object",
        "properties": {"query": {"type": "string", "description": "搜索查询文本，描述要查找的信息"}},
        "required": ["query"],
        "additionalProperties": False,
    }

    return FunctionTool(
        name="knowledge_base_search",
        description="在知识库中搜索相关信息。输入查询文本，返回匹配的文档内容和元数据。",
        params_json_schema=params_schema,
        on_invoke_tool=knowledge_base_search,
        strict_json_schema=True,
    )
