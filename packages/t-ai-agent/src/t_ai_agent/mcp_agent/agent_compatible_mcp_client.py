# import json
# from typing import List, Any
# from agents import FunctionTool as AgentTool, Run<PERSON>ontextWrapper
# from mcp.types import Too<PERSON> as MC<PERSON>Tool, TextContent
# from mcp import ClientSession
# from mcp.client.sse import sse_client
# from pydantic import BaseModel, Field
# from loguru import logger

# from t_ai_app.ctx import ReqCtx,TRACE_ID_HEADER_NAME


# class AgentCompatibleMCPClient(BaseModel):
#     """
#     Agent compatible mcp client
#     """

#     endpoint: str = Field(description="mcp server endpoint")
#     tools: List[MCPTool] = Field(description="mcp tools")

#     def get_mcp_tools(self) -> List[MCPTool]:
#         """get tools"""
#         return self.tools

#     async def call_tool(self, tool_name: str, args: dict[str, Any]) -> str:
#         """call tool"""
#         logger.info(f"call tool: {tool_name}, args: {args}")
#         try:
#             async with sse_client(
#                     url=self.endpoint,
#                     timeout=60,
#                     headers={TRACE_ID_HEADER_NAME: ReqCtx.get_trace_id()}
#             ) as client_stream:
#                 async with ClientSession(*client_stream) as session:
#                     result = await session.call_tool(tool_name, args)
#                     return "".join(
#                         [
#                             x.text
#                             for x in result.content
#                             if isinstance(x, TextContent)
#                                and x.text is not None
#                                and x.text != ""
#                         ]
#                     )  # todo: now text based content
#         except Exception as e:
#             logger.exception(f"call tool error: {e}")
#             raise e

#     def mcp_tool_to_agent_tool(self, mcp_tool: MCPTool) -> AgentTool:
#         """mcp tool to t-ai-t_ai_agent tool"""

#         async def _on_invoke_tool(_ctx: RunContextWrapper[Any], args_str: str) -> str:
#             result = await self.call_tool(mcp_tool.name, json.loads(args_str))
#             return result

#         return AgentTool(
#             name=mcp_tool.name,
#             description=mcp_tool.description,
#             params_json_schema=mcp_tool.inputSchema,  # todo： 可能需要转换
#             on_invoke_tool=_on_invoke_tool,
#         )

#     def get_openai_agent_tools(self) -> List[AgentTool]:
#         """get openai t-ai-t_ai_agent tools"""
#         return [self.mcp_tool_to_agent_tool(tool) for tool in self.get_mcp_tools()]

#     def merge_remote_and_choiced_tools(
#             self, choiced_tools: List[AgentTool]
#     ) -> List[AgentTool]:
#         """merge remote and choiced tools

#         Args:
#             choiced_tools (List[AgentTool]): choiced tools

#         Returns:
#             List[AgentTool]: merged tools
#         """
#         tools_from_remote = self.get_openai_agent_tools()
#         if len(choiced_tools) == 0:
#             return tools_from_remote
#         else:
#             tools_from_remote_map = {tool.name: tool for tool in tools_from_remote}
#             result = []
#             for choiced_tool in choiced_tools:
#                 remote_tool = tools_from_remote_map.get(choiced_tool.name)
#                 if remote_tool:
#                     if (
#                             choiced_tool.params_json_schema
#                             and len(choiced_tool.params_json_schema) > 0
#                     ):
#                         remote_tool.params_json_schema = (
#                             choiced_tool.params_json_schema
#                         )  # 使用提供的tool说明覆盖
#                     result.append(remote_tool)
#             return result
