# from typing import List, Optional, Union, ForwardRef

# from agents import Agent, Model, RunContextWrapper, FunctionTool, function_tool
# from loguru import logger
# from pydantic import BaseModel, ConfigDict, Field
# from agents.tool_context import ToolContext

# from t_ai_agent.model import AgentExecutionContext
# from t_ai_app.ctx import ReqCtx
# from t_ai_common.clients.ai_proxy_admin import AIProxyAdminClient
# from t_ai_common.utils.error_helper import raise_root_cause_err

# # from .agent_compatible_mcp_client import AgentCompatibleMCPClient
# from t_ai_mcp import TMCPClient, TMCPConfig

# # Forward reference for Agent type
# AgentRef = ForwardRef('Agent')

# class MCPClientBasedAgent(BaseModel):
#     model_config = ConfigDict(arbitrary_types_allowed=True)
#     mcp_server_key: str = Field(description="mcp server key")
#     mcp_server_name: str = Field(description="mcp server name")
#     mcp_server_version: Optional[str] = Field(description="mcp server version")
#     mcp_server_args: Optional[dict] = Field(default={}, description="mcp server args")
#     model: Union[str, Model] = Field(description="model")
#     tool_description: Optional[str] = Field(description="tool description")
#     instructions: Optional[str] = Field(description="instructions")
#     ai_proxy_admin_client: AIProxyAdminClient = Field(
#         description="ai proxy admin client"
#     )
#     choiced_tools: List[FunctionTool] = Field(description="choiced tools")

#     @staticmethod
#     def define_last_tools(
#         tools_from_remote: List[FunctionTool], choiced_tools: List[FunctionTool]
#     ) -> List[FunctionTool]:
#         """
#         apply choiced tools spec to related remote_tool to provide more flexible
#         """
#         if not choiced_tools:
#             return tools_from_remote
#         else:
#             tools_from_remote_map = {tool.name: tool for tool in tools_from_remote}
#             result = []
#             for choiced_tool in choiced_tools:
#                 if remote_tool := tools_from_remote_map.get(choiced_tool.name):
#                     # overwrite tools_from_remote params_json_schema to choiced one
#                     if choiced_tool.params_json_schema:
#                         remote_tool.params_json_schema = choiced_tool.params_json_schema
#                     result.append(remote_tool)
#             return result

#     def as_tool(self) -> FunctionTool:
#         @function_tool(
#             name_override=self.mcp_server_key,
#             description_override=self.tool_description or self.instructions,
#         )
#         async def _tool(context: ToolContext, input: str) -> str | None:
#             from agents.run import Runner

#             tool_call_id = context.tool_call_id
#             agent_exec_ctx: AgentExecutionContext = context.context

#             try:
#                 mcp_server_info = await self.ai_proxy_admin_client.get_mcp_server_info(
#                     mcp_server_name=self.mcp_server_name,
#                     mcp_server_version=self.mcp_server_version,
#                 )
#                 # mcp_client = AgentCompatibleMCPClient(
#                 #     endpoint=mcp_server_info.endpoint,
#                 #     tools=mcp_server_info.tools,
#                 # )
#                 # tools = mcp_client.merge_remote_and_choiced_tools(self.choiced_tools)
#                 async with TMCPClient(
#                     name=self.mcp_server_name,
#                     config=TMCPConfig(
#                         mcp_server_endpoint=mcp_server_info.endpoint,
#                         mcp_server_args=self.mcp_server_args,
#                         request_id_getter=lambda: ReqCtx.get_trace_id(),
#                     ),
#                 ) as client:
#                     await client.initialize()
#                     tools_from_remote = await client.list_openai_agent_tools()
#                     last_tools = self.define_last_tools(
#                         tools_from_remote, self.choiced_tools
#                     )

#                     agent = Agent(
#                         name=self.mcp_server_name,
#                         model=self.model,
#                         instructions=self.instructions or mcp_server_info.description,
#                         tools= [] + last_tools,
#                         # tool_use_behavior="stop_on_first_tool",
#                     )
#                     output = await Runner.run(
#                         starting_agent=agent,
#                         input=input,
#                         context=context.context,
#                     )

#                 # 设置工具调用成功设置成功状态
#                 agent_exec_ctx.update_tool_call_status([tool_call_id], "success")

#                 return str(output.final_output)
#             except Exception as e:
#                 logger.exception(f"Error running tool: {e}")

#                 # 设置工具调用失败设置错误状态
#                 agent_exec_ctx.update_tool_call_status([tool_call_id], "error")

#                 # 返回的时候返回root cause
#                 raise_root_cause_err(e)

#         return _tool

# # Rebuild the model to handle forward references
# MCPClientBasedAgent.model_rebuild()
