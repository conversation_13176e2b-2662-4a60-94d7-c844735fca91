from typing import Dict

from openai import Async<PERSON><PERSON>A<PERSON>
from t_ai_app import G
from t_ai_app.ctx import ReqCtx


class AiProxyClientFactory:

    def __init__(self):
        self._model_cache: Dict[str, AsyncOpenAI] = {}

    async def get_client(self, model_publisher: str | None = None) -> AsyncOpenAI:
        headers = {}
        if ReqCtx.get_trace_id() is not None:
            headers["X-Request-Id"] = ReqCtx.get_trace_id()

        if model_publisher is None:
            return AsyncOpenAI(
                api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
                base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
                timeout=G.APP_SETTING.ai_proxy.llm_timeout,
                default_headers=headers
            )

        if model_publisher in self._model_cache:
            return self._model_cache[model_publisher]

        headers["X-AI-Proxy-Model-Publisher"] = model_publisher
        client = AsyncOpenAI(
            api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
            base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            default_headers=headers,
        )
        self._model_cache[model_publisher] = client
        return client


ai_proxy_client_factory = AiProxyClientFactory()
