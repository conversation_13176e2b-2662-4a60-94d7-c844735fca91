from typing import Optional

from t_ai_agent.model.agent_context import Agent<PERSON>ontex<PERSON>
from t_ai_agent.model.agent_dsl import Agent<PERSON><PERSON>


def gen_system_prompt_from_trigger(agent_meta: AgentMeta, trigger_key: Optional[str]):
    todo = ""
    for trigger_item in agent_meta.props.triggers:
        if trigger_item.key == trigger_key:
            todo = trigger_item.conversation_content
            if todo is None or len(todo) == 0:
                # 兜底的逻辑
                todo = trigger_item.name
    prompt = f"你是`{agent_meta.name}`，现在请你选择合适的工具执行我的任务：{todo}"
    agent_meta.props.system_prompt = prompt


def reset_agent_from_trigger(agent_meta: AgentMeta, trigger_key: Optional[str]):
    gen_system_prompt_from_trigger(agent_meta, trigger_key)
    agent_meta.children = None
    agent_meta.handoffs = None


def get_skill_tools_from_trigger(agent_ctx: AgentContext, agent_meta: AgentMeta):
    triggers = agent_meta.props.triggers
    if triggers is not None and len(triggers) > 0:
        for trigger in triggers:
            if agent_ctx.trigger == trigger.key and trigger.tool is not None:
                return [trigger.tool]
    else:
        return []
