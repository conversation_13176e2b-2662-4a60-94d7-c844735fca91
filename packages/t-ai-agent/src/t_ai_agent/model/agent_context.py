import time
from typing import TYPE_CHECKING, Any, Optional

from agents import ToolCallOutputItem
from agents.mcp import MCPServer
from loguru import logger
from openai.types.responses import (
    ResponseFunctionToolCall,
    ResponseCompletedEvent
)
from pydantic import BaseModel, ConfigDict

from t_ai_app.ctx import ReqCtx
from t_ai_mcp import TMCPServer
from .agent_dsl import AgentMeta, LlmModel, Tools
from .common import TokenUsage

if TYPE_CHECKING:
    from .agent_message import AgentMessage


class AgentMetadata(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    avatar: Optional[str] = None
    llm_model: Optional[LlmModel] = None

    @classmethod
    def from_meta(cls, **kwargs):
        return cls(**kwargs)


class ToolMetadata(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    module_key: Optional[str] = None
    visible: bool = True
    final_output: bool = False
    tool_config: Optional[Tools] = None
    agent_name: Optional[str] = None  # 这里用agent_name，因为sdk中的Agent对象中只有name


class ToolCallMetadata(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    module_key: Optional[str] = None
    arguments: Optional[str] = None
    create_time: Optional[float] = None
    build_args_cost_time: float = 0.00
    invoke_args_changed: bool = False
    invoke_cost_time: Optional[float] = 0.00
    invoke_cost_token: TokenUsage = TokenUsage()
    invoke_status: Optional[str] = "success"
    invoke_output: Optional[str] = None
    visible: bool = True

    def cost(self, end_time: float):
        if self.create_time is not None:
            self.invoke_cost_time = round(end_time - self.create_time, 2)
        else:
            self.invoke_cost_time = None

    @classmethod
    def from_meta(cls, key, name, arguments, create_time, module_key=None):
        return cls(
            key=key,
            name=name,
            arguments=arguments,
            create_time=create_time,
            module_key=module_key,
        )


class AgentContext(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    agents: list[AgentMeta] = []
    tools: list[ToolMetadata] = []
    mcp_servers: list[MCPServer] = []
    trigger: Optional[str] = None  # 触发器用于创建Agent实例时工具的重新挂载
    main_agent_lang_switch: Optional[bool] = None  # 主语言开关，用于多语言支持

    def get_agent_meta_by_name(self, agent_name: str) -> Optional[AgentMeta]:
        for agent in self.agents:
            if agent.name == agent_name:
                return agent
        return None

    def find_agent_meta(self, agent_key) -> Optional[AgentMeta]:
        for agent in self.agents:
            if agent.key == agent_key:
                return agent
        return None

    def append_agent(self, agent: AgentMeta):
        has_same_key = False
        if len(self.agents) > 0:
            for original_agent in self.agents:
                if original_agent.key == agent.key:
                    has_same_key = True
                    break
        if not has_same_key:
            self.agents.append(agent)
        return self

    def any_tool_set_final_output(self):
        """
        检查是否有任何工具设置了final_output为True
        """
        for tool in self.tools:
            if tool.final_output:
                return True
        return False

    def find_tool(self, tool_key) -> Optional[ToolMetadata]:
        for tool in self.tools:
            if tool.key == tool_key:
                return tool
        return None

    def find_tool_in_agent(self, tool_key, agent_name) -> Optional[ToolMetadata]:
        if agent_name is None:
            return self.find_tool(tool_key)
        for tool in self.tools:
            if tool.key == tool_key and tool.agent_name is not None and tool.agent_name == agent_name:
                return tool
        return None

    def append_tool(self, tool: ToolMetadata):
        self.tools.append(tool)

    def add_tool(
            self,
            tool_key: str,
            tool_name: str,
            module_key: str = "",
            visible: bool = True,
            final_output: bool = False,
            tool_config: Optional[Tools] = None,
            agent_name: Optional[str] = None,
    ):
        self.append_tool(
            ToolMetadata(
                key=tool_key,
                name=tool_name,
                module_key=module_key,
                visible=visible,
                final_output=final_output,
                tool_config=tool_config,
                agent_name=agent_name,
            )
        )
        return self

    def add_mcp_server(self, mcp_server: MCPServer):
        self.mcp_servers.append(mcp_server)
        return self


class AgentExecutionContext(AgentContext):
    current_agent: Optional[Any] = None  # Agent对象，来至Agent SDK
    tools_call_mapping: dict[str, ToolCallMetadata] = {}
    tools_call_mapping_manual: dict[str, ToolCallMetadata] = {}  # 手动添加进来的工具调用
    cost_time: Optional[float] = 0.00
    variables: dict[str, Any] = {}
    total_token_usage: TokenUsage = TokenUsage()
    agent_result: Optional[Any] = None
    conversation_history: list[dict] = []  # 这个主要存储input_items
    _runner_context: Optional[Any] = None  # 存储Runner的上下文引用
    agent_meta: Optional[AgentMeta] = None
    tools_to_final_output_result: bool = False

    def from_agent_context(self, agent_context: AgentContext):
        self.agents = agent_context.agents
        self.tools = agent_context.tools
        # 如果有mcp的源是trantor的mcp服务，需要copy一分
        for mcp_server in agent_context.mcp_servers:
            if isinstance(mcp_server, TMCPServer):
                self.mcp_servers.append(TMCPServer(mcp_server.mcp_server_name, mcp_server.cfg))
            else:
                self.mcp_servers.append(mcp_server)
        return self

    def set_conversation_history(self, input_items: list[dict]):
        """
        设置对话历史（主要是input_items）

        Args:
            input_items: 原始的输入消息列表
        """
        self.conversation_history = input_items or []
        return self

    def set_runner_context(self, runner_context):
        """
        设置Runner上下文引用，用于获取完整的对话历史

        Args:
            runner_context: agents库的Runner上下文
        """
        self._runner_context = runner_context
        return self

    def get_conversation_history(self) -> list[dict]:
        """
        获取对话历史（input_items）

        Returns:
            list[dict]: 对话历史列表
        """
        return self.conversation_history

    def get_full_conversation_history(self) -> list[dict]:
        """
        获取完整的对话历史，包括当前会话中的所有消息

        Returns:
            list[dict]: 完整的对话历史列表
        """
        # 尝试从Runner上下文中获取完整的消息历史
        if self._runner_context and hasattr(self._runner_context, "messages"):
            try:
                # 将agents库的消息格式转换为我们的格式
                full_history = []
                for msg in self._runner_context.messages:
                    if hasattr(msg, "role") and hasattr(msg, "content"):
                        # 处理不同类型的消息内容
                        content = ""
                        if isinstance(msg.content, str):
                            content = msg.content
                        elif isinstance(msg.content, list):
                            # 处理多部分内容
                            text_parts = []
                            for part in msg.content:
                                if hasattr(part, "text"):
                                    text_parts.append(part.text)
                                elif isinstance(part, dict) and "text" in part:
                                    text_parts.append(part["text"])
                            content = " ".join(text_parts)
                        elif hasattr(msg.content, "text"):
                            content = msg.content.text

                        full_history.append({"role": msg.role, "content": content})
                return full_history
            except Exception as e:
                logger.warning(f"Failed to get full conversation history from runner context: {e}")

        # 如果无法从Runner获取，则返回input_items
        return self.conversation_history

    def get_input_items_history(self) -> list[dict]:
        """
        明确获取input_items历史（用户输入和部分助手回复）

        Returns:
            list[dict]: input_items历史列表
        """
        return self.conversation_history

    def update_tool_call_status(self, call_ids: list[str], status: str, final_output: str = None):
        """
        Update status for a list of tool calls

        Args:
            call_ids (list[str]): List of tool call IDs
            status (str): New status to set ('success' or 'error')
            final_output (str): New final output
        """
        if not call_ids:
            return

        for call_id in call_ids:
            # 修改工具映射
            tool_call_meta = self.tools_call_mapping.get(call_id)
            if tool_call_meta:
                tool_call_meta.cost(time.time())
                tool_call_meta.invoke_status = status
                if final_output:
                    tool_call_meta.invoke_output = final_output

            # 修改手动添加的工具映射
            manual_tool_meta = self.tools_call_mapping_manual.get(call_id)
            if manual_tool_meta:
                manual_tool_meta.cost(time.time())
                manual_tool_meta.invoke_status = status
                if final_output:
                    manual_tool_meta.invoke_output = final_output

    def create_tool_call_message(self, event_data: ResponseFunctionToolCall) -> Optional["AgentMessage"]:
        """
        Create a tool call message from event data

        Args:
            event_data: The event data containing tool call information

        Returns:
            Optional[AgentMessage]: The created agent message or None if no valid tool call
        """
        from .agent_message import AgentMessage

        tools = self.tools
        if not tools:
            return None

        # Check if tool is registered in context
        call_id = event_data.call_id
        tool_key = event_data.name
        arguments = event_data.arguments
        tool_meta: ToolMetadata = None

        if len(self.tools) > 0:
            tool_meta = self.find_tool_in_agent(tool_key, self.current_agent.name)

        # If tool info not found in context, log warning
        if tool_meta is None:
            logger.warning(f"The tool call {tool_key} hasn't been initiated yet")
            return None

        # Create tool call metadata
        self.tools_call_mapping[call_id] = ToolCallMetadata(
            key=tool_key,
            name=tool_meta.name,
            module_key=tool_meta.module_key,
            arguments=arguments,
            visible=tool_meta.visible,
            create_time=time.time(),
        )

        # Create and return agent message
        return AgentMessage.from_event(event_data, content_type="function_call", context=self)

    def create_tool_call_arguments_message(self, data: ToolCallOutputItem) -> Optional["AgentMessage"]:
        from .agent_message import AgentMessage

        tool_call_id = data.raw_item.get("call_id")
        if tool_call_id is None:
            return None

        call_tool_info = self.tools_call_mapping.get(tool_call_id)
        if call_tool_info is None:
            return None

        return AgentMessage.from_event(data, content_type="function_call_arguments", context=self)

    def create_tool_output_message(self, event_item: ToolCallOutputItem) -> Optional["AgentMessage"]:
        """
        Create a tool output message from event item

        Args:
            event_item: The event item containing tool output information

        Returns:
            Optional[AgentMessage]: The created agent message or None if no valid tool output
        """
        from .agent_message import AgentMessage

        # Filter out multiple handoffs messages
        if event_item.output.find("Multiple handoffs detected, ignoring this one.") >= 0:
            return None

        if not event_item.raw_item:
            return None

        tool_call_id = event_item.raw_item.get("call_id")
        if tool_call_id is None:
            return None

        call_tool_info = self.tools_call_mapping.get(tool_call_id)
        if call_tool_info is None:
            return None

        # Update tool call metadata
        call_tool_info.cost(time.time())
        call_tool_info.invoke_output = (
            event_item.output if isinstance(event_item.output, str) else str(event_item.output)
        )

        # Update tool call mapping
        self.tools_call_mapping[tool_call_id] = call_tool_info

        # Create and return agent message
        return AgentMessage.from_event(event_item, content_type="function_call_output", context=self)

    def update_tool_call_arguments(self, call_id: str, arguments: str):
        """
        Update tool call arguments where invoke trantor programmable service args has ModuleKey

        :param call_id:
        :param arguments:
        :return:
        """
        # 修改工具映射
        tool_meta = self.tools_call_mapping.get(call_id)
        if tool_meta:
            tool_meta.arguments = arguments
            tool_meta.invoke_args_changed = True

        # 修改手动添加的工具映射 - 使用 .get() 方法安全访问
        manual_tool_meta = self.tools_call_mapping_manual.get(call_id)
        if manual_tool_meta:
            manual_tool_meta.arguments = arguments
            manual_tool_meta.invoke_args_changed = True

        # 此处需要更新 conversation_history 里对应的工具调用参数
        conversation_history = self.get_conversation_history()
        for item in conversation_history:
            if item.get("call_id") == call_id:
                item["arguments"] = arguments
                break

    def manual_add_tool_call_mapping(self, call_id, tool_call: ToolCallMetadata, agent_name: str):
        if self.tools_call_mapping.get(call_id) is None:
            tool_key = tool_call.key
            if len(self.tools) > 0:
                tool_info = self.find_tool_in_agent(tool_key, agent_name)
                if tool_info is not None and tool_info.name is not None:
                    tool_call.name = tool_info.name
                    tool_call.module_key = tool_info.module_key
            self.tools_call_mapping_manual[call_id] = tool_call

    def create_tool_call_messages_with_manual(self) -> list["AgentMessage"]:
        """
        创建手动设置进来的工具消息
        :return: 消息列表
        """
        from .agent_message import (
            AgentMessage,
            AgentMessageMeta,
            AgentToolCallContent,
            AgentToolOutputContent,
        )

        messages = []
        for call_id, tool_call in self.tools_call_mapping_manual.items():
            call_message = AgentMessage(
                content=AgentToolCallContent(
                    key=tool_call.key,
                    name=tool_call.name,
                    arguments=tool_call.arguments,
                    call_id=call_id,
                ),
                meta=AgentMessageMeta.from_agent(self.current_agent, self),
            )
            messages.append(call_message)

            output_message = AgentMessage(
                content=AgentToolOutputContent(
                    call_id=call_id,
                    output=tool_call.invoke_output,
                    invoke_status=tool_call.invoke_status,
                    invoke_cost_time=tool_call.invoke_cost_time,
                ),
                meta=AgentMessageMeta.from_agent(self.current_agent, self),
            )
            messages.append(output_message)

        # 清空掉手动添加的工具
        self.tools_call_mapping_manual.clear()

        return messages

    def update_token_usage(self, event_data: ResponseCompletedEvent):
        """
        Handle response completion event and update token usage

        Args:
            event_data: The response completion event data

        """
        if not event_data.response:
            return

        invoke_response = event_data.response
        if not invoke_response.usage:
            return

        # Update total token usage
        usage = invoke_response.usage
        if usage:
            self.total_token_usage.add(usage)

    def create_token_usage_message(self) -> "AgentMessage":
        """
        Get a message containing the current token usage

        Returns:
            AgentMessage: Message containing token usage information
        """
        from .agent_message import AgentMessage

        return AgentMessage.from_event(self.total_token_usage, content_type="tokens", context=self)

    def create_callback_message(self) -> Optional["AgentMessage"]:
        from .agent_message import AgentMessage

        agent_meta = self.get_agent_meta_by_name(getattr(self.current_agent, 'name'))

        # 用户问题建议回调
        if agent_meta and agent_meta.props.user_questions_suggest:

            header = ReqCtx.get_header() or {}
            callback_url = header.t_ai_callback or ""
            return AgentMessage.from_event(
                {
                    "callback_url": f"{callback_url}/api/trantor/ai/recommend-prompt",
                    "callback_type": "http",
                },
                content_type="callback",
                context=self,
            )
        else:
            return None
