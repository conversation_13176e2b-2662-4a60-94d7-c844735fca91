from openai.types.responses import ResponseUsage
from pydantic import BaseModel


class TokenUsage(BaseModel):
    input_tokens: int = 0
    cached_tokens: int = 0  # Tokens that are cached and reused
    output_tokens: int = 0
    total_tokens: int = 0
    reasoning_tokens: int = 0  # Tokens used for reasoning, if applicable

    def from_usage(self, usage: ResponseUsage):
        self.input_tokens = usage.input_tokens
        self.output_tokens = usage.output_tokens
        self.total_tokens = usage.total_tokens
        self.cached_tokens = usage.input_tokens_details.cached_tokens
        self.reasoning_tokens = usage.output_tokens_details.reasoning_tokens
        return self

    def add(self, usage: ResponseUsage):
        self.input_tokens += usage.input_tokens
        self.output_tokens += usage.output_tokens
        self.total_tokens += usage.total_tokens
        self.cached_tokens += usage.input_tokens_details.cached_tokens
        self.reasoning_tokens += usage.output_tokens_details.reasoning_tokens
        return self
