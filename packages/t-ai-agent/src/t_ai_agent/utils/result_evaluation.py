from dataclasses import dataclass
from typing import Literal
import time
from loguru import logger

from agents import Agent, ModelSettings, OpenAIChatCompletionsModel, TResponseInputItem, Runner

from t_ai_agent.ai_proxy_client_factory import ai_proxy_client_factory

instructions = """
您是一位严谨而专业的评估专家。您需要综合分析用户的意图和大模型的回答进行评估，然后判断大模型回答的是否足够好。

## 评估示例（但不限于）：
- 回答不能仅仅告诉用户等待而不执行具体操作。
  例如：
  用户：请帮我分析一下这个数据集。
  助手：请稍等，我将为你交接给数据分析专家来处理这个任务。
  评估结果：needs_redo

- 回答不能让用户进行二次确认，应该主动执行。
  例如：
  用户：请帮我创建一个供应商数据。
  助手：我将为您交接给供应商专家来处理这个任务，请问是否需要继续执行？
  评估结果：needs_redo

- 回答中如果已经明确表明无法获取结果，则直接返回"pass"。
  例如：
  用户：请帮我分析一下企业风险信息。
  助手：抱歉，因为查询工具执行异常，我无法获取企业风险信息。
  评估结果：pass

- 回答中如果要求用户补充信息的，则直接返回"pass"。
  例如：
  用户：请帮我分析一下企业风险信息。
  助手：请问您能否补充以下信息，以便我能更好的分析吗？\n1.企业名称 \n2.企业地址。
  评估结果：pass

## 约束：
- 评估时需结合用户意图和上下文，确保回答符合用户需求。
- 避免让用户等待或二次确认，提升回答的主动性和实用性。
- 评估时不必追求完美
    """

@dataclass
class EvaluationFeedback:
    feedback: str
    score: Literal["pass", "needs_redo"]


class EvaluationAgent:
    def __init__(self):
        self._model_publisher = "openai"
        self._model_name = "gpt-4.1"
        self._index: int = 0
        self._agent = None

    def get_evaluation_count(self):
        return self._index

    async def _create_agent(self) -> Agent:
        return Agent(
            name="evaluator",
            model_settings=ModelSettings(
                temperature=0.0,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0,
            ),
            model=OpenAIChatCompletionsModel(
                model=self._model_name,
                openai_client=await ai_proxy_client_factory.get_client(self._model_publisher),
            ),
            output_type=EvaluationFeedback,
            instructions=instructions,
        )

    async def run_agent(self, list_input: list[TResponseInputItem]) -> EvaluationFeedback:
        self._index += 1
        logger.info(f"################ evaluation index: {self._index}")
        start_time = time.time()
        if self._agent is None:
            self._agent = await self._create_agent()
        evaluator_result = await Runner.run(self._agent, list_input)
        result: EvaluationFeedback = evaluator_result.final_output
        logger.info(f"################ evaluation result: {result}")
        logger.info(f"################ evaluation total cost: {round(time.time() - start_time, 2)}")
        return result
