import json
from typing import Union

from agents.items import TResponseInputItem
from openai.types.responses.response_input_file_param import ResponseInputFileParam
from openai.types.responses.response_input_image_param import ResponseInputImageParam
from t_ai_common.utils.common import get_short_key, parse_file_name

from .attachment_handler import process_attachments


class InputItemHelpers:
    """
    将输入的input_items转换为TResponseInputItem,
    因为 input_items 是外部输入，需要进行转换, input_items 的格式如下:
    ```
    [
        {
            "role": "user",
            "content": "[{\"type\":\"text\",\"text\":\"你好\"},{\"type\":\"attachments\",\"attachments\":[...]}]"
        },
        {
            "role": "assistant",
            "content": "{\"type\":\"function_call\",\"key\":\"tool_key\",\"arguments\":{\"param1\":\"value1\",\"param2\":\"value2\"}}"
        },
    ]
    ```
    """

    @classmethod
    async def to_input_items(cls, input_items) -> list[TResponseInputItem]:
        result = []

        if not input_items:
            return result

        for item in input_items:
            # Handle user or assistant messages
            content = item.get("content", "")

            if item["role"] == "user":
                content_list = json.loads(content)

                # Convert to proper content format
                attachment_infos = await cls.get_all_attachment_info(content_list)
                attachment_prompt = get_attachment_prompt(attachment_infos)

                formatted_content = []
                for content_item in content_list:
                    if content_item.get("type") == "text":
                        # 处理文本内容
                        text = content_item.get("text", "")

                        # 处理文本内容中的附件URL
                        if len(attachment_prompt) > 0:
                            text = text + "\n" + attachment_prompt

                        formatted_content.append(
                            {
                                "type": "input_text",
                                "text": text,
                            }
                        )
                    elif content_item.get("type") == "attachments":
                        # 处理附件
                        attachments = content_item.get("attachments", [])
                        attachment_items = await cls.to_attachments_items(attachments)
                        for attachment_item in attachment_items:
                            formatted_content.append(attachment_item)

                result.append({"role": item["role"], "content": formatted_content, "type": "message"})

            elif item["role"] == "assistant":
                content_json = json.loads(content)
                if content_json.get("type") == "text":
                    result.append(
                        {
                            "role": item["role"],
                            "content": [{"type": "output_text", "text": content_json.get("text", "")}],
                            "type": "message",
                        }
                    )
                elif content_json.get("type") == "function_call":
                    result.append(
                        {
                            "call_id": content_json.get("callId", ""),
                            "name": get_short_key(content_json.get("key", "")),
                            "arguments": content_json.get("arguments", "{}"),
                            "type": "function_call",
                        }
                    )
                elif content_json.get("type") == "function_call_output":
                    result.append(
                        {
                            "call_id": content_json.get("callId", ""),
                            "output": content_json.get("output", "{}"),
                            "type": "function_call_output",
                        }
                    )
                # elif content_json.get("type") == "agent_handoff":
                #     agent_meta = content_json.get("targetAgent", {})
                #     agent_key = agent_meta.get("key")
                #     agent_name = agent_meta.get("name")
                #     # 给个假的call_id，交接的本质是function_call，但是我并没有存在交接的function_call相关信息
                #     call_id = 'call_' + uuid.uuid4().hex
                #     if agent_key and agent_name:
                #         result.append({
                #             "call_id": call_id,
                #             "name": get_short_key(agent_key),
                #             "arguments": '{}',
                #             "type": "function_call"
                #         })
                #         result.append({
                #             "call_id": call_id,
                #             "output": json.dumps({'assistant': agent_name},ensure_ascii=False),
                #             "type": "function_call_output"
                #         })

            else:
                result.append({"role": item["role"], "content": content, "type": "message"})

        return result

    @classmethod
    async def to_attachments_items(cls, attachments) -> list[Union[ResponseInputImageParam, ResponseInputFileParam]]:
        result = []

        if not attachments:
            return result

        attachment_results = await process_attachments(attachments) if attachments else []
        for attachment in attachment_results:
            result.append(attachment.to_agent_input())

        return result

    @classmethod
    async def get_all_attachment_info(cls, content_list):
        attachment_info = []
        for content_item in content_list:
            if content_item.get("type") == "attachments":
                attachments = content_item.get("attachments", [])
                # 附件支持2种格式，一种是字典列表，一种是字符串列表
                for attachment in attachments:
                    if isinstance(attachment, dict):
                        attachment_url = attachment.get("encodeUrl") or attachment.get("url")
                        file_name = attachment.get("fileName")
                        if attachment_url and file_name:
                            attachment_info.append({"url": attachment_url, "fileName": file_name})
                    else:
                        attachment_url = attachment
                        attachment_info.append({"url": attachment_url, "fileName": parse_file_name(attachment_url)})
        return attachment_info


def get_attachment_prompt(attachment_infos):
    if not attachment_infos:
        return ""
    attachment_list = [f"- {info['fileName']} ({info['url']})" for info in attachment_infos]
    return f"\n附件列表：\n" + "\n".join(attachment_list)
