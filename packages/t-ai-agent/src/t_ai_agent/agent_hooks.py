from agents import Agent, AgentHooks, FunctionTool
from loguru import logger

from .agent_tool import convert_to_params_json_schema
from .model.agent_context import AgentExecutionContext
from .model.agent_dsl import FieldMeta, ServiceTool, SubTools
from .utils.trantor_helper import is_var_value


def _has_var_value_in_field(field: FieldMeta) -> bool:
    """
    递归检查字段是否包含变量值

    Args:
        field: 要检查的字段元数据

    Returns:
        bool: 如果字段或其子字段包含变量值则返回True，否则返回False
    """
    # 检查当前字段的 default_value 是否为变量
    if field.default_value is not None:
        if is_var_value(field.default_value):
            return True

    # 递归检查 elements 列表
    if field.elements is not None:
        for element in field.elements:
            if _has_var_value_in_field(element):
                return True

    return False


class CustomAgentHooks(AgentHooks):

    async def _handle_dynamic_params_schema(self, context, agent: Agent) -> None:
        """
        处理工具的动态参数schema
        """
        try:
            if agent.tools and len(agent.tools) > 0:
                # 获取执行上下文
                agent_exec_ctx: AgentExecutionContext = context.context
                for tool in agent.tools:
                    # 处理FunctionTool
                    if isinstance(tool, FunctionTool):
                        # 在上下文中找工具
                        tool_meta = agent_exec_ctx.find_tool_in_agent(tool.name, agent.name)
                        if not tool_meta or not tool_meta.tool_config:
                            continue
                        # 获取input_meta
                        input_meta_list = None
                        if isinstance(tool_meta.tool_config, ServiceTool):
                            input_meta_list = tool_meta.tool_config.input
                        elif isinstance(tool_meta.tool_config, SubTools):
                            input_meta_list = tool_meta.tool_config.input
                        # 处理工具入参
                        if input_meta_list and len(input_meta_list) > 0:
                            # 判断input是否有变量
                            has_variable = any(_has_var_value_in_field(field) for field in input_meta_list)
                            if has_variable:
                                schema = await convert_to_params_json_schema(input_meta_list, agent_exec_ctx)
                                if schema:
                                    tool.params_json_schema = schema
                                    logger.info(f"Successfully updated tool params schema for {tool.name}")
        except Exception as e:
            logger.error(f"Error in dynamic params mapping: {str(e)}")
            # 不抛出异常，让工具继续使用原始参数模式

    async def on_start(self, context, agent: Agent) -> None:
        # 动态参数处理
        # await self._handle_dynamic_params_schema(context, agent)
        pass
