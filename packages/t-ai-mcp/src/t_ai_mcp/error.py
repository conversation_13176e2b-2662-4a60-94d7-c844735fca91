class TMCPServerError(Exception):
    """Exception raised when the TMCPServer makes an error."""

    message: str

    def __init__(self, message: str):
        self.message = message


class TMCPServerConnectionError(TMCPServerError):
    """Exception raised when the TMCPServer cannot connect to the server."""

    def __init__(self, message: str):
        super().__init__(message)


class MCPServerGuardError(Exception):
    """Exception raised when the TMCPServerGroup makes an error."""

    message: str

    def __init__(self, message: str):
        self.message = message
