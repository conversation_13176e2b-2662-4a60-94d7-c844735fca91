import asyncio
import logging
from collections import deque
from contextlib import AbstractAsyncContextManager, AsyncExitStack, asynccontextmanager
from datetime import datetime
from typing import Any, override, Optional, final

import anyio
import httpx
from agents import Agent, RunContextWrapper
from agents.mcp.server import MCPServer
from anyio.streams.memory import MemoryObjectReceiveStream, MemoryObjectSendStream
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.client.streamable_http import GetSessionIdCallback, streamablehttp_client
from mcp.shared.message import SessionMessage
from mcp.types import (
    ClientNotification,
    InitializedNotification,
    InitializeResult,
    NotificationParams,
    CallToolResult,
    GetPromptResult,
    ListPromptsResult,
    TextContent,
)
from mcp.types import Tool as MCPTool

from t_ai_common.utils.common import obj_pretty
from .error import MCPServerGuardError, TMCPServerError, TMCPServerConnectionError
from .models import TMCPServerConfig, TMCPTransportArgs

logger = logging.getLogger(__name__)


class TMCPConnectionPool:
    """TMCP 连接池管理器"""

    def __init__(self):
        self._pool: dict[str, TMCPServer] = {}
        self._lock = asyncio.Lock()

    async def add_server(self, server_key: str, server: "TMCPSharedServer"):
        async with self._lock:
            if server_key not in self._pool:
                # 创建新连接并缓存
                mcp_server = TMCPServer(server.name, server.cfg)
                self._pool[server_key] = mcp_server

    async def get_server(self, server_key: str) -> Optional["TMCPServer"]:
        """获取或创建服务器连接"""
        async with self._lock:
            if server_key in self._pool:
                return self._pool.get(server_key)
        return None

    async def cleanup_server(self, server_key: str):
        """清理特定服务器连接"""
        async with self._lock:
            if server_key in self._pool:
                server = self._pool[server_key]
                await server.cleanup()
                del self._pool[server_key]

    async def cleanup_all(self):
        """清理所有连接"""
        async with self._lock:
            for server in self._pool.values():
                await server.cleanup()
            self._pool.clear()


# 全局连接池实例
_connection_pool = TMCPConnectionPool()


@final
class TMCPServer(AbstractAsyncContextManager, MCPServer):
    """
    A multi-model mcp server compatible with openai-agents.
    Features:
    1. support transport both SSE and StreamableHttp
    2. support trim tools from mcp server tool/list
    3. support mcp_server_args based on mcp-spec
    4. support lazy init
    5. support add custom args in call_tool

    fix:
    1 fix "RuntimeError: Attempted to exit cancel scope in a different task than it was entered in."
    refer: https://github.com/jlowin/fastmcp/pull/635

    """

    def __init__(self, mcp_server_name: str, config: TMCPServerConfig):
        # Call parent class __init__ with use_structured_content parameter
        super().__init__(use_structured_content=config.use_structured_content)
        self.mcp_server_name = mcp_server_name
        self.cfg = config
        self._lock: asyncio.Lock = asyncio.Lock()

        # session task to mange session task
        self._session_ready_event = asyncio.Event()
        self._session_stop_event = asyncio.Event()
        self._session_task: asyncio.Task | None = None

        # session instance that manged by session task
        self._client_session: ClientSession | None = None
        self._client_session_initialize_result: InitializeResult | None = None

    @property
    @override
    def name(self) -> str:
        """server name"""
        return self.mcp_server_name

    def create_streams(
            self,
    ) -> AbstractAsyncContextManager[
        tuple[
            MemoryObjectReceiveStream[SessionMessage | Exception],
            MemoryObjectSendStream[SessionMessage],
            GetSessionIdCallback | None
        ] | tuple[
            MemoryObjectReceiveStream[SessionMessage | Exception],
            MemoryObjectSendStream[SessionMessage]
        ]
        ]:
        """Create the streams for the server."""
        match self.cfg.transport_mode:
            case "sse":
                return sse_client(
                    url=self.cfg.server_endpoint,
                    headers=self.cfg.headers,
                    timeout=self.cfg.timeout.total_seconds(),
                    sse_read_timeout=self.cfg.sse_read_timeout.total_seconds(),
                )
            case "streamable_http":
                return streamablehttp_client(
                    url=self.cfg.server_endpoint,
                    headers=self.cfg.headers,
                    timeout=self.cfg.timeout,
                    sse_read_timeout=self.cfg.sse_read_timeout,
                    terminate_on_close=self.cfg.terminate_on_close,
                )

    @asynccontextmanager
    async def _session_connect(self):
        """connect to the server internally"""
        async with AsyncExitStack() as exit_stack:
            try:
                transport = await exit_stack.enter_async_context(self.create_streams())
                # streamablehttp_client returns (read, write, get_session_id)
                # sse_client returns (read, write)
                read_stream, write_stream, *_ = transport

                self._client_session = await exit_stack.enter_async_context(
                    ClientSession(
                        read_stream=read_stream,
                        write_stream=write_stream,
                        read_timeout_seconds=self.cfg.client_session_timeout,
                    )
                )
                # mcp spec initialize
                self._client_session_initialize_result = await self._client_session.initialize()
                # mcp server  initialize
                await self._initialize_mcp_server()
                self._connection_healthy = True
                yield
            except anyio.ClosedResourceError:
                self._connection_healthy = False
                raise RuntimeError("Server session was closed unexpectedly")
            except Exception as e:
                self._connection_healthy = False
                raise e
            finally:
                # reset session instance
                self._client_session = None
                self._client_session_initialize_result = None

    async def _session_runner(self):
        """
        Background task that manages the actual session lifecycle.
        """
        try:
            async with AsyncExitStack() as exit_stack:
                await exit_stack.enter_async_context(self._session_connect())
                # session is ready
                self._session_ready_event.set()
                # wailt until disconnect/stop is requested
                await self._session_stop_event.wait()
        finally:
            self._session_ready_event.set()

    async def _connect(self) -> None:
        """Connect to the server and initialize the session."""
        try:
            # double lock check
            if self._session_task is not None:
                return
            async with self._lock:
                if self._session_task is not None:
                    return
                self._session_task = asyncio.create_task(self._session_runner())
                # wait until session is ready
                await self._session_ready_event.wait()
                # in this time, _session_task should not in done status
                # if it is done, it means the session failed to connect in the most situations
                if self._session_task.done():
                    exception = self._session_task.exception()
                    if exception is None:
                        raise RuntimeError("Session task completed without exception but connection failed")

                    # Handle ExceptionGroup from TaskGroup
                    if exception.__class__.__name__ == "ExceptionGroup" and hasattr(exception, "exceptions"):
                        # Extract the first exception from the group
                        exceptions = getattr(exception, "exceptions", [])
                        if exceptions:
                            exception = exceptions[0]
                            # spec error
                            if isinstance(exception, httpx.HTTPStatusError):
                                raise exception
                    raise RuntimeError(f"Client failed to connect unknown error :{exception}") from exception
                logger.info(f"TMCPServer[{self.name}] initialized")
        except Exception as e:
            logger.exception(f"Error initializing TMCPServer[{self.name}]: {e}")
            await self.cleanup()
            raise e

    @override
    async def connect(self) -> None:
        """connect to the  server if lazy we return directly"""
        if self.cfg.lazy:
            logger.debug(f"TMCPServer[{self.name}] is lazy connect, no need to connect now")
            return
        await self._connect()

    async def reconnect(self) -> None:
        """reconnect to the server"""
        await self.cleanup()
        await self._connect()

    async def _initialize_mcp_server(self) -> None:
        """initialize mcp server"""
        if self._client_session is None:
            raise TMCPServerError(f"TMCPServer[{self.name}] not initialized. Make sure you call `connect()` first.")
        request_id = self.cfg.request_id_factory() if self.cfg.request_id_factory else None
        initialized_notification = InitializedNotification(
            method="notifications/initialized",
            params=NotificationParams(
                _meta=NotificationParams.Meta(
                    **TMCPTransportArgs(
                        request_id=request_id,
                        args=self.cfg.server_args,
                    ).to_dict()
                )
            ),
        )
        await self._client_session.send_notification(
            notification=ClientNotification(initialized_notification),
            related_request_id=request_id,
        )

    async def _ensure_session(self):
        """Get the session, ensuring it is initialized."""
        # 对于非连接池的连接，使用原有逻辑
        if self._client_session is not None:
            return
        if self.cfg.lazy:
            logger.debug(f"TMCPServer[{self.name}] will to lazy connect")
            await self._connect()

    @override
    async def call_tool(
            self, tool_name: str, args: dict[str, Any], _extra: dict[str, Any] | None = None
    ) -> CallToolResult:
        """call tool with automatic reconnection"""
        await self._ensure_session()
        if self._client_session is None:
            raise TMCPServerConnectionError(
                f"TMCPServer[{self.name}] not initialized. Make sure you call `connect()` first.")
        _start = datetime.now()
        args_str = obj_pretty(args)
        logger.info(f"TMCPServer[{self.name}] call tool {tool_name} with args {args_str}")
        request_id = self.cfg.request_id_factory() if self.cfg.request_id_factory else None
        result = await self._client_session.call_tool(
            name=tool_name,
            arguments=TMCPTransportArgs(
                request_id=request_id,
                args=args,
                extra=_extra,
            ).to_dict(),
        )
        result_str = obj_pretty(result)
        cost_time = datetime.now() - _start
        logger.info(
            f"TMCPServer[{self.name}] call tool {tool_name} with args {args_str} result {result_str} cost {cost_time}"
        )
        return result

    @override
    async def list_tools(
            self,
            run_context: RunContextWrapper[Any] | None = None,
            agent: Agent[Any] | None = None,
    ) -> list[MCPTool]:
        return self.cfg.list_tools or []

    @override
    async def cleanup(self):
        """Cleanup the server."""
        try:
            if self._session_task is None:
                return
            async with self._lock:
                if self._session_task is None:
                    return
                self._session_stop_event.set()
                await self._session_task
                self._session_task = None
                logger.debug(f"TMCPServer[{self.name}] has been cleaned up")
        finally:
            # reset session and task
            self._session_ready_event.clear()
            self._session_stop_event.clear()
            self._session_task = None
            self._client_session = None
            self._client_session_initialize_result = None

    @override
    async def list_prompts(
            self,
    ) -> ListPromptsResult:
        """List the prompts available on the server."""
        raise NotImplementedError

    @override
    async def get_prompt(
            self, name: str, arguments: dict[str, Any] | None = None
    ) -> GetPromptResult:
        """Get a specific prompt from the server."""
        raise NotImplementedError

    @override
    async def __aenter__(self):
        """self and connect"""
        await self.connect()  # when parallel initial, this should be removed
        return self

    @override
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()


class TMCPSharedServer(AbstractAsyncContextManager, MCPServer):

    def __init__(self, mcp_server_name: str, config: TMCPServerConfig):
        super().__init__(config.use_structured_content)
        self.mcp_server_name = mcp_server_name
        self.cfg = config
        # 重连同步机制
        self._reconnect_event: asyncio.Event = asyncio.Event()
        self._reconnect_event.set()  # 初始状态为已连接
        self._reconnecting: bool = False
        self._reconnect_lock: asyncio.Lock = asyncio.Lock()

    def _get_share_key(self):
        """Generate a cache key"""
        # meta_str = f"{self.mcp_server_name}:{self.cfg.model_dump_json()}"
        # return self.mcp_server_name + ":" + hashlib.md5(meta_str.encode()).hexdigest()
        return self.mcp_server_name

    @property
    @override
    def name(self) -> str:
        return self.mcp_server_name

    @override
    async def connect(self):
        await _connection_pool.add_server(self._get_share_key(), self)

    @override
    async def list_tools(
            self,
            run_context: RunContextWrapper[Any] | None = None,
            agent: Agent[Any] | None = None,
    ) -> list[MCPTool]:
        return self.cfg.list_tools or []

    @override
    async def cleanup(self):
        pass

    async def get_shared_server(self) -> TMCPServer:
        """Get the session, ensuring it is initialized."""
        server = await _connection_pool.get_server(self._get_share_key())
        if server is None:
            raise TMCPServerError(f"TMCPServer[{self.name}] not in pool!")
        return server

    @override
    async def call_tool(
            self, tool_name: str, args: dict[str, Any], _extra: dict[str, Any] | None = None
    ) -> CallToolResult:
        """call tool with automatic reconnection"""
        try:
            shared_server = await self.get_shared_server()
            try:
                return await shared_server.call_tool(tool_name, args, _extra)
            except (anyio.ClosedResourceError, TMCPServerConnectionError) as e:
                logger.warning(f"TMCPServer[{self.name}] connection error during tool call: {e}")
                # 连接相关的错误，则进行重连
                await self._reconnect(shared_server)
                return await shared_server.call_tool(tool_name, args, _extra)
        except Exception as e:
            logger.exception(f"TMCPServer[{self.name}] failed to call tool {tool_name} with args {args}: {e}")
            return CallToolResult(
                isError=True,
                content=[TextContent(text=f"Error executing mcp tool: {str(e)}", type='text')]
            )

    async def _reconnect(self, server: TMCPServer):
        """重连MCP服务器"""
        # 如果已经在重连中，等待重连完成
        if self._reconnecting:
            logger.debug(f"TMCPServer[{self.name}] waiting for reconnection to complete")
            await self._reconnect_event.wait()
            return

        # 尝试获取重连锁
        async with self._reconnect_lock:
            # 双重检查：如果已经有其他线程完成了重连，直接返回
            if not self._reconnecting:
                self._reconnecting = True
                self._reconnect_event.clear()
                try:
                    logger.info(f"TMCPServer[{self.name}] starting reconnection")
                    await server.reconnect()
                    logger.info(f"TMCPServer[{self.name}] reconnection completed successfully")
                except Exception as e:
                    logger.error(f"TMCPServer[{self.name}] reconnection failed: {e}")
                    raise
                finally:
                    self._reconnecting = False
                    self._reconnect_event.set()
            else:
                # 其他线程等待重连完成
                await self._reconnect_event.wait()

    @override
    async def list_prompts(
            self,
    ) -> ListPromptsResult:
        """List the prompts available on the server."""
        raise NotImplementedError

    @override
    async def get_prompt(self, name: str, arguments: dict[str, Any] | None = None) -> GetPromptResult:
        """Get a specific prompt from the server."""
        raise NotImplementedError

    @override
    async def __aenter__(self):
        """self and connect"""
        await self.connect()  # when parallel initial, this should be removed
        return self

    @override
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()


class MCPServerGuard(AbstractAsyncContextManager):
    def __init__(self, *mcp_servers: MCPServer):
        self._group: deque[MCPServer] = deque()  # for outside visist
        self.exit_stack: AsyncExitStack = AsyncExitStack()  # for context manger

        self._initialized: bool = False
        self._context_entered = False
        self.add_server(*mcp_servers)
        self._lock: asyncio.Lock = asyncio.Lock()  # for cleanup

    async def initialize(self):
        """initialize"""
        if self._initialized:
            return
        if not self._group:
            logger.warning("No server in the guard")
            return
        async with self._lock:
            self._initialized = True
            logger.debug(f"MCPServerGuard will to initialize  {len(self._group)} server")
            await asyncio.gather(
                *[self.exit_stack.enter_async_context(server) for server in self._group]  # type: ignore
            )

    def add_server(self, *mcp_servers: MCPServer):
        if self._initialized:
            raise MCPServerGuardError("Server guard already initialized")
        for server in mcp_servers:
            self._group.append(server)

    @property
    def servers(self) -> tuple[MCPServer, ...]:
        """Returns a tuple of the managed servers."""
        return tuple(self._group)

    async def cleanup(self):
        """Cleanup"""
        logger.debug(f"MCPServerGuard will to cleanup {len(self._group)} server ")
        async with self._lock:
            try:
                await self.exit_stack.aclose()
            except Exception as e:
                logger.error(f"Error cleaning up: {e}")
            finally:
                self._group.clear()

    @override
    async def __aenter__(self):
        """init when enter"""
        if self._context_entered:
            raise MCPServerGuardError("MCPServerGuard instances are not re-entrant.")

        self._context_entered = True
        await self.initialize()
        return self

    @override
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
