import json
from collections.abc import <PERSON><PERSON><PERSON>
from typing import Any, Callable

from agents import Fun<PERSON><PERSON>ool as AgentT<PERSON>
from agents import RunContextWrapper
from mcp.types import CallTool<PERSON><PERSON>ult, TextContent
from mcp.types import Tool as MCPTool

MCP_TOOL_CALL_BACK_TYPE = Callable[[str, dict[str, Any]], Awaitable[str]]


class TMCPHelper:
    @staticmethod
    def mcp_tool_to_agent_tool(mcp_tool: MCPTool, mcp_tool_call_back: MCP_TOOL_CALL_BACK_TYPE) -> AgentTool:
        """
        Convert an MCP tool to an agent tool.
        """

        async def _on_invoke_tool(_ctx: RunContextWrapper[Any], args_str: str) -> str:
            return await mcp_tool_call_back(mcp_tool.name, json.loads(args_str))

        return AgentTool(
            name=mcp_tool.name,
            description=mcp_tool.description or "",
            params_json_schema=mcp_tool.inputSchema,
            on_invoke_tool=_on_invoke_tool,
        )

    @staticmethod
    def extract_text_from_call_tool_result(result: CallToolResult) -> str:
        return "".join(
            [x.text for x in result.content if isinstance(x, TextContent) and x.text is not None and x.text != ""]
        )

    @staticmethod
    def merge_mcp_tool(mcp_tool_target: MCPTool, mcp_tool_source: MCPTool) -> MCPTool:
        """merge source to target"""
        if mcp_tool_source.description:
            mcp_tool_target.description = mcp_tool_source.description
        if mcp_tool_source.inputSchema:
            mcp_tool_target.inputSchema = mcp_tool_source.inputSchema
        if mcp_tool_source.annotations:
            mcp_tool_target.annotations = mcp_tool_source.annotations
        return mcp_tool_target

    @staticmethod
    def merge_mcp_tools(mcp_target_tools: list[MCPTool], mcp_source_tools: list[MCPTool]) -> list[MCPTool]:
        """merge mcp tools

        merge mcp_tools from source to target
        policy:
            if source tools is empty, return target tools directly
            merge based on the defination and size of source tools

        Args:
            mcp_target_tools (list[MCPTool]): target mcp tools
            mcp_source_tools (list[MCPTool]): source mcp tools

        Returns:
            list[MCPTool]: merged tools
        """
        if not mcp_source_tools:
            return mcp_target_tools
        else:
            mcp_target_tools_map = {tool.name: tool for tool in mcp_target_tools}
            result = []
            for mcp_tool_source in mcp_source_tools:
                if mcp_tool_target := mcp_target_tools_map.get(mcp_tool_source.name):
                    result.append(TMCPHelper.merge_mcp_tool(mcp_tool_target, mcp_tool_source))
            return result
