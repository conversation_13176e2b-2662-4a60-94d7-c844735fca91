import logging

from t_ai_mcp import TMCPServer, TMCPServerConfig
from t_ai_mcp.models import TMCPTransportArgs
from t_ai_mcp.server import MCPServerGuard


def test_TMCPTransportArgs():
    args = TMCPTransportArgs(
        args={
            "requirement": "根据数据生成一个带有温度折线图的html",
            "data": "本周天气分别是(单位:摄氏度):10,20,10,12,15,16,17",
        },
    )
    assert args.to_dict() == {
        "requirement": "根据数据生成一个带有温度折线图的html",
        "data": "本周天气分别是(单位:摄氏度):10,20,10,12,15,16,17",
    }


async def test_list_tools():
    async with TMCPServer(
        "basic_test",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
        ),
    ) as server:
        tools = await server.list_tools()
        logging.info(tools)
        assert len(tools) > 0


async def test_call_tool():
    async with TMCPServer(
        "basic_test",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
        ),
    ) as server:
        result = await server.call_tool(
            "create_html",
            {
                "requirement": "根据数据生成一个带有温度折线图的html",
                "data": "本周天气分别是(单位:摄氏度):10,20,10,12,15,16,17",
            },
        )
        logging.info(result)
        assert result is not None


async def test_third_mcp_server():
    """
    test third mcp server
    """
    async with TMCPServer(
        "third_mcp_server_test",
        TMCPServerConfig(
            server_endpoint="https://pymupdf4llm-mcp-server-c7b22d8f.erda.cloud/sse",
            server_args={
                "model": "deepseek-r1",
                "a": "xxxx",
                "b": "yyyy",
            },
            headers={
                "X-Request-ID": "1234567890",
            },
        ),
    ) as server:
        result = await server.call_tool(
            "create_pdf_from_text",
            {
                "filename": "result",
                "text_content": "本周天气分别是(单位:摄氏度):10,20,10,12,15,16,17",
            },
            # _extra={
            #     "c": "zzzz",
            # },
        )
        logging.info(result)
        assert result is not None


async def test_call_tool_lazy():
    mcp_server = TMCPServer(
        "basic_test",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
        ),
    )
    async with mcp_server as server:
        result = await server.call_tool(
            "create_html",
            {
                "requirement": "根据数据生成一个带有温度折线图的html",
                "data": "本周天气分别是(单位:摄氏度):10,20,10,12,15,16,17",
            },
        )
        logging.info(result)
        assert result is not None


async def test_fix_tool_list():
    mcp_server = TMCPServer(
        "basic_test",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
        ),
        lazy_connect=True,
    )
    full_tools = []
    async with mcp_server as server:
        full_tools = await server.list_tools()
    mcp_server.cfg.fix_list_tools = full_tools
    async with mcp_server as server:
        result = await server.call_tool(
            "create_html",
            {
                "requirement": "根据数据生成一个带有温度折线图的html",
                "data": "本周天气分别是(单位:摄氏度):10,20,10,12,15,16,17",
            },
        )
        assert result is not None


async def test_mcp_server_group():
    mcp_server = TMCPServer(
        "basic_test",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
        ),
    )
    async with MCPServerGuard(mcp_server):
        tools = await mcp_server.list_tools()
        logging.info(tools)
        assert len(tools) > 0


async def test_lazy_mcp_server_group():
    a = TMCPServer(
        "a",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
            lazy=True
        )
    )
    b = TMCPServer(
        "b",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
            lazy=True
        )
    )
    c = TMCPServer(
        "c",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
            lazy=True
        ),
    )
    async with MCPServerGuard(a, b, c):
        assert c._client_session is not None
        assert a._client_session is None
        assert b._client_session is None
        tools = await a.list_tools()
        assert a._client_session is not None
        logging.info(tools)
        assert len(tools) > 0


async def test_mcp_server_reusable():
    """
    mcp server can be reused in different contexts(but no concurrent safety)
    """
    a = TMCPServer(
        "a",
        TMCPServerConfig(
            server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
            server_args={},
        ),
        lazy_connect=True,
    )
    async with MCPServerGuard(a):
        assert a._client_session is None
        tools = await a.list_tools()
        assert a._client_session is not None
        assert len(tools) > 0
    assert a._client_session is None
    async with MCPServerGuard(a):
        assert a._client_session is None
        tools = await a.list_tools()
        assert a._client_session is not None
        assert len(tools) > 0


async def test_parallel_mcp_server_group():
    import asyncio
    import random

    async def work(task_id):
        logging.info(f"Task {task_id} started")
        mcp_servers = [
            TMCPServer(
                f"mcp_server_{i}",
                TMCPServerConfig(
                    server_endpoint="https://html-generator-v2-a4b9f776.erda.cloud/sse",
                    server_args={},
                    lazy=True
                ),
            )
            for i in range(10000)
        ]
        # sample 2 servers to make it not lazy
        not_lazy_servers = random.sample(mcp_servers, 2)
        for server in not_lazy_servers:
            server._lazy_connect = False
        async with MCPServerGuard(*mcp_servers):
            # not lazy servers should have established connections
            for server in not_lazy_servers:
                assert server._client_session is not None
            target_lazy_server = None
            for server in mcp_servers:
                if server not in not_lazy_servers:
                    target_lazy_server = server
                    break
            # target lazy server should not have established connection
            assert target_lazy_server is not None
            assert target_lazy_server._client_session is None

            # test list_tools
            assert await not_lazy_servers[0].list_tools() is not None
            assert await target_lazy_server.list_tools() is not None
            # after call list_tools, target lazy server should have established connection
            assert target_lazy_server._client_session is not None

    await asyncio.gather(*[work(task_id) for task_id in range(10)], return_exceptions=True)
