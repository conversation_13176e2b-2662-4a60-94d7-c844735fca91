[project]
name = "t-ai-mcp"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "t-ai-common",
    "pydantic>=2.11.4",
    "mcp[cli]>=1.11.0",
    "openai-agents>=0.2.5",
    "anyio>=4.9.0",
    "httpx>=0.28.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_mcp"]
