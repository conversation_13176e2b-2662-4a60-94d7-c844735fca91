def merge_by_element(original_list: list, current_element: dict):
    for existing in original_list:
        if existing.get("key") == current_element.get("key"):
            # Skip if already exists
            return
    original_list.append(current_element)


def merge_by_list(original_list: list, current_list: list):
    existing_keys = {item["key"] for item in original_list}
    for new_list_item in current_list:
        if new_list_item["key"] not in existing_keys:
            original_list.append(new_list_item)
            existing_keys.add(new_list_item["key"])
