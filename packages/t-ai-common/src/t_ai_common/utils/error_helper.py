class RootCauseError(Exception):
    """一个异常类，其 str() 方法总是返回根源异常的信息"""

    def __str__(self):
        # root cause
        root_cause = self
        while getattr(root_cause, "__cause__", None) is not None:
            root_cause = root_cause.__cause__
        # 返回根源异常的字符串表示
        root_cause_format = f"{type(root_cause).__name__}: {str(root_cause)}"
        return root_cause_format


def raise_root_cause_err(e: Exception):
    raise RootCauseError() from e
