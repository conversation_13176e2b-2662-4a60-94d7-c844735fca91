from typing import Generic, List, Optional, TypeAlias, TypeVar

from mcp import Tool as MCPTool
from pydantic import BaseModel, Field

D = TypeVar("D")


class AIProxyError(BaseModel):
    code: str = Field(description="错误码")
    msg: str = Field(description="错误信息")
    ctx: str = Field(description="错误上下文(一般是错误路径)")

    def format_error(self) -> str:
        return f"{self.code}: {self.msg} at {self.ctx}"


# 创建泛型响应模型
class AIProxyResp(BaseModel, Generic[D]):
    success: bool = Field(description="是否成功")
    data: D = Field(description="数据")
    err: Optional[AIProxyError] = Field(default=None, description="错误信息")

    def check_or_data(self) -> D:
        if not self.success:
            if self.err:
                raise RuntimeError(self.err.format_error())
            else:
                raise RuntimeError("unknown error")
        return self.data


class MCPServerInfo(BaseModel):
    id: str = Field(description="请求ID")
    name: str = Field(description="工具名称")
    version: str = Field(description="工具版本")
    description: Optional[str] = Field(description="工具描述")
    endpoint: str = Field(description="工具SSE端点")
    tools: List[MCPTool] = Field(description="工具列表")  # todo: 要不要用这个？ 还是使用sse 返回的？


class MCPServerInfoList(BaseModel):
    total: int = Field(description="总数")
    items: List[MCPServerInfo] = Field(alias="list", description="mcp server info list")


MCPServerInfoResp: TypeAlias = AIProxyResp[MCPServerInfo]

MCPServerInfoListResp: TypeAlias = AIProxyResp[MCPServerInfoList]
