from typing import Optional

import httpx
from pydantic import BaseModel, Field, ValidationError

from .models import MCPServerInfo, MCPServerInfoList, MCPServerInfoResp


class AIProxyAdminClient(BaseModel):
    endpoint: str = Field(description="ai proxy的端点")
    authorization: str = Field(description="授权")
    timeout: int = Field(description="超时", default=60)

    async def get_mcp_server_info(
        self, mcp_server_name: str, mcp_server_version: Optional[str] = None
    ) -> MCPServerInfo:
        """get_mcp_server_info"""
        try:
            async with httpx.AsyncClient(
                base_url=self.endpoint,
                timeout=self.timeout,
                verify=False,
                headers={"Authorization": self.authorization},
            ) as client:
                url = f"/api/ai-proxy/mcp/servers/{mcp_server_name}"
                if mcp_server_version:
                    url += f"?version={mcp_server_version}"
                r = await client.get(url)
                r.raise_for_status()
                return MCPServerInfoResp.model_validate_json(r.text).check_or_data()
        except ValidationError as e:
            raise RuntimeError(f"get mcp server info,pydantic validation error happened:{e.json()}")
        except Exception as e:
            raise RuntimeError(f"get mcp server info from {self.endpoint} error happened") from e

    async def get_mcp_server_info_list(
        self,
        page_num: int = 1,
        page_size: int = 10,
    ) -> MCPServerInfoList:
        """get mcp server list"""
        pass
