from typing import Dict, List

from loguru import logger
from openai import AsyncOpenAI


class OpenAIClientService:
    """AIProxy 异步调用服务类"""

    @staticmethod
    def get_client(base_url: str, api_key: str, model_id: str) -> AsyncOpenAI:
        """获取配置好的 AsyncOpenAI 客户端实例"""
        return AsyncOpenAI(base_url=base_url, api_key=api_key, default_headers={"X-AI-Proxy-Model-Id": model_id})

    @classmethod
    async def generate_completion(
        cls,
        base_url: str,
        api_key: str,
        model_id: str,
        messages: List[Dict[str, str]],
        temperature: float = 0,
        max_tokens: int = 4096,
    ) -> str:
        """异步生成 AI 补全"""
        try:
            client = cls.get_client(base_url=base_url, api_key=api_key, model_id=model_id)
            response = await client.chat.completions.create(
                model="deepseek-r1",
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API 调用失败: {e}")
            raise
