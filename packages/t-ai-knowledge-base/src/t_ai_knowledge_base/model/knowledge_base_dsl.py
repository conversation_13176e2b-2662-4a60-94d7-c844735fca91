from enum import Enum
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

KNOWLEDGE_BASE_TYPE = "KnowledgeBase"


class ChunkType(str, Enum):
    """文档分块策略类型"""

    AUTOMATIC = "automatic"
    CUSTOM = "custom"
    HIERARCHY = "hierarchy"


class DocumentType(str, Enum):
    """文档类型枚举"""

    MARKDOWN = "markdown"
    TXT = "txt"
    PDF = "pdf"


class FileExtension(str, Enum):
    """文件扩展名枚举"""

    md = "md"
    txt = "txt"
    pdf = "pdf"


class KnowledgeBaseAbstractModel(BaseModel):
    model_config = ConfigDict(populate_by_name=True, validate_default=True, extra="ignore", validate_assignment=True)


class ChunkStrategy(KnowledgeBaseAbstractModel):
    type: ChunkType


class AutomaticChunkStrategy(ChunkStrategy):
    type: ChunkType = ChunkType.AUTOMATIC

    @classmethod
    def from_dict(cls, data: dict):
        return cls.model_validate(data)


class CustomChunkStrategy(ChunkStrategy):
    type: ChunkType = ChunkType.CUSTOM
    separator: str = Field(default="\n")
    max_chunk_size: int = Field(default=800, alias="maxChunkSize")
    chunk_overlap: int = Field(default=80, alias="chunkOverlap")
    remove_extra_spaces: bool = Field(default=False, alias="removeExtraSpaces")
    remove_urls_emails: bool = Field(default=False, alias="removeUrlsEmails")

    @classmethod
    def from_dict(cls, data: dict):
        return cls.model_validate(data)


class HierarchyChunkStrategy(ChunkStrategy):
    type: ChunkType = ChunkType.HIERARCHY
    hierarchy: int = Field(default=3, alias="hierarchy")
    retention_hierarchy_info: bool = Field(False, alias="retentionHierarchyInfo")

    @classmethod
    def from_dict(cls, data: dict):
        return cls.model_validate(data)


def parse_chunk_strategy(data: dict):
    chunk_strategy = data.get("type", ChunkType.AUTOMATIC.value)

    if chunk_strategy == ChunkType.AUTOMATIC.value:
        return AutomaticChunkStrategy.from_dict(data)
    elif chunk_strategy == ChunkType.CUSTOM.value:
        return CustomChunkStrategy.from_dict(data)
    elif chunk_strategy == ChunkType.HIERARCHY.value:
        return HierarchyChunkStrategy.from_dict(data)
    else:
        raise ValueError(f"unknown chunk strategy: {chunk_strategy}")


class KnowledgeBaseDocument(KnowledgeBaseAbstractModel):
    id: Optional[int] = None
    key: Optional[str]
    type: DocumentType
    url: Optional[str] = None
    filename: Optional[str] = None
    file_extension: Optional[FileExtension] = Field(None, alias="fileExtension")
    strategy: ChunkStrategy = Field(default_factory=lambda: AutomaticChunkStrategy())

    def model_post_init(self, __context) -> None:
        """Pydantic v2 的模型后处理方法，确保数据一致性"""
        if self.type:
            # 定义类型与扩展名的映射关系（只定义一次）
            type_to_extension = {
                DocumentType.MARKDOWN: FileExtension.md,
                DocumentType.TXT: FileExtension.txt,
                DocumentType.PDF: FileExtension.pdf,
            }

            # 获取当前类型对应的标准扩展名
            expected_ext = type_to_extension.get(self.type)

            if expected_ext:
                # 如果没有扩展名，设置为标准扩展名
                # 或者如果扩展名与标准不一致，修正为标准扩展名
                if not self.file_extension or self.file_extension != expected_ext:
                    self.file_extension = expected_ext

    @classmethod
    def from_dict(cls, data: dict):
        # 复制数据避免修改原始数据
        data = data.copy()

        # 处理 strategy 字段，根据类型选择正确的子类
        if "strategy" in data and data["strategy"]:
            data["strategy"] = parse_chunk_strategy(data["strategy"])

        # 智能推导 file_extension
        if "fileExtension" not in data and "file_extension" not in data:
            document_type = data.get("type")
            # 根据type自动设置file_extension
            type_to_extension = {
                DocumentType.MARKDOWN.value: FileExtension.md.value,
                DocumentType.TXT.value: FileExtension.txt.value,
                DocumentType.PDF.value: FileExtension.pdf.value,
            }
            if document_type in type_to_extension:
                data["file_extension"] = type_to_extension[document_type]

        return cls.model_validate(data)


def parse_document(data: dict) -> KnowledgeBaseDocument:
    """
    解析文档数据，统一使用 KnowledgeBaseDocument

    不再需要特定的子类，因为父类已经能够处理所有文档类型
    """
    document_type = data.get("type", DocumentType.TXT.value)  # 默认为txt类型

    if document_type in [DocumentType.MARKDOWN.value, DocumentType.TXT.value, DocumentType.PDF.value]:
        return KnowledgeBaseDocument.from_dict(data)
    else:
        raise ValueError(f"unknown document type: {document_type}")


class KnowledgeBaseProperties(KnowledgeBaseAbstractModel):
    documents: list[KnowledgeBaseDocument] = Field(default=[], alias="documents")

    @classmethod
    def from_dict(cls, data: dict):
        # 处理 documents 字段，根据类型选择正确的子类
        if "documents" in data and data["documents"]:
            documents = []
            for document_data in data["documents"]:
                documents.append(parse_document(document_data))
            data["documents"] = documents

        return cls.model_validate(data)


class KnowledgeBaseElement(KnowledgeBaseAbstractModel):
    type: str
    id: Optional[str] = None
    key: str
    name: str
    desc: Optional[str] = None

    @classmethod
    def from_dict(cls, data: dict):
        return cls.model_validate(data)


class KnowledgeBase(KnowledgeBaseElement):
    type: str = KNOWLEDGE_BASE_TYPE
    props: KnowledgeBaseProperties

    @classmethod
    def from_dict(cls, data: dict):
        # 处理 props 字段，使用 KnowledgeBaseProperties.from_dict
        if "props" in data:
            data["props"] = KnowledgeBaseProperties.from_dict(data["props"])

        return cls.model_validate(data)
