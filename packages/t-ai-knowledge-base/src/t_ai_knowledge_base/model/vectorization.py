from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field


class Vectorization(BaseModel):
    model_config = ConfigDict(populate_by_name=True, validate_default=True, extra="ignore", validate_assignment=True)


class SplitResult(Vectorization):
    """文档切片结果模型"""

    document_key: str
    document_name: str = ""
    chunks: List[str] = Field(default_factory=list)
    total_chunks: int = 0
    total_characters: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)
