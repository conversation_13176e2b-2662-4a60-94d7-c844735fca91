from typing import Any, Literal, Optional, Union

from pydantic import BaseModel, ConfigDict, Field
from t_ai_knowledge_base.model.knowledge_base_dsl import KnowledgeBase

KNOWLEDGE_BASE_TYPE = "AgentKnowledgeBase"
RetrieverStrategy = Literal["hybrid", "semantics", "full"]
ReplyMode = Literal["default", "customization"]
KnowledgeBaseDisplayMode = Literal["card", "text"]


class AgentKnowledgeBaseAbstractModel(BaseModel):
    model_config = ConfigDict(populate_by_name=True, validate_default=True, extra="ignore", validate_assignment=True)


class KnowledgeBaseRetriever(AgentKnowledgeBaseAbstractModel):
    strategy: RetrieverStrategy = Field(default="hybrid")
    max_return_count: int = Field(default=5, alias="maxReturnCount")
    min_match_degree: float = Field(default=0.5, alias="minMatchDegree")
    query_rewrite: bool = Field(default=False, alias="queryRewrite")
    result_rerank: bool = Field(default=False, alias="resultRerank")

    @classmethod
    def from_dict(cls, data: dict):
        return cls.model_validate(data)


class KnowledgeBaseReply(AgentKnowledgeBaseAbstractModel):
    reply_mode: ReplyMode = Field("default", alias="replyMode")
    reply_content: Optional[str] = Field(None, alias="replyContent")


class KnowledgeBaseDisplaySource(AgentKnowledgeBaseAbstractModel):
    visible: bool = Field(default=False, alias="visible")


class AgentKnowKnowledgeBase(AgentKnowledgeBaseAbstractModel):
    knowledge_bases: list[KnowledgeBase] = Field([], alias="knowledgeBases")
    retriever: KnowledgeBaseRetriever
    reply: KnowledgeBaseReply
    source: KnowledgeBaseDisplaySource
