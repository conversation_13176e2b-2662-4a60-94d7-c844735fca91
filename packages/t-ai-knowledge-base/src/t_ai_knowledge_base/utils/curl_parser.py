"""
curl命令解析工具

提供解析curl命令并提取HTTP请求头信息的功能。
"""

import shlex
from typing import Dict

from loguru import logger


class CurlParser:
    """
    curl命令解析器

    用于解析curl命令字符串，提取其中的HTTP请求头信息。
    """

    @staticmethod
    def parse_headers(curl_command: str, target_headers: list = None) -> Dict[str, str]:
        """
        解析curl命令并提取指定的请求头信息

        参数:
            curl_command: 完整的curl命令字符串
            target_headers: 需要提取的请求头列表（小写），如果为None则提取所有请求头

        返回:
            包含解析出的请求头的字典
        """
        if target_headers is None:
            target_headers = []

        headers = {}

        try:
            # 使用shlex安全解析curl命令
            tokens = shlex.split(curl_command)

            i = 0
            while i < len(tokens):
                token = tokens[i]

                # 查找 -H 或 --header 参数
                if token in ["-H", "--header"] and i + 1 < len(tokens):
                    header_value = tokens[i + 1]

                    # 解析 "Key: Value" 格式的请求头
                    if ":" in header_value:
                        key, value = header_value.split(":", 1)
                        key = key.strip()
                        value = value.strip()

                        # 如果指定了target_headers，只提取指定的请求头
                        if not target_headers or key.lower() in target_headers:
                            headers[key] = value

                    i += 2

                # 查找 -b 或 --cookie 参数
                elif token in ["-b", "--cookie"] and i + 1 < len(tokens):
                    cookie_value = tokens[i + 1]

                    # 如果target_headers包含'cookie'或为空，则添加Cookie头
                    if not target_headers or "cookie" in target_headers:
                        headers["Cookie"] = cookie_value
                        logger.debug(f"从-b参数解析出Cookie: {cookie_value}")

                    i += 2

                else:
                    i += 1

        except Exception as e:
            logger.warning(f"解析curl命令失败: {e}")
            logger.info("将使用默认的请求头配置")

        return headers

    @staticmethod
    def extract_url(curl_command: str) -> str:
        """
        从curl命令中提取URL

        参数:
            curl_command: 完整的curl命令字符串

        返回:
            提取出的URL，如果解析失败返回空字符串
        """
        try:
            tokens = shlex.split(curl_command)

            # 寻找URL参数（通常是第一个不以-开头的参数，或者在curl后面）
            for i, token in enumerate(tokens):
                if i == 0:  # 跳过 'curl' 命令本身
                    continue
                if not token.startswith("-") and ("http://" in token or "https://" in token):
                    return token.strip("'\"")

        except Exception as e:
            logger.warning(f"从curl命令提取URL失败: {e}")

        return ""

    @staticmethod
    def extract_method(curl_command: str) -> str:
        """
        从curl命令中提取HTTP方法

        参数:
            curl_command: 完整的curl命令字符串

        返回:
            HTTP方法（GET, POST, PUT等），默认返回GET
        """
        try:
            tokens = shlex.split(curl_command)

            i = 0
            while i < len(tokens):
                token = tokens[i]

                # 查找 -X 或 --request 参数
                if token in ["-X", "--request"] and i + 1 < len(tokens):
                    return tokens[i + 1].upper()

                i += 1

        except Exception as e:
            logger.warning(f"从curl命令提取HTTP方法失败: {e}")

        return "GET"


class HttpHeaderBuilder:
    """
    HTTP请求头构建器

    支持从多种来源构建HTTP请求头，包括curl命令解析、环境变量等。
    """

    def __init__(self, base_headers: Dict[str, str] = None):
        """
        初始化请求头构建器

        参数:
            base_headers: 基础请求头字典
        """
        self.base_headers = base_headers or {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
        }

    def build_from_curl(self, curl_command: str, target_headers: list = None) -> Dict[str, str]:
        """
        从curl命令构建请求头

        参数:
            curl_command: curl命令字符串
            target_headers: 需要从curl中提取的请求头列表（小写）

        返回:
            构建好的请求头字典
        """
        headers = self.base_headers.copy()

        if curl_command:
            logger.debug("从curl命令解析请求头")
            parsed_headers = CurlParser.parse_headers(curl_command, target_headers)

            # 将解析出的请求头合并到基础请求头中
            for key, value in parsed_headers.items():
                headers[key] = value
                logger.debug(f"从curl解析出请求头: {key}")

        return headers

    def build_from_env(self, env_mapping: Dict[str, str], defaults: Dict[str, str] = None) -> Dict[str, str]:
        """
        从环境变量构建请求头

        参数:
            env_mapping: 环境变量名到请求头名的映射，如 {"T_AI_COOKIES": "Cookie"}
            defaults: 默认请求头值

        返回:
            构建好的请求头字典
        """
        import os

        headers = self.base_headers.copy()

        # 从环境变量获取
        for env_name, header_name in env_mapping.items():
            value = os.getenv(env_name, "")
            if value:
                headers[header_name] = value

        # 设置默认值
        if defaults:
            for key, value in defaults.items():
                if key not in headers:
                    headers[key] = value

        return headers
