"""
图片URL处理工具

提供图片链接替换、识别和转换等功能。
"""

import hashlib
import os
import re
import time
from typing import List, Optional, Tuple

import httpx
from loguru import logger

from .curl_parser import HttpHeaderBuilder


class ImageUrlProcessor:
    """
    图片URL处理器

    支持检测、替换和转换文档中的图片链接为统一的API格式。
    """

    # 支持的图片格式
    SUPPORTED_IMAGE_EXTENSIONS = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"]

    # 默认的新链接模板 - 从环境变量获取域名
    @classmethod
    def _get_default_template(cls) -> str:
        """从环境变量获取域名并构建默认模板"""
        domain = os.getenv("T_AI_DOMAIN", "t-ai-aigc.app.terminus.io")
        return f"{domain}/api/ai/vector_database/image?uuid={{uuid}}"

    def __init__(self, image_api_template: Optional[str] = None):
        """
        初始化图片URL处理器

        参数:
            image_api_template: 自定义的图片API模板，包含{uuid}占位符
        """
        self.image_api_template = image_api_template or self._get_default_template()

        # 编译正则表达式模式
        self._compile_patterns()

    def _compile_patterns(self):
        """编译正则表达式模式"""
        extensions_pattern = "|".join(self.SUPPORTED_IMAGE_EXTENSIONS)

        self.patterns = [
            # Markdown图片语法: ![alt](url)
            re.compile(rf"!\[([^\]]*)\]\(([^)]+\.(?:{extensions_pattern})(?:\?[^)]*)?)\)", re.IGNORECASE),
            # HTML img标签
            re.compile(
                rf'<img[^>]+src=["\']([^"\']+\.(?:{extensions_pattern})(?:\?[^"\']*)?)["\'][^>]*>', re.IGNORECASE
            ),
            # 直接图片URL (包含中文标点)
            re.compile(
                rf"(^|[\s\n：:,，。.!！?？])(https?://[^\s\n]+\.(?:{extensions_pattern})(?:\?[^\s\n]*)?)",
                re.IGNORECASE | re.MULTILINE,
            ),
        ]

    def calculate_url_hash(self, url: str) -> str:
        """
        计算URL的MD5哈希值

        参数:
            url: 原始图片URL

        返回:
            MD5哈希值的十六进制字符串
        """
        return hashlib.md5(url.encode("utf-8")).hexdigest()

    def generate_new_url(self, original_url: str) -> str:
        """
        生成新的图片API URL

        参数:
            original_url: 原始图片URL

        返回:
            新的图片API URL
        """
        url_hash = self.calculate_url_hash(original_url)
        return self.image_api_template.format(uuid=url_hash)

    def _build_request_headers(self):
        """
        构建HTTP请求头，支持从环境变量中的curl命令解析

        返回:
            构建好的请求头字典
        """
        # 创建请求头构建器
        header_builder = HttpHeaderBuilder()

        # 从环境变量获取curl命令并解析
        curl_command = os.getenv("T_AI_PLATFORM_CURL", "")
        if curl_command:
            # 指定需要从curl中提取的请求头
            target_headers = ["referer", "cookie", "trantor2-app", "trantor2-team"]
            headers = header_builder.build_from_curl(curl_command, target_headers)
        else:
            # 如果没有curl命令，使用基础请求头
            headers = header_builder.base_headers.copy()

        # 设置默认的trantor2请求头（如果没有从curl中解析到）
        if "trantor2-app" not in headers:
            headers["trantor2-app"] = "t_ai_platform"

        if "trantor2-team" not in headers:
            headers["trantor2-team"] = "1"

        # FIXME 先写死
        headers["referer"] = (
            "https://t-ai-portal.app.terminus.io/knowledge-t_ai/knowledge/knowledge$e4556506-aa51-4c23-bd2b-d3fcd02b5737/page?_tab_id=6hEGngBOgKQZ&sceneKey=t_ai_management%24scene_knowledge&viewKey=t_ai_management%24scene_knowledge-list"
        )
        headers["cookie"] = (
            "Hm_lvt_5c04d88621b64442372da67ce63efe4e=1719920032; _ga=GA1.1.618267065.1721222211; _ga_4WZZBBLFXK=GS1.1.1743419881.5.0.1743419881.0.0.0; taid=f4953672-c54f-4862-a8c9-de0ca80cdb80; _ga_GGY77GT80X=GS2.1.s1747884264$o5$g0$t1747884264$j0$l0$h0; emp_cookie=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMmNjMzk2Njc4MDk3NDM5ZGE3NGExYzEzOTYwNmEyZjAiLCJleHBpcmUiOjEyMDk2MDAsInBhdGgiOiIvIiwiZG9tYWluIjoidGVybWludXMuaW8iLCJodHRwT25seSI6dHJ1ZSwic2VjdXJlIjp0cnVlLCJpc3MiOiJpYW0oMi41LjI0LjExMzAuMC1TTkFQU0hPVCkiLCJzdWIiOiJpYW0gdXNlciIsImV4cCI6MTc1MTYyMTU0NiwibmJmIjoxNzUwNDExOTQ2LCJpYXQiOjE3NTA0MTE5NDYsImp0aSI6IjUyZTkwMWQ3Yjk2ZDRjN2RiZTM1M2MzZGFkNTA5OWY1In0.eNZGWNJ5XSHwYqdlnaXCO3hOY_PDXPt5fv0WekjEy00; t_iam_test=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA2MjQxNDE4MjUzNzM0OTRjZWRlYzhjMzE0ZWY4ODM0ZjcyMjVhYzRhMjk4OSIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgyLjUuMjUuMDUzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzUxMDM3OTUyLCJuYmYiOjE3NTA3Nzg3NTIsImlhdCI6MTc1MDc3ODc1MiwianRpIjoiODM4OTQ0MGQyODk0NGJhMzlhYjk1ZGJiNGFlOGNiZmIifQ.2gKfTwV3tEGgb9Oh4ic8Oi5jV4wKtMBdmofQ7iaZZac; trantor_v2_lng=zh-CN; Trantor2-ORIGIN-ORG-ID=; emp_u_c_local=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJsb2dpbiIsInBhdGgiOiIvIiwidG9rZW5LZXkiOiI4ODRiYzgzODcxMWZlMTM1YjJhMWY3OWVhYjljNjVmZTgxOGFkZjM3OGY5YmVjMWMxOGQ0NmQxYWE3N2U3YTdjIiwibmJmIjoxNzUwODkzNzUwLCJkb21haW4iOiJ0ZXJtaW51cy5pbyIsImlzcyI6ImRyYWNvIiwidGVuYW50SWQiOjEsImV4cGlyZV90aW1lIjo2MDQ4MDAsImV4cCI6MTc1MTQ5ODU1MCwiaWF0IjoxNzUwODkzNzUwfQ.2tRrKynV8RmI80lNBv1yD6lBCgJQCRZC9Mnzwg38nzQ; t_iam_staging=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA2MjYxMDIzNDk0NzM1YTBjZGE0MGMzMTM0MTIyOGQzNjQ3MjY2YzMxMGNhYiIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgyLjUuMjUuMDUzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzUxMTY3MzIxLCJuYmYiOjE3NTA5MDgxMjEsImlhdCI6MTc1MDkwODEyMSwianRpIjoiMjE5NmU0M2Q0ZjQ2NDNlNzg0NTRiNzhjZThkZjBjZWQifQ.v2dICJFR67z_C9F7engKL9-ylaVY9yvcCXN-DrZP-5U; terp_iam_dev=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA2MjQxNDAwMjkyMDk4OWUwNzA4M2MyMGE0ZTViOTIwZDA2MzhkMjE5NmNmNyIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgyLjUuMjUuMDUzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzUxMTc4MTAwLCJuYmYiOjE3NTA5MTg5MDAsImlhdCI6MTc1MDkxODkwMCwianRpIjoiNWMzZWVlZGVhMjY2NDA5NThiYjgzOGY5N2U2MDI2MmMifQ.oV-SrKaOX-6yWaRHXpYKfZwkO7l2e7HjkehJAmGwP1g; ai_iam_test=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA2MjMxOTEwMDYyNjgwMmI4OTY4NWRhNDk0YzMwYTM3OTMyZTEyMTNkMWZiZiIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgyLjUuMjUuMDUzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzUxMTg1OTE4LCJuYmYiOjE3NTA5MjY3MTgsImlhdCI6MTc1MDkyNjcxOCwianRpIjoiZmQ1ZDQzMmExMTRjNDVlMGIwOWU1ZjA3ZTg3NjcyY2IifQ.rVxicWkB8BKpGN--5V0P5fTW4b6U0FDLGt3bzWpsvkE; ai_iam_staging=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA2MjYwNzIyMzEwODY1NDJiMTUzN2E5MDk0YzZmYmE4ZGQ4ODZlMTk4YzViZSIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgyLjUuMjUuMDUzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzUxMjAwNTI5LCJuYmYiOjE3NTA5NDEzMjksImlhdCI6MTc1MDk0MTMyOSwianRpIjoiOWEwZTUwOTYzYjNhNGMzNzg4ZjE4M2M4NDM3NjFjMGMifQ.xk5Df40B5gTHrUkWQaeDg5knOXkJZd6zZ851gFX6lvE; t_iam_dev=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA2MjUxMDE2Mzg3NTY4MTU1MmFmYjc0YjY0ODk0YmE4Y2QwMmUyYzE1ZDU5YSIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgwLjEzLjAtU05BUFNIT1QpIiwic3ViIjoiaWFtIHVzZXIiLCJleHAiOjE3NTEyMDEyNDksIm5iZiI6MTc1MDk0MjA0OSwiaWF0IjoxNzUwOTQyMDQ5LCJqdGkiOiI1OTkxZDlkZDY3NDg0YWM0OTFkMDViOTJiN2ZlOTJlMCJ9.vJ9RZoifqwKOFwvAzO_3zGgJkFmo4rrQCFR6kvD0CkA"
        )

        return headers

    def _persist_image_mapping(self, image_url_hash: str, original_url: str) -> bool:
        """
        发送HTTP请求持久化图片URL映射

        参数:
            image_url_hash: 图片的哈希值
            original_url: 原始图片地址链接

        返回:
            是否成功持久化
        """
        try:
            service_key = "t_ai_management$knowledge_base_doc_image_mapping"
            trantor_api_domain = os.getenv("T_AI_PLATFORM_DOMAIN", "https://t-ai-platform-runtime.app.terminus.io")
            trantor_api_image_mapping_url = f"{trantor_api_domain}/api/trantor/service/engine/execute/{service_key}"

            payload = {"params": {"image_key": image_url_hash, "original_hyperlink": original_url}}

            # 使用动态请求头构建方法
            headers = self._build_request_headers()

            start_time = time.time()

            with httpx.Client(verify=False, timeout=300.0) as client:
                response = client.post(trantor_api_image_mapping_url, headers=headers, json=payload)
                response.raise_for_status()
                response_json = response.json()

            logger.debug("image mapping: end. response:{}", response_json)
            logger.debug(f"######## image mapping: end. cost:{round(time.time() - start_time, 2)}")

            if response.status_code == 200:
                logger.info(f"成功持久化图片映射: {image_url_hash} -> {original_url}")
                return True
            else:
                logger.warning(f"持久化图片映射失败，状态码: {response.status_code}, 响应: {response.text}")
                return False

        except httpx.HTTPStatusError as http_error:
            logger.error(f"HTTP状态错误: {http_error}")
            logger.warning("图片映射持久化失败，但不影响URL替换功能")
            return False

        except httpx.ConnectError as conn_error:
            logger.error(f"连接错误: {conn_error}")
            logger.info("网络连接问题，可能是SSL配置或服务器状态导致")
            return False

        except httpx.TimeoutException as timeout_error:
            logger.error(f"请求超时: {timeout_error}")
            logger.info("请求超时，可能是网络延迟或服务器响应慢")
            return False

        except httpx.RequestError as req_error:
            logger.error(f"持久化图片映射时发生网络错误: {req_error}")
            return False
        except Exception as e:
            logger.error(f"持久化图片映射时发生未知错误: {e}")
            return False

    def find_image_urls(self, content: str) -> List[Tuple[str, str, str]]:
        """
        查找内容中的所有图片URL

        参数:
            content: 要搜索的文本内容

        返回:
            [(格式类型, 原始URL, 上下文), ...] 的列表
        """
        found_urls = []

        # Markdown格式
        for match in self.patterns[0].finditer(content):
            alt_text = match.group(1)
            url = match.group(2)
            context = f"![{alt_text}]({url})"
            found_urls.append(("markdown", url, context))

        # HTML格式
        for match in self.patterns[1].finditer(content):
            url = match.group(1)
            context = match.group(0)
            found_urls.append(("html", url, context))

        # 直接URL格式
        for match in self.patterns[2].finditer(content):
            prefix = match.group(1)
            url = match.group(2)
            context = match.group(0)
            found_urls.append(("direct", url, context))

        return found_urls

    def is_image_url(self, url: str) -> bool:
        """
        检查URL是否为图片链接

        参数:
            url: 要检查的URL

        返回:
            如果是图片URL返回True，否则返回False
        """
        if not url.startswith(("http://", "https://")):
            return False

        # 提取文件扩展名
        url_lower = url.lower().split("?")[0]  # 去除查询参数
        for ext in self.SUPPORTED_IMAGE_EXTENSIONS:
            if url_lower.endswith(f".{ext}"):
                return True

        return False

    def replace_image_urls(self, content: str, persistence: bool = False) -> Tuple[str, int]:
        """
        替换内容中的图片链接

        参数:
            content: 原始文档内容
            persistence: 是否持久化图片URL映射到数据库

        返回:
            (替换后的内容, 替换的链接数量)
        """
        if not content:
            return content, 0

        try:
            modified_content = content
            replacements_count = 0

            def process_url_replacement(original_url: str) -> str:
                """处理URL替换的公共逻辑"""
                nonlocal replacements_count
                new_url = self.generate_new_url(original_url)

                # 如果需要持久化，发送HTTP请求
                if persistence:
                    image_url_hash = self.calculate_url_hash(original_url)
                    logger.debug(f"开始持久化图片映射: {original_url} -> {image_url_hash}")

                    persistence_success = self._persist_image_mapping(image_url_hash, original_url)

                    if persistence_success:
                        logger.info(f"持久化成功，使用新URL: {original_url} -> {new_url}")
                        replacements_count += 1
                        return new_url
                    else:
                        return original_url
                else:
                    # 不需要持久化时，直接替换
                    logger.debug(f"无需持久化，直接替换图片链接: {original_url} -> {new_url}")
                    replacements_count += 1
                    return new_url

            # 处理Markdown格式
            def replace_markdown(match):
                alt_text = match.group(1)
                original_url = match.group(2)
                new_url = process_url_replacement(original_url)
                return f"![{alt_text}]({new_url})"

            modified_content = self.patterns[0].sub(replace_markdown, modified_content)

            # 处理HTML格式
            def replace_html(match):
                original_url = match.group(1)
                new_url = process_url_replacement(original_url)
                # 保持原始HTML标签结构，只替换src属性
                return match.group(0).replace(original_url, new_url)

            modified_content = self.patterns[1].sub(replace_html, modified_content)

            # 处理直接URL
            def replace_direct_url(match):
                prefix = match.group(1)  # 空白符或标点
                original_url = match.group(2)  # 实际URL
                new_url = process_url_replacement(original_url)
                return prefix + new_url

            modified_content = self.patterns[2].sub(replace_direct_url, modified_content)

            if replacements_count > 0:
                logger.info(f"已替换 {replacements_count} 个图片链接{', 并已持久化映射关系' if persistence else ''}")

            return modified_content, replacements_count

        except Exception as e:
            logger.error(f"替换图片链接时出错: {e}")
            # 如果替换失败，返回原始内容
            return content, 0

    def get_url_mapping(self, content: str) -> dict:
        """
        获取内容中图片URL的映射关系

        参数:
            content: 文档内容

        返回:
            {原始URL: 新URL} 的映射字典
        """
        mapping = {}
        found_urls = self.find_image_urls(content)

        for _, original_url, _ in found_urls:
            if original_url not in mapping:
                mapping[original_url] = self.generate_new_url(original_url)

        return mapping

    @classmethod
    def create_with_custom_template(cls, template: str) -> "ImageUrlProcessor":
        """
        使用自定义模板创建处理器

        参数:
            template: 自定义的图片API模板，必须包含{uuid}占位符

        返回:
            ImageUrlProcessor实例
        """
        if "{uuid}" not in template:
            raise ValueError("模板必须包含{uuid}占位符")

        return cls(image_api_template=template)

    def get_supported_extensions(self) -> List[str]:
        """
        获取支持的图片扩展名列表

        返回:
            支持的文件扩展名列表
        """
        return self.SUPPORTED_IMAGE_EXTENSIONS.copy()

    def set_image_api_template(self, template: str):
        """
        设置图片API模板

        参数:
            template: 新的图片API模板，必须包含{uuid}占位符
        """
        if "{uuid}" not in template:
            raise ValueError("模板必须包含{uuid}占位符")

        self.image_api_template = template
