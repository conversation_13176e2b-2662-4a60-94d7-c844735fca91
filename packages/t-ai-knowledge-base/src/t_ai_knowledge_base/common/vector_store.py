"""
使用LangChain的向量存储和RAG功能的向量存储管理。
"""

import re
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStore
from langchain_milvus import BM25BuiltInFunction, Milvus
from langchain_openai import OpenAIEmbeddings
from loguru import logger
from t_ai_app import G

from .config import EmbeddingSettings, MilvusSettings
from .vector_search import VectorSearchEngine


class VectorStoreManager:
    """使用LangChain Milvus集成的向量存储管理器。"""

    @staticmethod
    def _sanitize_collection_name(name: str) -> str:
        """
        处理collection名称，使其符合Milvus命名规则。

        特殊处理规则:
        - 如果包含$符号，只保留$符号后面的部分
        - 然后应用标准的Milvus命名规则

        Milvus命名规则:
        - 长度不能超过255个字符
        - 第一个字符必须是字母或下划线
        - 只能包含数字、字母和下划线
        - 不能为空

        参数:
            name: 原始collection名称

        返回:
            处理后符合规则的collection名称
        """
        if not name or not isinstance(name, str):
            return "default_collection"

        # 特殊处理：如果包含$符号，只保留$符号后面的部分
        if "$" in name:
            # 找到最后一个$符号的位置，保留其后面的部分
            dollar_index = name.rfind("$")
            name = name[dollar_index + 1 :]
            logger.debug(f"Found $ symbol, keeping suffix: '{name}'")

        # 如果处理后为空，使用默认名称
        if not name:
            return "default_collection"

        # 移除所有不符合规则的字符，只保留字母、数字和下划线
        sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", name)

        # 确保第一个字符是字母或下划线
        if sanitized and not (sanitized[0].isalpha() or sanitized[0] == "_"):
            sanitized = "_" + sanitized

        # 如果处理后为空，使用默认名称
        if not sanitized:
            sanitized = "default_collection"

        # 限制长度为255个字符
        if len(sanitized) > 255:
            sanitized = sanitized[:255]

        # 确保末尾不是下划线（可选的美化处理）
        sanitized = sanitized.rstrip("_")

        # 如果处理后为空，使用默认名称
        if not sanitized:
            sanitized = "default_collection"

        logger.debug(f"Collection name sanitized: '{name}' -> '{sanitized}'")
        return sanitized

    def __init__(
        self, milvus_settings: Optional[MilvusSettings] = None, embedding_settings: Optional[EmbeddingSettings] = None
    ):
        """
        初始化向量存储管理器。

        参数:
            milvus_settings: Milvus配置，如果为None则使用默认配置
            embedding_settings: 嵌入配置，如果为None则使用默认配置
        """
        self.milvus_settings = milvus_settings or MilvusSettings()
        self.embedding_settings = embedding_settings or EmbeddingSettings()

        # 延迟初始化：不在这里立即创建连接
        self._embeddings = None
        self._connection_args = None

        self._vector_stores: Dict[str, VectorStore] = {}

    @property
    def embeddings(self):
        """延迟初始化嵌入模型"""
        if self._embeddings is None:
            logger.info("正在初始化OpenAI嵌入模型...")
            self._embeddings = OpenAIEmbeddings(
                model=self.embedding_settings.model_name,
                api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
                base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            )
            logger.info("OpenAI嵌入模型初始化完成")
        return self._embeddings

    @property
    def connection_args(self):
        """延迟初始化Milvus连接参数"""
        if self._connection_args is None:
            logger.info("正在准备Milvus连接参数...")
            self._connection_args = {
                "uri": self.milvus_settings.uri,
            }

            # 如果有数据库名称，也添加到连接参数中
            if self.milvus_settings.db_name:
                self._connection_args["db_name"] = self.milvus_settings.db_name
                logger.info(f"使用指定数据库: {self.milvus_settings.db_name}")
            else:
                logger.info("使用Milvus默认数据库")

            logger.info("Milvus连接参数准备完成")
        return self._connection_args

    def get_or_create_collection(self, collection_name: str) -> VectorStore:
        """获取或创建作为LangChain向量存储的Milvus集合。"""
        # 处理collection名称，使其符合Milvus命名规则
        sanitized_name = self._sanitize_collection_name(collection_name)

        if sanitized_name not in self._vector_stores:
            try:
                logger.info(f"正在连接到Milvus集合: {sanitized_name} (原名: {collection_name})")

                # 创建Milvus向量存储（这里才真正建立连接）
                # 根据是否需要混合搜索来决定是否添加BM25内置函数
                try:
                    # 首先尝试创建带BM25内置函数的向量存储，用于混合搜索
                    logger.info(f"尝试创建带BM25内置函数的集合: {sanitized_name}")
                    vector_store = Milvus(
                        embedding_function=self.embeddings,  # 这里会触发嵌入模型的延迟初始化
                        collection_name=sanitized_name,
                        connection_args=self.connection_args,  # 这里会触发连接参数的延迟初始化
                        drop_old=False,  # 不删除现有集合
                        auto_id=True,
                        # 添加BM25内置函数，结合稀疏检索和密集检索
                        builtin_function=BM25BuiltInFunction(
                            input_field_names=["text"],  # 使用text作为输入字段名
                            output_field_names=["sparse_vector"],  # 使用sparse_vector作为输出字段名
                        ),
                        # 当使用BM25内置函数时，需要使用统一的索引参数
                        search_params={"metric_type": "L2"},  # 使用L2度量类型
                    )

                    # 验证集合是否成功创建了稀疏向量字段
                    if hasattr(vector_store, "col") and vector_store.col:
                        try:
                            schema = vector_store.col.schema
                            sparse_fields = [field for field in schema.fields if field.dtype == 101]
                            if sparse_fields:
                                logger.info(
                                    f"成功创建带BM25内置函数的集合: {sanitized_name}，稀疏向量字段: {[f.name for f in sparse_fields]}"
                                )
                            else:
                                logger.warning(f"集合 {sanitized_name} 创建成功但未发现稀疏向量字段")
                        except Exception as verify_e:
                            logger.warning(f"验证稀疏向量字段时出错: {verify_e}")

                except Exception as bm25_error:
                    # 如果BM25内置函数创建失败，创建普通的向量存储
                    logger.warning(f"创建带BM25内置函数的集合失败: {bm25_error}")
                    logger.info(f"尝试创建普通向量存储集合: {sanitized_name}")

                    try:
                        vector_store = Milvus(
                            embedding_function=self.embeddings,  # 这里会触发嵌入模型的延迟初始化
                            collection_name=sanitized_name,
                            connection_args=self.connection_args,  # 这里会触发连接参数的延迟初始化
                            drop_old=False,  # 不删除现有集合
                            auto_id=True,
                            # 不使用BM25内置函数，只使用密集向量
                            search_params={"metric_type": "L2"},  # 使用L2度量类型
                        )
                        logger.info(f"成功创建普通向量存储集合: {sanitized_name}")
                    except Exception as fallback_error:
                        logger.error(f"创建普通向量存储也失败: {fallback_error}")
                        raise fallback_error

                self._vector_stores[sanitized_name] = vector_store
                logger.info(f"成功连接到Milvus集合: {sanitized_name} (原名: {collection_name})")

            except Exception as e:
                error_msg = f"连接Milvus集合失败: {sanitized_name} (原名: {collection_name})"
                logger.error(f"{error_msg}，错误详情: {e}")

                # 提供更友好的错误信息
                if "connection" in str(e).lower() or "timeout" in str(e).lower():
                    raise ConnectionError(f"{error_msg}。请检查Milvus服务是否正常运行，网络连接是否正常。原始错误: {e}")
                elif "authentication" in str(e).lower() or "credential" in str(e).lower():
                    raise ConnectionError(f"{error_msg}。请检查Milvus用户名和密码是否正确。原始错误: {e}")
                else:
                    raise RuntimeError(f"{error_msg}。原始错误: {e}")

        return self._vector_stores[sanitized_name]

    def add_documents(self, collection_name: str, documents: List[Document], document_key: str) -> List[str]:
        """向向量存储添加文档。"""
        try:
            vector_store = self.get_or_create_collection(collection_name)

            # 向元数据添加文档键
            for doc in documents:
                doc.metadata = doc.metadata or {}
                doc.metadata["document_key"] = document_key

            # 向向量存储添加文档
            ids = vector_store.add_documents(documents)
            logger.info(f"向集合{collection_name}添加了{len(documents)}个文档")

            return ids

        except Exception as e:
            logger.error(f"向{collection_name}添加文档时出错: {e}")
            raise

    def search_documents(
        self,
        collection_name: str,
        query: str,
        k: int = 4,
        min_match_degree: Optional[float] = None,
        filter_dict: Optional[Dict[str, Any]] = None,
        use_hybrid: bool = None,
        search_strategy: str = None,
    ) -> List[Document]:
        """在向量存储中搜索文档。

        参数:
            collection_name: 集合名称
            query: 查询文本
            k: 返回的最大文档数
            min_match_degree: 最小匹配程度 (0.01-0.99)，低于此值的结果将被过滤
            filter_dict: 过滤条件
            use_hybrid: 是否使用混合检索模式 (兼容旧参数，已废弃，请使用search_strategy)
            search_strategy: 检索策略，可选值: "hybrid"(混合检索), "semantics"(语义检索), "full"(全文检索)
        """
        try:
            vector_store = self.get_or_create_collection(collection_name)

            # 确定搜索策略：优先使用search_strategy，其次use_hybrid，最后默认为semantics
            if search_strategy is not None:
                # 验证search_strategy参数的有效性
                valid_strategies = {"hybrid", "semantics", "full"}
                if search_strategy not in valid_strategies:
                    raise ValueError(f"无效的搜索策略: {search_strategy}，有效值为: {valid_strategies}")
                final_strategy = search_strategy
            elif use_hybrid is not None:
                # 兼容旧的use_hybrid参数
                final_strategy = "hybrid" if use_hybrid else "semantics"
            else:
                # 默认策略
                final_strategy = "semantics"

            # 获取清理后的collection名称，确保与vector_store使用的名称一致
            sanitized_collection_name = self._sanitize_collection_name(collection_name)

            # 使用VectorSearchEngine执行搜索
            return VectorSearchEngine.search_documents(
                vector_store, sanitized_collection_name, query, k, min_match_degree, filter_dict, final_strategy
            )

        except Exception as e:
            logger.error(f"在集合{collection_name}中搜索时出错: {e}")
            # 添加更详细的错误信息
            import traceback

            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise

    def delete_documents(self, collection_name: str, document_key: str) -> int:
        """按文档键删除文档。"""
        try:
            vector_store = self.get_or_create_collection(collection_name)

            # 检查是否可以直接访问 Milvus 集合对象
            if hasattr(vector_store, "col") and vector_store.col:
                # 直接使用 Milvus 集合的 delete 方法按表达式删除
                expr = f'document_key == "{document_key}"'
                logger.info(f"使用表达式直接删除文档: {expr}")

                try:
                    # 先获取匹配的文档数量，用于返回
                    result = vector_store.col.query(expr=expr, output_fields=["count(*)"])
                    count = len(result) if result else 0

                    # 执行删除操作
                    delete_result = vector_store.col.delete(expr=expr)
                    logger.info(f"删除结果: {delete_result}")

                    # 返回删除的文档数量
                    return count
                except Exception as e:
                    logger.warning(f"使用表达式直接删除失败: {e}，尝试备用方法")

            # 备用方法：使用 search_documents 然后按 ID 删除
            logger.info(f"使用备用方法删除文档键为 {document_key} 的文档")

            # 使用 search_documents 方法查找文档
            search_results = self.search_documents(
                collection_name=collection_name,
                query="",  # 空查询，因为我们只关心过滤条件
                k=1000,  # 大k值以获取所有匹配的文档
                filter_dict={"document_key": {"$eq": document_key}},
                search_strategy="semantics",  # 明确使用语义搜索
            )

            logger.info(f"搜索到 {len(search_results)} 个要删除的文档")

            # 调试：打印搜索结果的元数据
            for i, doc in enumerate(search_results[:3]):  # 只打印前3个文档
                logger.info(f"文档 {i} 元数据: {doc.metadata}")

            # 尝试从文档中提取 ID
            document_ids = []
            for doc in search_results:
                # 尝试多种可能的 ID 字段名称
                doc_id = None
                for id_field in ["id", "pk"]:
                    if id_field in doc.metadata:
                        doc_id = doc.metadata[id_field]
                        break

                # 如果找到 ID，添加到列表
                if doc_id:
                    document_ids.append(doc_id)

            if document_ids:
                # 按 ID 删除
                if hasattr(vector_store, "delete"):
                    vector_store.delete(document_ids)
                    logger.info(f"删除了 {len(document_ids)} 个键为 {document_key} 的文档")
                    return len(document_ids)

            logger.warning(f"未找到键为 {document_key} 的文档需要删除")
            return 0

        except Exception as e:
            logger.error(f"删除键为 {document_key} 的文档时出错: {e}")
            import traceback

            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise

    def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """获取集合统计信息。"""
        try:
            sanitized_name = self._sanitize_collection_name(collection_name)
            vector_store = self.get_or_create_collection(collection_name)

            # 基本统计信息（实现取决于向量存储）
            stats = {"collection_name": sanitized_name, "original_name": collection_name, "status": "connected"}

            # 如果Milvus有统计方法，使用它们
            if hasattr(vector_store, "col") and hasattr(vector_store.col, "num_entities"):
                stats["num_entities"] = vector_store.col.num_entities

            return stats

        except Exception as e:
            sanitized_name = self._sanitize_collection_name(collection_name)
            logger.error(f"获取集合{sanitized_name}的统计信息时出错: {e}")
            return {
                "collection_name": sanitized_name,
                "original_name": collection_name,
                "status": "error",
                "error": str(e),
            }

    def create_retriever(
        self, collection_name: str, search_type: str = "similarity", search_kwargs: Optional[Dict[str, Any]] = None
    ) -> Any:
        """为集合创建LangChain检索器。"""
        vector_store = self.get_or_create_collection(collection_name)

        # 确保search_kwargs包含必要的参数
        default_kwargs = {"k": 4, "param": {"metric_type": "L2"}}  # 添加必要的搜索参数

        # 如果提供了search_kwargs，合并它们
        if search_kwargs:
            # 确保param字段存在
            if "param" not in search_kwargs:
                search_kwargs["param"] = default_kwargs["param"]

            # 如果有filter参数，转换为expr参数
            if "filter" in search_kwargs:
                filter_dict = search_kwargs.pop("filter")
                # 尝试转换过滤字典为Milvus表达式字符串
                if "document_key" in filter_dict:
                    doc_key_filter = filter_dict["document_key"]
                    if isinstance(doc_key_filter, dict) and "$eq" in doc_key_filter:
                        # 单个文档键等于查询
                        search_kwargs["expr"] = f'document_key == "{doc_key_filter["$eq"]}"'
                    elif isinstance(doc_key_filter, dict) and "$in" in doc_key_filter:
                        # 多个文档键IN查询
                        doc_keys = doc_key_filter["$in"]
                        if doc_keys and isinstance(doc_keys, list):
                            # 将列表转换为IN表达式
                            keys_str = ", ".join([f'"{k}"' for k in doc_keys])
                            search_kwargs["expr"] = f"document_key in [{keys_str}]"

            default_kwargs.update(search_kwargs)

        search_kwargs = default_kwargs

        return vector_store.as_retriever(search_type=search_type, search_kwargs=search_kwargs)

    def test_connection(self) -> Dict[str, Any]:
        """
        测试Milvus连接是否正常

        返回:
            包含连接状态信息的字典
        """
        try:
            # 尝试创建一个测试集合来验证连接
            test_collection_name = "connection_test"
            logger.info("开始测试Milvus连接...")

            # 这会触发实际的连接尝试，使用简单的向量存储避免BM25相关问题
            test_vector_store = Milvus(
                embedding_function=self.embeddings,
                collection_name=test_collection_name,
                connection_args=self.connection_args,
                drop_old=True,  # 测试用的，可以删除
                auto_id=True,
                # 测试连接时不使用BM25内置函数，避免复杂性
                search_params={"metric_type": "L2"},  # 使用L2度量类型
            )

            # 如果能到这里说明连接成功，清理测试集合
            if hasattr(test_vector_store, "col") and hasattr(test_vector_store.col, "drop"):
                try:
                    test_vector_store.col.drop()
                    logger.info("已清理测试集合")
                except:
                    logger.warning("清理测试集合失败，但不影响连接测试结果")

            logger.info("Milvus连接测试成功")
            return {
                "status": "success",
                "message": "Milvus连接正常",
                "connection_uri": self.milvus_settings.uri,
                "database": self.milvus_settings.db_name or "default",
            }

        except Exception as e:
            error_msg = f"Milvus连接测试失败: {e}"
            logger.error(error_msg)

            return {
                "status": "failed",
                "message": error_msg,
                "connection_uri": self.milvus_settings.uri,
                "database": self.milvus_settings.db_name or "default",
                "error": str(e),
            }

    def health_check(self) -> Dict[str, Any]:
        """
        执行健康检查，包括连接状态和嵌入模型状态

        返回:
            健康检查结果
        """
        result = {
            "milvus_connection": {"status": "unknown"},
            "embedding_model": {"status": "unknown"},
            "overall_status": "unknown",
        }

        # 检查Milvus连接
        milvus_test = self.test_connection()
        result["milvus_connection"] = milvus_test

        # 检查嵌入模型
        try:
            logger.info("开始测试嵌入模型...")
            test_embedding = self.embeddings.embed_query("test")
            if test_embedding and len(test_embedding) > 0:
                result["embedding_model"] = {
                    "status": "success",
                    "message": "嵌入模型正常",
                    "model_name": self.embedding_settings.model_name,
                    "embedding_dimension": len(test_embedding),
                }
                logger.info("嵌入模型测试成功")
            else:
                raise ValueError("嵌入结果为空")

        except Exception as e:
            error_msg = f"嵌入模型测试失败: {e}"
            logger.error(error_msg)
            result["embedding_model"] = {
                "status": "failed",
                "message": error_msg,
                "model_name": self.embedding_settings.model_name,
                "error": str(e),
            }

        # 确定整体状态
        if result["milvus_connection"]["status"] == "success" and result["embedding_model"]["status"] == "success":
            result["overall_status"] = "healthy"
        else:
            result["overall_status"] = "unhealthy"

        return result
