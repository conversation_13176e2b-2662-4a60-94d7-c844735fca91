"""
使用LangChain内置文本切片器的文本切分功能。
"""

import re
from typing import Any, List

from langchain_core.documents import Document
from langchain_text_splitters import (
    CharacterTextSplitter,
    MarkdownHeaderTextSplitter,
    RecursiveCharacterTextSplitter,
)

from ..model.knowledge_base_dsl import ChunkType


class TextSplitterFactory:
    """基于策略创建LangChain文本切片器的工厂类。"""

    @staticmethod
    def create_splitter(strategy: ChunkType, **kwargs) -> Any:
        """根据策略创建适当的LangChain文本切片器。"""

        if strategy == "automatic":
            # 自动策略使用RecursiveCharacterTextSplitter
            return RecursiveCharacterTextSplitter(
                chunk_size=kwargs.get("max_chunk_size", 800),
                chunk_overlap=kwargs.get("chunk_overlap", 200),
                length_function=len,
                is_separator_regex=False,
            )

        elif strategy == "custom":
            # 自定义策略使用CharacterTextSplitter和自定义分隔符
            separator = kwargs.get("separator", "\n\n")
            return CharacterTextSplitter(
                separator=separator,
                chunk_size=kwargs.get("max_chunk_size", 800),
                chunk_overlap=kwargs.get("chunk_overlap", 100),
                length_function=len,
                is_separator_regex=False,
            )

        elif strategy == "hierarchy":
            # 层次化策略使用MarkdownHeaderTextSplitter
            headers_to_split_on = [
                ("#", "Header 1"),
                ("##", "Header 2"),
                ("###", "Header 3"),
                ("####", "Header 4"),
            ]
            return MarkdownHeaderTextSplitter(
                headers_to_split_on=headers_to_split_on,
                strip_headers=False,
                return_each_line=kwargs.get("return_each_line", False),
            )

        else:
            raise ValueError(f"不支持的切分策略: {strategy}")

    @staticmethod
    def split_text(strategy: ChunkType, content: str, **kwargs) -> List[Document]:
        """使用适当的策略切分文本。"""
        splitter = TextSplitterFactory.create_splitter(strategy, **kwargs)

        if strategy == "hierarchy":
            # 对于markdown，直接切分文本
            return splitter.split_text(content)
        else:
            # 对于其他策略，创建文档
            return splitter.create_documents([content])

    @staticmethod
    def preprocess_content(
        content: str, remove_urls: bool = False, remove_emails: bool = False, clean_whitespace: bool = False
    ) -> str:
        """使用基本清理预处理内容。"""

        if remove_urls:
            content = re.sub(
                r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+", "", content
            )

        if remove_emails:
            content = re.sub(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", "", content)

        if clean_whitespace:
            # 只清理多余的空格和制表符，保留换行符
            content = re.sub(r"[ \t]+", " ", content)  # 多个空格/制表符替换为单个空格
            content = re.sub(r" *\n *", "\n", content)  # 清理换行符前后的空格
            content = content.strip()

        return content
