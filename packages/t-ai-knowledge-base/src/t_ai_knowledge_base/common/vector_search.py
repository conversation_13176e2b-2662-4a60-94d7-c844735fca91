"""
向量检索模块，提供各种向量检索策略和工具。
"""

from typing import Any, Dict, List, Optional

from langchain_core.documents import Document
from loguru import logger


class VectorSearchEngine:
    """向量检索引擎，提供混合检索、语义检索和全文检索功能。"""

    @staticmethod
    def search_documents(
        vector_store,
        collection_name: str,
        query: str,
        k: int = 4,
        min_match_degree: Optional[float] = None,
        filter_dict: Optional[Dict[str, Any]] = None,
        search_strategy: str = "hybrid",
    ) -> List[Document]:
        """在向量存储中搜索文档。

        参数:
            vector_store: 向量存储对象
            collection_name: 集合名称
            query: 查询文本
            k: 返回的最大文档数
            min_match_degree: 最小匹配程度 (0.01-0.99)，低于此值的结果将被过滤
            filter_dict: 过滤条件
            search_strategy: 检索策略，可选值: "hybrid"(混合检索), "semantics"(语义检索), "full"(全文检索)
        """
        try:
            logger.info(f"开始在集合{collection_name}中搜索，查询: '{query[:50]}...'，策略: {search_strategy}")

            # 准备基础搜索参数
            search_kwargs = {"k": k * 2, "param": {"metric_type": "L2"}}  # 获取更多结果以便后续过滤

            # 添加过滤条件
            if filter_dict:
                if "document_key" in filter_dict:
                    doc_key_filter = filter_dict["document_key"]
                    if isinstance(doc_key_filter, dict) and "$eq" in doc_key_filter:
                        # 单个文档键等于查询
                        search_kwargs["expr"] = f'document_key == "{doc_key_filter["$eq"]}"'
                    elif isinstance(doc_key_filter, dict) and "$in" in doc_key_filter:
                        # 多个文档键IN查询
                        doc_keys = doc_key_filter["$in"]
                        if doc_keys and isinstance(doc_keys, list):
                            keys_str = ", ".join([f'"{k}"' for k in doc_keys])
                            search_kwargs["expr"] = f"document_key in [{keys_str}]"

            # 检测向量存储的实际能力
            capabilities = VectorSearchEngine._detect_vector_capabilities(vector_store, collection_name)

            docs_and_scores = None
            if search_strategy == "hybrid" and capabilities["supports_hybrid"]:
                # 混合检索：使用 LangChain 的内置混合搜索
                try:
                    # 计算权重
                    similarity = min_match_degree if min_match_degree is not None else 0.7
                    similarity = max(0.1, min(0.9, similarity))  # 确保在合理范围内

                    # 智能适应权重参数：先尝试双权重，如果失败则尝试单权重
                    search_kwargs["ranker_type"] = "weighted"

                    # 首先尝试双权重参数（密集向量权重，稀疏向量权重）
                    try:
                        search_kwargs["ranker_params"] = {"weights": [similarity, 1.0 - similarity]}
                        logger.info(f"混合检索尝试双权重: [{similarity}, {1.0 - similarity}]")
                        docs_and_scores = vector_store.similarity_search_with_score(query, **search_kwargs)
                        logger.info("双权重混合检索成功")
                    except Exception as inner_e:
                        inner_error_msg = str(inner_e).lower()
                        if "weights param mismatch" in inner_error_msg or "invalid parameter" in inner_error_msg:
                            logger.info(f"双权重参数不匹配，尝试单权重: {inner_e}")
                            # 尝试单权重参数
                            search_kwargs["ranker_params"] = {"weights": [similarity]}
                            logger.info(f"混合检索尝试单权重: [{similarity}]")
                            docs_and_scores = vector_store.similarity_search_with_score(query, **search_kwargs)
                            logger.info("单权重混合检索成功")
                        else:
                            raise inner_e  # 重新抛出非权重相关的错误

                except Exception as e:
                    error_msg = str(e).lower()
                    if "weights param mismatch" in error_msg or "invalid parameter" in error_msg:
                        logger.warning(f"混合检索权重参数不匹配: {e}，集合可能没有稀疏向量，回退到语义检索")
                    else:
                        logger.warning(f"混合检索失败: {e}，回退到语义检索")

                    # 回退到语义检索
                    search_kwargs_fallback = {"k": k * 2, "param": {"metric_type": "L2"}}
                    if "expr" in search_kwargs:
                        search_kwargs_fallback["expr"] = search_kwargs["expr"]
                    docs_and_scores = vector_store.similarity_search_with_score(query, **search_kwargs_fallback)

            elif search_strategy == "full":
                # 全文检索：如果有稀疏向量就优先使用，否则降级为语义检索
                if capabilities["supports_hybrid"]:
                    try:
                        # 智能适应权重参数：先尝试双权重，如果失败则尝试单权重
                        search_kwargs["ranker_type"] = "weighted"

                        # 首先尝试双权重参数，稀疏向量权重更高
                        try:
                            search_kwargs["ranker_params"] = {"weights": [0.1, 0.9]}  # 密集向量，稀疏向量
                            logger.info("全文检索尝试双权重: [0.1, 0.9] (优先稀疏向量)")
                            docs_and_scores = vector_store.similarity_search_with_score(query, **search_kwargs)
                            logger.info("双权重全文检索成功")
                        except Exception as inner_e:
                            inner_error_msg = str(inner_e).lower()
                            if "weights param mismatch" in inner_error_msg or "invalid parameter" in inner_error_msg:
                                logger.info(f"双权重参数不匹配，尝试单权重: {inner_e}")
                                # 尝试单权重参数，使用稀疏向量权重
                                search_kwargs["ranker_params"] = {"weights": [0.9]}
                                logger.info("全文检索尝试单权重: [0.9] (稀疏向量)")
                                docs_and_scores = vector_store.similarity_search_with_score(query, **search_kwargs)
                                logger.info("单权重全文检索成功")
                            else:
                                raise inner_e  # 重新抛出非权重相关的错误
                    except Exception as e:
                        error_msg = str(e).lower()
                        if "weights param mismatch" in error_msg or "invalid parameter" in error_msg:
                            logger.warning(f"全文检索权重参数不匹配: {e}，集合可能没有稀疏向量，回退到语义检索")
                        else:
                            logger.warning(f"全文检索失败: {e}，回退到语义检索")

                        search_kwargs_fallback = {"k": k * 2, "param": {"metric_type": "L2"}}
                        if "expr" in search_kwargs:
                            search_kwargs_fallback["expr"] = search_kwargs["expr"]
                        docs_and_scores = vector_store.similarity_search_with_score(query, **search_kwargs_fallback)
                else:
                    logger.info("没有稀疏向量字段，全文检索降级为语义检索")
                    # 为降级的语义检索创建干净的搜索参数
                    fallback_search_kwargs = {"k": k * 2, "param": {"metric_type": "L2"}}
                    if "expr" in search_kwargs:
                        fallback_search_kwargs["expr"] = search_kwargs["expr"]
                    docs_and_scores = vector_store.similarity_search_with_score(query, **fallback_search_kwargs)
            else:
                # 语义检索：仅使用密集向量
                logger.info("使用语义检索策略")

                # 智能处理语义检索：根据集合能力选择最佳方法
                if capabilities["supports_dense_only"]:
                    # 集合支持纯密集向量搜索
                    semantic_search_kwargs = {"k": k * 2, "param": {"metric_type": "L2"}}
                    if "expr" in search_kwargs:
                        semantic_search_kwargs["expr"] = search_kwargs["expr"]

                    logger.info("使用纯密集向量语义检索")
                    docs_and_scores = vector_store.similarity_search_with_score(query, **semantic_search_kwargs)

                elif capabilities["has_sparse_vector"]:
                    # 集合有稀疏向量但我们只想使用密集向量
                    # 尝试使用单权重（密集向量权重=1.0）来强制只使用密集向量
                    try:
                        semantic_search_kwargs = {
                            "k": k * 2,
                            "param": {"metric_type": "L2"},
                            "ranker_type": "weighted",
                            "ranker_params": {"weights": [1.0]},  # 只使用密集向量
                        }
                        if "expr" in search_kwargs:
                            semantic_search_kwargs["expr"] = search_kwargs["expr"]

                        logger.info("使用单权重强制密集向量语义检索")
                        docs_and_scores = vector_store.similarity_search_with_score(query, **semantic_search_kwargs)

                    except Exception as e:
                        logger.warning(f"单权重语义检索失败: {e}，尝试双权重偏向密集向量")
                        # 如果单权重失败，尝试双权重但完全偏向密集向量
                        try:
                            semantic_search_kwargs = {
                                "k": k * 2,
                                "param": {"metric_type": "L2"},
                                "ranker_type": "weighted",
                                "ranker_params": {"weights": [1.0, 0.0]},  # 密集向量权重=1.0，稀疏向量权重=0.0
                            }
                            if "expr" in search_kwargs:
                                semantic_search_kwargs["expr"] = search_kwargs["expr"]

                            logger.info("使用双权重偏向密集向量语义检索")
                            docs_and_scores = vector_store.similarity_search_with_score(query, **semantic_search_kwargs)

                        except Exception as e2:
                            logger.error(f"所有语义检索方法都失败: {e2}")
                            raise e2

                else:
                    # 默认回退
                    semantic_search_kwargs = {"k": k * 2, "param": {"metric_type": "L2"}}
                    if "expr" in search_kwargs:
                        semantic_search_kwargs["expr"] = search_kwargs["expr"]

                    logger.info("使用默认语义检索")
                    docs_and_scores = vector_store.similarity_search_with_score(query, **semantic_search_kwargs)

            if not docs_and_scores:
                logger.info(f"在{collection_name}中未找到匹配的文档")
                return []

            # 过滤低于最小匹配度的结果
            results = []
            if min_match_degree is not None:
                # 计算分数范围
                scores = [score for _, score in docs_and_scores]
                max_score = max(scores)
                min_score = min(scores)
                score_range = max_score - min_score

                # 根据归一化分数过滤结果
                for doc, score in docs_and_scores:
                    # 归一化分数到 0-1 范围
                    norm_score = (score - min_score) / score_range if score_range > 0 else 0

                    if norm_score >= min_match_degree:
                        # 添加分数到元数据
                        if not doc.metadata:
                            doc.metadata = {}
                        doc.metadata["raw_score"] = float(score)
                        doc.metadata["norm_score"] = float(norm_score)
                        results.append(doc)

                logger.info(
                    f"应用最小匹配度 {min_match_degree}，过滤后剩余 {len(results)}/{len(docs_and_scores)} 个文档"
                )
            else:
                # 不需要过滤，但仍添加分数信息
                scores = [score for _, score in docs_and_scores]
                max_score = max(scores) if scores else 0
                min_score = min(scores) if scores else 0
                score_range = max_score - min_score

                for doc, score in docs_and_scores:
                    norm_score = (score - min_score) / score_range if score_range > 0 else 0
                    if not doc.metadata:
                        doc.metadata = {}
                    doc.metadata["raw_score"] = float(score)
                    doc.metadata["norm_score"] = float(norm_score)
                    results.append(doc)

            # 限制返回数量
            results = results[:k]
            logger.info(f"在{collection_name}中找到{len(results)}个文档")

            return results

        except Exception as e:
            logger.error(f"在集合{collection_name}中搜索时出错: {e}")
            import traceback

            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise

    @staticmethod
    def _check_has_sparse_vector(vector_store) -> bool:
        """检查向量存储是否有稀疏向量字段并且支持混合搜索"""
        if hasattr(vector_store, "col") and vector_store.col:
            try:
                # 检查schema中是否有稀疏向量字段
                schema = vector_store.col.schema
                has_sparse_field = False
                for field in schema.fields:
                    if field.dtype == 101:  # SPARSE_FLOAT_VECTOR
                        has_sparse_field = True
                        break

                if not has_sparse_field:
                    logger.debug("集合schema中没有稀疏向量字段")
                    return False

                # 进一步检查：尝试获取集合信息，验证稀疏向量字段是否真正可用
                try:
                    # 检查集合是否真正支持多向量搜索
                    collection_info = vector_store.col.describe()
                    logger.debug(f"集合信息: {collection_info}")

                    # 检查是否有稀疏向量相关的索引
                    indexes = vector_store.col.indexes
                    sparse_index_exists = False
                    for index in indexes:
                        if hasattr(index, "field_name") and "sparse" in index.field_name.lower():
                            sparse_index_exists = True
                            break

                    if sparse_index_exists:
                        logger.debug("发现稀疏向量索引，支持混合搜索")
                        return True
                    else:
                        logger.debug("未发现稀疏向量索引，可能不支持混合搜索")
                        # 即使没有明确的稀疏索引，也先尝试混合搜索，因为可能使用内置函数
                        return True

                except Exception as e:
                    logger.debug(f"检查集合索引信息时出错: {e}，假设不支持混合搜索")
                    return False

            except Exception as e:
                logger.warning(f"检查稀疏向量字段时出错: {e}")
                return False
        return False

    @staticmethod
    def _detect_vector_capabilities(vector_store, collection_name: str) -> Dict[str, Any]:
        """通过实际测试检测向量存储的真实搜索能力"""
        capabilities = {
            "supports_dense_only": False,
            "supports_hybrid": False,
            "has_sparse_vector": False,
            "optimal_strategy": "semantics",
        }

        try:
            # 首先检查schema
            if hasattr(vector_store, "col") and vector_store.col:
                schema = vector_store.col.schema
                sparse_fields = [field for field in schema.fields if field.dtype == 101]
                if sparse_fields:
                    capabilities["has_sparse_vector"] = True
                    logger.debug(f"集合 {collection_name} 有稀疏向量字段: {[f.name for f in sparse_fields]}")
                else:
                    logger.debug(f"集合 {collection_name} 没有稀疏向量字段")
                    capabilities["optimal_strategy"] = "semantics"
                    capabilities["supports_dense_only"] = True
                    return capabilities

            # 如果有稀疏向量字段，测试实际搜索能力
            if capabilities["has_sparse_vector"]:
                test_query = "test"
                base_params = {"k": 1, "param": {"metric_type": "L2"}}

                # 测试1: 纯语义搜索（只使用密集向量）
                try:
                    vector_store.similarity_search_with_score(test_query, **base_params)
                    capabilities["supports_dense_only"] = True
                    logger.debug(f"集合 {collection_name} 支持纯语义搜索")
                except Exception as e:
                    logger.debug(f"集合 {collection_name} 纯语义搜索失败: {e}")

                # 测试2: 混合搜索（双权重）
                try:
                    hybrid_params = base_params.copy()
                    hybrid_params["ranker_type"] = "weighted"
                    hybrid_params["ranker_params"] = {"weights": [0.7, 0.3]}
                    vector_store.similarity_search_with_score(test_query, **hybrid_params)
                    capabilities["supports_hybrid"] = True
                    capabilities["optimal_strategy"] = "hybrid"
                    logger.debug(f"集合 {collection_name} 支持双权重混合搜索")
                except Exception as e:
                    logger.debug(f"集合 {collection_name} 双权重混合搜索失败: {e}")

                    # 测试3: 单权重搜索
                    try:
                        single_weight_params = base_params.copy()
                        single_weight_params["ranker_type"] = "weighted"
                        single_weight_params["ranker_params"] = {"weights": [1.0]}
                        vector_store.similarity_search_with_score(test_query, **single_weight_params)
                        capabilities["supports_hybrid"] = True
                        capabilities["optimal_strategy"] = "hybrid_single_weight"
                        logger.debug(f"集合 {collection_name} 支持单权重搜索")
                    except Exception as e:
                        logger.debug(f"集合 {collection_name} 单权重搜索也失败: {e}")

            # 确定最优策略
            if capabilities["supports_hybrid"]:
                capabilities["optimal_strategy"] = "hybrid"
            elif capabilities["supports_dense_only"]:
                capabilities["optimal_strategy"] = "semantics"
            else:
                capabilities["optimal_strategy"] = "semantics"  # 默认回退

        except Exception as e:
            logger.warning(f"检测集合 {collection_name} 向量能力时出错: {e}")
            # 默认回退到最安全的模式
            capabilities["supports_dense_only"] = True
            capabilities["optimal_strategy"] = "semantics"

        logger.info(f"集合 {collection_name} 向量能力检测结果: {capabilities}")
        return capabilities
