"""
使用LangChain实现文档切片和检索功能的知识库服务。
注意：检索方法只返回原始文档结果，不包含LLM总结，以便交给agent处理。
"""

import time
from typing import Any, Dict, List, Optional

from langchain_community.document_loaders import PyPDFLoader, WebBaseLoader
from langchain_core.documents import Document
from loguru import logger

from ..common.config import EmbeddingSettings, MilvusSettings
from ..common.text_splitter import TextSplitterFactory
from ..common.vector_store import VectorStoreManager
from ..model.agent_knowledge_base_dsl import KnowledgeBaseRetriever
from ..model.knowledge_base_dsl import KnowledgeBase, KnowledgeBaseDocument
from ..model.vectorization import SplitResult
from ..utils import ImageUrlProcessor


class KnowledgeBaseService:
    """提供文档处理和检索功能的知识库服务。

    注意：此服务专注于文档切分、向量化和检索，不包含LLM总结功能。
    检索结果直接返回原始文档块，以便agent进行后续处理。
    """

    def __init__(
        self, milvus_settings: Optional[MilvusSettings] = None, embedding_settings: Optional[EmbeddingSettings] = None
    ):
        """
        初始化知识库服务。

        参数:
            milvus_settings: Milvus配置，如果为None则使用默认配置
            embedding_settings: 嵌入配置，如果为None则使用默认配置
        """
        # 初始化向量存储管理器
        self.vector_store_manager = VectorStoreManager(
            milvus_settings=milvus_settings, embedding_settings=embedding_settings
        )

        # 默认集合名称
        self.default_collection = "knowledge_base"

    def _load_content_from_url(self, url: str) -> str:
        """根据URL文件类型选择合适的加载器加载内容。"""
        try:
            if not url.startswith(("http://", "https://")):
                raise ValueError(f"不支持的URL格式: {url}，仅支持http://或https://")

            # 根据URL扩展名判断文件类型并调用对应的专用方法
            url_lower = url.lower()

            if url_lower.endswith(".pdf"):
                return self._load_pdf_from_url(url)
            elif url_lower.endswith((".md", ".markdown", ".txt", ".text")):
                return self._load_text_from_url(url)
            else:
                raise ValueError(f"不支持的文件类型: {url}，仅支持 .pdf, .md, .markdown, .txt, .text")

        except Exception as e:
            logger.error(f"从URL {url} 加载内容时出错: {e}")
            raise ValueError(f"无法从URL {url} 加载内容: {e}")

    def _load_pdf_from_url(self, url: str) -> str:
        """直接从URL加载PDF文件并解析内容。"""
        try:
            # 直接使用PyPDFLoader加载URL中的PDF
            loader = PyPDFLoader(url)
            documents = loader.load()

            if not documents:
                raise ValueError("PDF文件解析后无内容")

            return "\n\n".join([doc.page_content for doc in documents])

        except Exception as e:
            logger.error(f"从URL {url} 加载PDF时出错: {e}")
            raise ValueError(f"无法从URL {url} 加载PDF: {e}")

    def _load_text_from_url(self, url: str) -> str:
        """使用WebBaseLoader从URL加载文本内容，能更好地处理编码问题。"""
        try:
            # 使用WebBaseLoader处理复杂编码情况，特别是中文文件名和OSS存储
            loader = WebBaseLoader([url])
            documents = loader.load()

            if not documents:
                raise ValueError(f"从URL {url} 未能加载到任何内容")

            # 合并所有文档内容
            content = "\n\n".join([doc.page_content for doc in documents])

            if not content.strip():
                raise ValueError("文件内容为空")

            return content

        except Exception as e:
            logger.error(f"从URL {url} 加载文本时出错: {e}")
            raise ValueError(f"无法从URL {url} 加载文本: {e}")

    def document_split(self, key: str, document: KnowledgeBaseDocument) -> SplitResult:
        """
        使用LangChain文本切片器将文档内容切分为块。

        参数:
            key: 文档的唯一标识符
            document: 文档元数据和URL

        返回:
            包含切片和元数据的SplitResult
        """
        try:
            # 从文档URL加载内容
            content = self._load_content_from_url(document.url)

            # 替换图片链接
            content, _ = ImageUrlProcessor().replace_image_urls(content, persistence=True)

            if not content.strip():
                logger.warning(f"从{document.url}未加载到内容")
                return SplitResult(
                    document_key=key,
                    document_name=document.filename if hasattr(document, "filename") else "",
                    chunks=[],
                    total_chunks=0,
                    total_characters=0,
                    metadata={},
                )

            # 从document.strategy.type获取策略
            strategy = document.strategy.type if document.strategy else "automatic"

            # 为文本切片器准备参数
            kwargs = {}
            if document.strategy:
                chunk_strategy = document.strategy

                if hasattr(chunk_strategy, "max_chunk_size"):
                    kwargs["max_chunk_size"] = chunk_strategy.max_chunk_size
                if hasattr(chunk_strategy, "chunk_overlap"):
                    kwargs["chunk_overlap"] = chunk_strategy.chunk_overlap
                if hasattr(chunk_strategy, "separator"):
                    kwargs["separator"] = chunk_strategy.separator

                # 为自定义策略应用文本预处理
                if strategy == "custom" and hasattr(chunk_strategy, "remove_urls_emails"):
                    content = TextSplitterFactory.preprocess_content(
                        content,
                        remove_urls=getattr(chunk_strategy, "remove_urls_emails", False),
                        remove_emails=getattr(chunk_strategy, "remove_urls_emails", False),
                        clean_whitespace=getattr(chunk_strategy, "remove_extra_spaces", False),
                    )

            # 使用LangChain切片器切分文本
            chunks = TextSplitterFactory.split_text(strategy, content, **kwargs)

            # 转换为文本块
            text_chunks = []
            for i, chunk in enumerate(chunks):
                if isinstance(chunk, Document):
                    chunk_text = chunk.page_content
                    # 对于层次化切分，添加元数据信息
                    if strategy == "hierarchy" and chunk.metadata:
                        hierarchy_path = self._build_hierarchy_path(chunk.metadata)
                        if hierarchy_path:
                            chunk_text = f"{hierarchy_path}\n\n{chunk_text}"
                else:
                    chunk_text = str(chunk)

                if chunk_text.strip():
                    text_chunks.append(chunk_text)

            logger.info(f"使用{strategy}策略将文档{key}切分为{len(text_chunks)}个块")

            return SplitResult(
                document_key=key,
                document_name=document.filename if hasattr(document, "filename") else "",
                chunks=text_chunks,
                total_chunks=len(text_chunks),
                total_characters=sum(len(chunk) for chunk in text_chunks),
                metadata={
                    "document_key": key,
                    "document_name": document.filename if hasattr(document, "filename") else "",
                    "resource_url": document.url if document.url else "",
                    "source_url": document.url if document.url else "",
                    "timestamp": int(time.time()),
                    "split_strategy": strategy,
                    "original_length": len(content),
                },
            )

        except Exception as e:
            logger.error(f"切分文档{key}时出错: {e}")
            raise

    def _build_hierarchy_path(self, metadata: Dict[str, Any]) -> str:
        """从元数据构建层次路径。"""
        path_parts = []
        for level in ["Header 1", "Header 2", "Header 3", "Header 4"]:
            if level in metadata:
                path_parts.append(metadata[level])
        return " > ".join(path_parts) if path_parts else ""

    def vectorization(self, split_result: SplitResult, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        将文档块存储到向量存储中以供检索。

        参数:
            split_result: 切分操作的结果
            collection_name: 目标集合名称

        返回:
            包含向量化结果的字典
        """
        try:
            collection = collection_name or self.default_collection

            if not split_result.chunks:
                logger.warning(f"文档{split_result.document_key}没有块需要向量化")
                return {
                    "document_key": split_result.document_key,
                    "collection_name": collection,
                    "chunks_added": 0,
                    "status": "no_chunks",
                }

            # 为向量存储创建Document对象
            documents = []
            for i, chunk in enumerate(split_result.chunks):
                doc = Document(
                    page_content=chunk,
                    metadata={
                        "document_key": split_result.document_key,
                        "document_name": split_result.document_name,
                        "chunk_index": i,
                        "source_url": split_result.metadata.get("source_url", ""),
                        "resource_url": split_result.metadata.get("resource_url", ""),
                        "timestamp": split_result.metadata.get("timestamp", int(time.time())),
                        "split_strategy": split_result.metadata.get("split_strategy", ""),
                        **split_result.metadata,
                    },
                )
                documents.append(doc)

            # 添加到向量存储
            doc_ids = self.vector_store_manager.add_documents(collection, documents, split_result.document_key)

            logger.info(f"为文档{split_result.document_key}向量化了{len(documents)}个块")

            return {
                "document_key": split_result.document_key,
                "collection_name": collection,
                "chunks_added": len(documents),
                "document_ids": doc_ids,
                "status": "success",
            }

        except Exception as e:
            logger.error(f"向量化文档{split_result.document_key}时出错: {e}")
            raise

    def retrieval(
        self,
        query: str,
        collection_name: Optional[str] = None,
        k: int = 4,
        min_match_degree: Optional[float] = None,
        document_keys: Optional[List[str]] = None,
        use_hybrid: bool = False,
    ) -> List[Document]:
        """
        使用向量相似度搜索检索相关文档。

        注意：此方法只返回原始检索结果，不进行LLM总结。

        参数:
            query: 搜索查询
            collection_name: 要搜索的集合
            k: 要检索的文档数量
            min_match_degree: 最小匹配程度，值越大表示要求越匹配（同时用作混合检索权重）
            document_keys: 按特定文档键过滤
            use_hybrid: 是否启用混合检索

        返回:
            相关文档列表（原始Document对象，包含page_content和metadata）
        """
        try:
            collection = collection_name or self.default_collection

            # 如果指定了文档键，准备过滤器
            filter_dict = None
            if document_keys:
                filter_dict = {"document_key": {"$in": document_keys}}

            # 搜索文档
            results = self.vector_store_manager.search_documents(
                collection,
                query,
                k=k,
                min_match_degree=min_match_degree,
                filter_dict=filter_dict,
                search_strategy="hybrid" if use_hybrid else "semantics",  # 转换为search_strategy
            )

            logger.info(f"为查询检索到{len(results)}个文档: {query[:50]}...")
            return results

        except Exception as e:
            logger.error(f"检索文档时出错: {e}")
            raise

    def multi_knowledge_base_retrieval(
        self, query: str, knowledge_bases: List[KnowledgeBase], retriever_config: KnowledgeBaseRetriever
    ) -> List[Document]:
        """
        通过分别搜索每个知识库来从多个知识库检索相关文档。

        注意：此方法只返回原始检索结果，不进行LLM总结。

        参数:
            query: 搜索查询
            knowledge_bases: 要搜索的知识库列表
            retriever_config: 检索配置

        返回:
            来自所有知识库的相关文档列表（原始Document对象）
        """
        all_results = []

        # 使用原始查询，不进行查询重写
        search_query = query

        # 分别循环遍历每个知识库
        for knowledge_base in knowledge_bases:
            # 从此知识库提取文档键
            document_keys = []
            if knowledge_base.props and knowledge_base.props.documents:
                for doc in knowledge_base.props.documents:
                    if doc.key:
                        document_keys.append(doc.key)

            if not document_keys:
                logger.warning(f"知识库中未找到文档键: {knowledge_base.key}")
                continue

            # 根据策略对此知识库执行检索
            knowledge_base_results = []

            # 准备过滤条件
            filter_dict = {"document_key": {"$in": document_keys}} if document_keys else None

            # 根据指定的策略进行检索
            if retriever_config.strategy == "hybrid":
                # 混合检索 (语义 + 关键词)
                knowledge_base_results = self.vector_store_manager.search_documents(
                    collection_name=knowledge_base.key,  # 使用知识库key作为collection名称
                    query=search_query,
                    k=retriever_config.max_return_count,
                    min_match_degree=retriever_config.min_match_degree,
                    filter_dict=filter_dict,
                    search_strategy="hybrid",  # 使用新的search_strategy参数
                )
            elif retriever_config.strategy == "semantics":
                # 语义检索 (仅使用密集向量)
                knowledge_base_results = self.vector_store_manager.search_documents(
                    collection_name=knowledge_base.key,  # 使用知识库key作为collection名称
                    query=search_query,
                    k=retriever_config.max_return_count,
                    min_match_degree=retriever_config.min_match_degree,
                    filter_dict=filter_dict,
                    search_strategy="semantics",  # 使用新的search_strategy参数
                )
            elif retriever_config.strategy == "full":
                # 全文检索 (主要使用BM25/稀疏向量)
                try:
                    # 使用VectorStoreManager统一调用，传递search_strategy="full"
                    knowledge_base_results = self.vector_store_manager.search_documents(
                        collection_name=knowledge_base.key,
                        query=search_query,
                        k=retriever_config.max_return_count,
                        min_match_degree=retriever_config.min_match_degree,
                        filter_dict=filter_dict,
                        search_strategy="full",  # 直接指定全文检索策略
                    )
                except Exception as e:
                    logger.error(f"知识库{knowledge_base.key}全文检索失败: {e}")
                    knowledge_base_results = []

            # 将知识库信息添加到元数据
            for doc in knowledge_base_results:
                if doc.metadata is None:
                    doc.metadata = {}
                doc.metadata["knowledge_base_key"] = knowledge_base.key
                doc.metadata["knowledge_base_name"] = knowledge_base.name

            all_results.extend(knowledge_base_results)
            logger.info(f"知识库'{knowledge_base.key}': 检索到{len(knowledge_base_results)}个文档")

        # 限制为max_return_count
        final_results = all_results[: retriever_config.max_return_count]

        logger.info(f"多知识库检索: 从{len(knowledge_bases)}个知识库检索到{len(final_results)}个文档")
        return final_results

    def get_document_chunks(self, document_key: str, collection_name: Optional[str] = None) -> List[Document]:
        """获取特定文档的所有块。"""
        try:
            collection = collection_name or self.default_collection

            # 使用文档键过滤器搜索
            filter_dict = {"document_key": {"$eq": document_key}}
            results = self.vector_store_manager.search_documents(
                collection,
                "",
                k=1000,  # 大k值以获取所有块
                filter_dict=filter_dict,
                search_strategy="semantics",  # 使用语义搜索，避免稀疏向量相关问题
            )

            # 按块索引排序
            results.sort(key=lambda x: x.metadata.get("chunk_index", 0))

            return results

        except Exception as e:
            logger.error(f"获取文档块时出错: {e}")
            raise

    def delete_document(self, document_key: str, collection_name: Optional[str] = None) -> bool:
        """删除文档的所有块。"""
        try:
            collection = collection_name or self.default_collection
            deleted_count = self.vector_store_manager.delete_documents(collection, document_key)
            return deleted_count > 0

        except Exception as e:
            logger.error(f"删除文档时出错: {e}")
            raise

    def get_collection_stats(self, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """获取集合的统计信息。"""
        try:
            collection = collection_name or self.default_collection
            return self.vector_store_manager.get_collection_stats(collection)

        except Exception as e:
            logger.error(f"获取集合统计信息时出错: {e}")
            raise
