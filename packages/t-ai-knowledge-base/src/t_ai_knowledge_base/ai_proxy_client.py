from typing import Dict

from openai import OpenAI
from t_ai_app import G


class AiProxyClient:

    def __init__(self):
        self._model_cache: Dict[str, OpenAI] = {}

    def get_client(self, model_publisher: str | None = None) -> OpenAI:
        if model_publisher is None:
            return OpenAI(
                api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
                base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            )
        if model_publisher in self._model_cache:
            return self._model_cache[model_publisher]
        client = OpenAI(
            api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
            base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            default_headers={"X-AI-Proxy-Model-Publisher": model_publisher},
        )
        self._model_cache[model_publisher] = client
        return client


ai_proxy_client = AiProxyClient()
