[project]
name = "t-ai-knowledge-base"
version = "0.1.0"
description = "Knowledge base management with document splitting and vectorization"
requires-python = ">=3.12"
dependencies = [
    # internal
    "t-ai-app",

    # external
    "pydantic>=2.11.4",
    "pydantic-settings>=2.10.0",
    "loguru>=0.7.3",

    # LangChain ecosystem
    "langchain-text-splitters>=0.2.0",
    "langchain-milvus>=0.1.0,<0.3.0",
    "langchain-core>=0.2.0",
    "langchain-community>=0.2.0",
    "langchain-openai>=0.1.0",
    "openai>=1.96.1",

    # Document loaders dependencies
    "beautifulsoup4>=4.12.0",
    "httpx>=0.28.1",
    "pypdf>=5.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_knowledge_base"]

[tool.uv.sources]
t-ai-app = { workspace = true }
t-ai-common = { workspace = true }
