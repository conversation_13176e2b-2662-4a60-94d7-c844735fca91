[project]
name = "t-ai-chat"
version = "0.1.0"
description = "Chat module"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # internal
    "t_ai_app",
    "t_ai_common",

    # external
    "pydantic>=2.0.0",
    "sqlalchemy>=2.0.0",
    "loguru>=0.7.3",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.build.targets.wheel]
packages = ["src/chat"]
