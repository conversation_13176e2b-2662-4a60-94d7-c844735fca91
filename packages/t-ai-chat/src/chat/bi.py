import json
import os
import time
from decimal import Decimal
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from loguru import logger
from pydantic import BaseModel, Field
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from t_ai_app import G
from t_ai_app.ctx import ReqCtx
from t_ai_common.clients import OpenAIClientService


class BISchemaRequest(BaseModel):
    """
    基于用户 table schema 生成 BI 图表请求

    # tableNames: 表名列表，格式如 [{"database": "terp_staging", "tableName": "sls_so_head_tr", "tableDescription": "销售订单抬头表"}]
    # tableSchemas: 表结构列表，格式如 [{"tableName": "sls_so_head_tr", "schema": "CREATE TABLE sls_so_head_tr (id INT, customer_id INT, product_id INT, quantity INT, order_date DATE)"}]
    # businessRules: 业务规则, 用户在此声明具体的业务逻辑，让 LLM 感知, eg: 实收金额是指含税金额(gross_base_amt);
    # sqlExecutionCallbackUrl: SQL执行回调URL
    # sqlExecutionCallbackToken: SQL执行回调令牌
    """

    user_content: str = Field(..., description="用户输入")
    model_id: Optional[str] = Field(G.APP_SETTING.ai_proxy.ai_proxy_model_id_gpt_4o, description="模型 id")
    table_names: List[Any] = Field(..., description="业务表名称")
    table_schemas: List[Any] = Field(..., description="业务表 schemas")
    business_rules: Optional[List[str]] = Field(None, description="业务规则")
    sql_execution_callback_url: Optional[str] = Field(None, description="sql 执行回调 url")
    sql_execution_callback_token: Optional[str] = Field(None, description="sql 执行回调 token")


class DatabaseService:
    """数据库异步交互服务类"""

    @staticmethod
    def get_db_url_from_request() -> Optional[str]:
        """
        从请求头获取域名，并根据域名构造数据库连接URL的环境变量名，
        然后从环境变量获取数据库连接URL

        Returns:
            Optional[str]: 数据库连接URL，如果无法获取则返回None
        """
        # 从请求头中解析 Referer 获取域名
        referer_value = ReqCtx.get_header().t_ai_source_referer

        # 依据域名拼接环境变量名，并读取数据库连接 URL
        db_url: Optional[str] = None
        if referer_value:
            try:
                # 确保URL有协议前缀
                if not referer_value.startswith(("http://", "https://")):
                    url_to_parse = f"https://{referer_value}"
                else:
                    url_to_parse = referer_value

                parsed_url = urlparse(url_to_parse)
                domain = parsed_url.netloc.split(":")[0]  # 移除可能的端口号

                if domain:
                    # 替换所有的点(.)和破折号(-)为下划线(_)
                    env_var_name = f"{domain.upper().replace('.', '_').replace('-', '_')}_DBURL"
                    db_url = os.getenv(env_var_name)
                    logger.info(f"Using env var '{env_var_name}' for DB connection. Value present: {bool(db_url)}")
            except Exception as e:
                logger.warning(f"Error while deriving DBURL from referer: {e}")

        return db_url

    @staticmethod
    async def execute_sql(db_url: str, sql: str) -> List[Dict[str, Any]]:
        """
        异步执行SQL查询并返回结果

        Args:
            db_url: 数据库连接URL
            sql: 要执行的SQL查询

        Returns:
            List[Dict[str, Any]]: SQL查询的结果，每行数据表示为一个字典

        Raises:
            Exception: 当数据库查询失败时
        """
        logger.info(f"Executing SQL: {sql}")

        try:
            # 将 MySQL URL 转换为异步版本
            # 例如从 mysql+pymysql://... 转换为 mysql+aiomysql://...
            if db_url.startswith("mysql+pymysql://"):
                async_db_url = db_url.replace("mysql+pymysql://", "mysql+aiomysql://")
            elif db_url.startswith("mysql://"):
                async_db_url = db_url.replace("mysql://", "mysql+aiomysql://")
            else:
                # 假设它已经是正确的异步URL或其他类型
                async_db_url = db_url

            engine = create_async_engine(async_db_url)
            async with engine.connect() as connection:
                result = await connection.execute(text(sql))

                # 处理不同版本的SQLAlchemy返回结果
                try:
                    # 尝试使用async风格
                    rows = await result.all()
                except (TypeError, AttributeError):
                    # 如果result.all()不是awaitable或没有all方法，直接使用fetchall
                    if hasattr(result, "fetchall"):
                        rows = result.fetchall()
                    else:
                        # 有些版本可能直接返回结果列表
                        rows = result

                # 获取列名
                if hasattr(result, "keys"):
                    column_names = result.keys()
                else:
                    # 如果result没有keys方法，尝试从第一行获取键
                    column_names = rows[0].keys() if rows else []

                # 将行转换为字典
                return [dict(zip(column_names, row)) for row in rows]
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise


class BIService:
    """BI 服务类，处理 BI 相关功能"""

    @staticmethod
    def build_bi_system_prompt(
        table_names: list, table_schemas: list, business_rules: Optional[List[str]] = None
    ) -> str:
        """
        构建 BI system prompt (同步方法，不涉及 I/O 操作)

        Args:
            table_names: 表名和中文描述映射
            table_schemas: 表结构信息
            business_rules: 自定义业务规则，列表格式，可包含多条规则
                例如: ["采购商与供应商是同一张 member_new 表，通过字段 biz_role 做区分，biz_role 值为 supplier 表示供应商，
                buyer 表示采购商，both 表示既是供应商，也是采购商。供应商表 supplier 通过 mem_id 字段关联到 member_new 表的 id 字段。"]

        Returns:
            str: 构建完成的系统提示词
        """
        prompt_template = r"""
## System Role
Expert SQL generator for MySQL 5.7. Generate executable SQL from user input and database schema.

## Database Information
### Table Definitions
{0}
Format: JSON array of objects with:
- database: Database name
- tableName: Table name (English)
- tableDescription: Table description (Chinese)

### Schema Definitions
{1}
Format: JSON array of objects with:
- tableName: Table name (English)
- schema: CREATE TABLE statement

## Business Rules
{2}

## Database Name Extraction (CRITICAL)
1. For EACH table:
   - Use EXACT database name from "database" field
   - Use EXACT table name from "tableName" field
   - Create tableName → databaseName mapping
   - NEVER use incorrect database names

2. Database Prefix Rules:
   - Always format as `databaseName.tableName`
   - Include database prefix for ALL table references (FROM, JOIN, subqueries)
   - Only use database names provided in input
   - No database name substitutions or omissions

## SQL Generation Process (INTERNAL ONLY)
1. Field Inventory:
   - Extract all fields from schemas
   - Map tables to their fields and database names
   - For each field, extract its comment/description if available
   - Create a mapping of Chinese field descriptions to actual field names
   - Document this mapping for reference: ChineseDescription → FieldName

2. Query Validation:
   - Verify all referenced fields exist in their tables
   - Ensure proper table joining with valid fields
   - Never use fields from one table when querying another
   - Never assume field existence or modify field names
   - For missing fields, use proper alternatives via JOINs

3. Database Name Verification:
   - Prefix every table with correct database name
   - Cross-check database names against Table Definitions

4. Field Selection Verification:
   - When user asks for data using Chinese descriptions (e.g., "询盘供应商数量"), match to the EXACT field with that comment
   - CRITICAL: Pay careful attention to comments in CREATE TABLE statements
   - For example: If "询盘供应商数量" appears in the comment for field "quote_supplier_cnt", use that field exactly
   - DO NOT match Chinese terms to similar-looking fields (e.g., never use "purhcase_cnt" when "quote_supplier_cnt" is the correct field)
   - Check each selected field against the schema to confirm it matches the required Chinese description

5. Final Validation:
   - Verify field existence in appropriate tables
   - Confirm database prefixes for all tables
   - Validate JOIN conditions and field references
   - Verify GROUP BY fields exist in queried tables
   - Double-check that Chinese descriptions are matched to correct field names

## SQL Requirements
- Start output with "SELECT" (no preceding whitespace/comments)
- Use only fields from provided schemas
- Add Chinese aliases to result fields based on their comments
- Avoid SELECT * - query only needed columns
- Include `deleted = 0` when 'deleted' field exists
- Use appropriate date functions for time references
- Apply correct business type filters
- Follow business rules for table relationships
- Keep SQL concise and properly formatted
- STRICTLY follow Business Rules for table relationships and business type mappings
- Apply the exact business type values from Business Type Mapping when filtering
- When choosing fields, ALWAYS match them to the correct Chinese description in the schema comments

## Field Selection Examples (CRITICAL)
User request: "统计询盘供应商数量"
In schema: "quote_supplier_cnt bigint(20) DEFAULT NULL COMMENT '询盘供应商数量'"
CORRECT selection: quote_supplier_cnt
INCORRECT selection: purhcase_cnt (even if it seems similar)

User request: "获取每周中位数金额"
In schema: "quote_total_mid_amount decimal(22, 2) DEFAULT NULL COMMENT '询盘报价中位数金额'"
CORRECT selection: quote_total_mid_amount
INCORRECT selection: Any other amount-related field

## Output Format
1. Raw SQL query only, starting with "SELECT"
2. No schema analysis, explanations, or comments
3. No markdown formatting
4. No content before or after the SQL query

## Examples

### Example 1: Basic Query
User: "统计近6个月的销售订单总数"
```
SELECT COUNT(*) AS 销售订单总数
FROM mydb.sales_orders
WHERE order_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

### Example 2: Business Type Filter
User: "统计半年来招标单发布的数量"
```
SELECT COUNT(*) AS 招标单数量
FROM mydb.purchase
WHERE biz_type in ('bidding', 'formalbidding')
AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

### Example 3: Join Query with Specific Field Selection
User: "统计名为青岛意鑫通市政工程有限公司的采购商近6个月发布询价单的供应商数量"
```
SELECT COUNT(DISTINCT bq.supplier_id) AS 询价供应商数量
FROM mydb.purchase p
JOIN mydb.company c ON p.company_id = c.id
WHERE c.company_name = '青岛意鑫通市政工程有限公司'
AND p.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
AND p.biz_type in ('buyoffer', 'bsbuyoffer');
```

### Example 4: Weekly Statistics Query
User: "统计名为某公司每周采购商询盘供应商数量"
```
SELECT
    ws.biz_week AS 业务发生周数,
    ws.quote_supplier_cnt AS 询盘供应商数量
FROM
    go_centi.bcp_crm_purchase_week_stat ws
JOIN
    bcp_dw.member_new mn ON mn.member_id = ws.buyer_member_id
WHERE
    mn.member_nick = '某公司'
    AND ws.biz_type in ('buyoffer', 'bsbuyoffer')
ORDER BY
    ws.biz_week;
```

Output SQL query only - no additional content.
"""

        # 格式化业务规则
        business_rules_section = ""
        if business_rules:
            business_rules_section = (
                "\n".join(business_rules) if isinstance(business_rules, list) else str(business_rules)
            )

        return prompt_template.format(
            json.dumps(table_names, ensure_ascii=False, indent=2),
            json.dumps(table_schemas, ensure_ascii=False, indent=2),
            business_rules_section,
        )

    @classmethod
    async def generate_sql_based_on_prompt(cls, model_id: str, system_prompt: str, user_content: str) -> str:
        """
        异步根据提示生成 SQL 查询

        Args:
            model_id: 模型 ID
            system_prompt: 系统提示词
            user_content: 用户输入内容

        Returns:
            str: 生成的 SQL 查询

        Raises:
            ValueError: 如果无法从 LLM 响应中提取有效的 SQL
        """
        # 准备消息
        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_content}]

        # 异步调用 AI 获取响应
        response_content = await OpenAIClientService.generate_completion(
            base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
            model_id=model_id,
            messages=messages,
        )

        # 从响应中提取 SQL 语句
        sql = response_content
        if "```" in response_content:
            # 处理代码块格式
            sql_blocks = []
            lines = response_content.split("\n")
            in_code_block = False
            current_block = []

            for line in lines:
                if line.startswith("```"):
                    if in_code_block:
                        sql_blocks.append("\n".join(current_block))
                        current_block = []
                    in_code_block = not in_code_block
                    continue
                if in_code_block:
                    if not line.lower().startswith("sql"):
                        current_block.append(line)

            # 使用第一个非空的 SQL 块
            sql = next((block for block in sql_blocks if block), sql)

        logger.info(f"extracted SQL: {sql}")
        return sql

    @classmethod
    async def generate_echart_options(
        cls, model_id: str, user_content: str, sql: str, sql_result: Any
    ) -> Dict[str, Any]:
        """
        异步基于 SQL、SQL 执行结果和用户输入生成 ECharts 配置选项

        Args:
            model_id: 模型 ID
            user_content: 用户的原始输入
            sql: 执行的 SQL 查询
            sql_result: SQL 查询的结果

        Returns:
            Dict[str, Any]: ECharts 配置选项的 JSON
        """
        # 准备系统提示词
        system_prompt = """
        你是一位数据可视化专家，擅长将SQL查询结果转换为ECharts图表配置。
        请根据提供的SQL查询、查询结果和用户需求，生成最合适的ECharts配置选项。
        只返回有效的JSON格式的ECharts配置，不要包含任何解释或其他文本。
        """

        # 将 Decimal 转换为字符串
        class DecimalEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, Decimal):
                    return str(obj)
                return super().default(obj)

        sql_result_str = json.dumps(sql_result, cls=DecimalEncoder, ensure_ascii=False, indent=2)

        # 准备用户消息
        user_message = f"""
        用户需求: {user_content}

        执行的SQL查询:
        ```sql
        {sql}
        ```

        SQL查询结果:
        ```json
        {sql_result_str}
        ```

        请生成适合展示这些数据的ECharts配置选项。只返回JSON格式的配置，不要包含任何其他文本。
        """

        # 异步调用 AI 获取响应
        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_message}]
        response_content = await OpenAIClientService.generate_completion(
            base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
            model_id=model_id,
            messages=messages,
        )

        # 从响应中提取 JSON
        try:
            # 处理可能的代码块格式
            if "```" in response_content:
                json_content = response_content.split("```")[1]
                if json_content.startswith("json"):
                    json_content = json_content[4:].strip()
            else:
                json_content = response_content

            # 解析 JSON
            echart_options = json.loads(json_content)
            return echart_options
        except Exception as e:
            logger.error(f"Failed to parse ECharts options: {e}")
            # 返回一个基本的错误图表配置
            return {"title": {"text": "数据可视化错误"}, "tooltip": {}, "series": [{"type": "bar", "data": []}]}


async def generate_echart_options_based_on_table_schema(request: BISchemaRequest) -> Any:
    """
    异步基于表结构生成 ECharts 选项

    此函数执行以下步骤:
    1. 生成系统提示词
    2. 异步生成 SQL 查询
    3. 异步执行 SQL 查询
    4. 异步基于 SQL 查询结果生成 ECharts 选项

    Args:
        request: BI 图表请求

    Returns:
        Any: 生成的 ECharts 选项

    Raises:
        ValueError: 当无法获取数据库连接 URL 时
    """
    total_start_time = time.time()

    # 生成系统提示词
    prompt_start_time = time.time()
    system_prompt = BIService.build_bi_system_prompt(
        table_names=request.table_names, table_schemas=request.table_schemas, business_rules=request.business_rules
    )
    prompt_duration = time.time() - prompt_start_time
    logger.info(f"构建系统提示词耗时: {prompt_duration:.2f}秒")

    # 异步生成 SQL 查询
    sql_gen_start_time = time.time()
    sql = await BIService.generate_sql_based_on_prompt(
        model_id=request.model_id, system_prompt=system_prompt, user_content=request.user_content
    )
    sql_gen_duration = time.time() - sql_gen_start_time
    logger.info(f"生成SQL查询耗时: {sql_gen_duration:.2f}秒")

    # 获取数据库连接 URL
    db_url = DatabaseService.get_db_url_from_request()
    if not db_url:
        raise ValueError("无法获取数据库连接URL，请确保请求头中包含正确的域名信息")

    # 异步执行 SQL 查询
    sql_exec_start_time = time.time()
    result = await DatabaseService.execute_sql(db_url, sql)
    sql_exec_duration = time.time() - sql_exec_start_time
    logger.info(f"执行SQL查询耗时: {sql_exec_duration:.2f}秒, 返回{len(result)}行结果")

    # 异步生成 ECharts 选项
    chart_gen_start_time = time.time()
    echart_options = await BIService.generate_echart_options(
        model_id=request.model_id, user_content=request.user_content, sql=sql, sql_result=result
    )
    chart_gen_duration = time.time() - chart_gen_start_time
    logger.info(f"生成ECharts选项耗时: {chart_gen_duration:.2f}秒")

    total_duration = time.time() - total_start_time
    logger.info(
        f"生成图表总耗时: {total_duration:.2f}秒 (SQL生成: {sql_gen_duration:.2f}秒, SQL执行: {sql_exec_duration:.2f}秒, 图表生成: {chart_gen_duration:.2f}秒)"
    )

    return echart_options
