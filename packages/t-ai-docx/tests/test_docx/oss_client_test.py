import alibabacloud_oss_v2 as oss
from loguru import logger
from t_ai_docx.common.utils import get_file_name_with_timestamp_append
from t_ai_docx.oss_client import OSS_BUCKET_CLIENT


def test_simple_oss_client():
    logger.info(OSS_BUCKET_CLIENT.list_prefix())


def test_oss_client_get_object_key():
    url = "https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com/trantor2/portal/TERP/1cc9f859-54cf-4157-be75-de237123850b/通过初筛范例：上海金旅注册资料（该文档中任何一页缺失都表示不通过）.pdf"
    assert (
        "trantor2/portal/TERP/1cc9f859-54cf-4157-be75-de237123850b/通过初筛范例：上海金旅注册资料（该文档中任何一页缺失都表示不通过）.pdf"
        == OSS_BUCKET_CLIENT.get_obj_key_from_url(url)
    )


def test_oss_client_download_file():
    url = "https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com/trantor2/portal/TERP/1cc9f859-54cf-4157-be75-de237123850b/通过初筛范例：上海金旅注册资料（该文档中任何一页缺失都表示不通过）.pdf"
    OSS_BUCKET_CLIENT.download_file_by_url(url, "./")


def test_oss_client_get_put_object():
    text_string = "Hello, OSS!"
    orignal_file_name = "test.txt"
    oss_object_key = OSS_BUCKET_CLIENT.put_object(
        get_file_name_with_timestamp_append(orignal_file_name),
        text_string.encode("utf-8"),
    )
    result = OSS_BUCKET_CLIENT.get_object(oss_object_key)
    assert text_string == result.body.read().decode("utf-8")
