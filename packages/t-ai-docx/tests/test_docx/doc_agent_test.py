import json

from loguru import logger
from t_ai_docx.doc_api.doc_agent import DocxBlankDefineAgent


def test_simple_doc_agent():
    agent = DocxBlankDefineAgent()
    result = agent.define_blank(
        blank_info="""
空缺:
类型: 主文档
段落索引: 36
开始run索引: 7
结束run索引: 7
预计的插入点: 段落 36 run 7
空缺类型: docx下划线格式
空缺文本: '      '
所在段落: '□A：勘察、设计、监理、施工总承包招标代理服务报酬暂定总价为人民币【】元（大写【】元整），其中，增值税税率为【】%，不含增值税价格为人民币【】元，增值税金额为人民币【】元。参照《招标代理服务收费管理暂行办法》(计价格[2002]1980号)计算标准收费,下浮率为      %，计费基数按实际完成招标项目的中标金额为准。'

        """,
        model_json_schema="""
        {
            "合同编号":"abcdefghigk",
            "合同名称":"关于xxx的合同",
            "人民币":123
        }
        """,
    )
    logger.info(result)
    assert result == "123"
