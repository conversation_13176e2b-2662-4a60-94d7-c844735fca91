import os

from docx import Document
from docx.document import Document
from loguru import logger
from t_ai_docx.doc_api import *
from t_ai_docx.doc_api.doc_helper import iter_block_items


def test_inspect_doc():
    test_dir = os.path.dirname(os.path.abspath(__file__))
    doc_path = os.path.join(test_dir, "resources", "空模板-招标代理合同_docx.docx")
    doc = Document(doc_path)
    # text = []
    logger.info("start...")
    inspect_doc(doc)
    logger.info("end...")


def test_blank_finder():
    test_dir = os.path.dirname(os.path.abspath(__file__))
    doc_path = os.path.join(test_dir, "resources", "空模板-招标代理合同_docx.docx")
    finder = DocxBlankFinder.from_doc_path(doc_path)
    # 或者逐个查找并处理空缺
    while True:
        blank = finder.next_blank()
        if not blank:
            break

        print("\n发现空缺:")
        print(blank)


def test_iter_block_items():
    test_dir = os.path.dirname(os.path.abspath(__file__))
    docx_path = os.path.join(test_dir, "resources", "采购招标模板文件（演示删减）.docx")
    doc = Document(docx_path)
    for index, block in enumerate(iter_block_items(doc)):
        print(
            f"Paragraph-{index}: {block.text if isinstance(block, Paragraph) else f'Table-{index}: {len(block.rows)}x{len(block.columns)    }'}"
        )
