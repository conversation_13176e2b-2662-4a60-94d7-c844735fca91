import os
import shutil
import time
from pathlib import Path


def copy_file_with_timestamp_append(src_p: Path) -> Path:
    """
    复制文件并在文件名后添加时间戳,返回新文件的路径
    """
    if not src_p.exists() or not src_p.is_file():
        raise FileNotFoundError(f"File not found: {src_p}")
    target_p = src_p.parent / f"{src_p.stem}_{int(time.time())}{src_p.suffix}"
    shutil.copy2(src_p, target_p)
    return target_p


def get_file_name_with_timestamp_append(file_name: str) -> str:
    name, ext = os.path.splitext(file_name)
    return f"{name}_{int(time.time())}{ext}"
