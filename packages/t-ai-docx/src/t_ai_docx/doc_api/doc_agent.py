import json
import re

from agents import Agent, ModelSetting<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Runner
from agents.models.openai_provider import OpenAIProvider
from loguru import logger
from pydantic import BaseModel, Field

from ..docx_settings import settings

openai_provider = OpenAIProvider(
    api_key=settings.openai_settings.api_key,
    base_url=settings.openai_settings.base_url,
    use_responses=False,
)


MODEL_MAPPING_DICT = {
    "qwen-plus": "[ID:a631af5b-67a5-4578-bf70-5872c0a80a39]",
    "qwen-plus-thinking": "[ID:72b1b3ec-4f18-430d-bfef-8bd8ddb7d612]",
    "claude-sonnet-4": "[ID:d0d3e36a-87ac-4469-9f29-fcc8a679c1d3]",
    "deepseek-r1": "[ID:5679c1e7-1f03-42f2-8c0c-2dd15f135894]",
    "deepseek-v3": "[ID:782efef6-56ce-4f15-beab-1ee66576498d]",
}


class DocxBlankDefineResult(BaseModel):
    reason: str = Field(description="判断依据")
    conclusion: str = Field(description="最终结论值")

    def is_unknown(self) -> bool:
        return self.conclusion == "未知"


class DocxBlankDefineAgent:
    SYSTEM_PROMPT = """
## 职责
你是docx空缺填充助手。
你需要按照[规则]完成用户提供的填空任务

## 规则
你需要按照用户提供的[空缺信息] 以及 [数据模型信息] 思考空缺处需要填什么,并按照[返回格式要求]来返回。

## 返回格式要求
返回值必须按照如下的格式:
<原因>
    {给出判断依据}
</原因>
<结论>
    {给出最终结论值}
</结论>
这里最终结论值如果判断为未知,需要返回 '未知' 字符串；
你需要返回这么一个结构即可,不要做任何反问。
例如：
1. 找到的情况
<原因>
    上下文中提到了合同编号,这里应该是想要我们填写合同编号，提供的数据模版中也有对应合同编号，所以这里应该填合同编号
</原因>
<结论>
    HT-2023-0086
</结论>
2. 没找到的情况
<原因>
    上下文中提到了合同发起人，这里应该是想让我们填写合同发起人，但是提供的数据模版中没有对应合同发起人，所以这里应该是未知
</原因>
<结论>
    未知
</结论>
3.1 一个段落里多个空缺的情况
<原因>
    上下文中有'发布日期：【】年 【】 月',这里好像有多个空缺,不过目前的空缺信息字符开始于5结束于7,判断为第1个空格,第1个空缺要求填一个年份,所以这里应该只需要返回年份
</原因>
<结论>
    2025
</结论>
3.2 一个段落里多个空缺的情况
<原因>
    上下文中有'发布日期：【】年 【】 月',这里好像有多个空缺,不过目前的空缺信息字符开始于9结束于11,判断为第2个空格, 第2个空缺要求填一个月份,所以这里应该只需要返回月份
</原因>
<结论>
    06
</结论>

## 文档主题
一般为招标 合同类文档填写。

##空缺信息
目前系统中只有两种空缺: 段落中空缺 以及 表格中空缺。
一个段落中可能会有多个空缺,这个时候你需要通过<空缺在段落中的位置>等位置信息来进一步判断真正需要的是哪个空缺。
请注意你一次只需要处理一个空缺。
段落中空缺的样例如下:
    以下是空缺在文档中的信息:
    空缺位置: 段落中空缺
    空缺类型: docx方括号字符
    位于段落: 5
    跨越run1-1
    这是段落中的第1个空缺，字符开始于5结束于7
    空缺的文本: '【】'
    空缺文本的上下文: '：【】'
    空缺所在段落文本: '招标标题：【】'
段落中空缺特点：
    1. 段落中空缺信息中会交代空缺的位置 空缺的内容 以及空缺的上下文等信息。
    2. 空缺上下文中最重要的信息为<空缺在段落中的位置> 以及 <空缺所在段落文本> 这两个信息, 其他信息作辅助判断。
表格中空缺的样例如下:
    以下是空缺在文档中的信息:
    空缺位置: 表格中空缺
    空缺类型: docx下划线格式
    位于表格: 47
    第40行
    第2单元格
    第0个段落
    跨越run3-4
    这是段落中的第1个空缺，字符开始于0结束于5
    当前单元格的上下文: 当前单元格[40,2]: 年     月    日   13	时30分 | 上: 招标人名称：上海某地产开发有限公司
    招标人地址：上海市杨浦区某某路123号4楼
    招标项目名称：上海某地产开发有限公司2022-2024年度工程灯具（国产）采购投标文件
    在     年   月   日   时   分（即开标时间）前不得开启
    投标人:
    投标文件电子版封套上还应清楚地标明“电子版”字样。
    商务标与技术标、正本与副本分开包装的，还应清楚地标明“商务标”或“技术标”，“正本”或“副本”字样。 | 左: 投标截止时间（开标时间） | 右: 无 | 下: 上海市浦东新区某某路222号4楼
    空缺的文本: '     '
    空缺文本的上下文: '     '
    空缺所在段落文本: '      年     月    日   13	时30分'
表格中空缺特点：
    1. 表格中空缺信息中会交代空缺的位置 空缺的内容 以及空缺的上下文等信息。
    2. 空缺上下文中最重要的信息为<空缺在段落中的位置> <空缺所在段落文本> 以及 <当前单元格的上下文> 这三个信息, 其他信息作辅助判断。
    3. <当前单元格的上下文> 是表格空缺的特点 它会告诉你当前单元格的前后左右信息，请根据这些信息综合判断。


##  数据模型样例
数据模型数据符合标准json格式
例如:
{
"合同编号": "HT-2023-0086",
"项目名称": "上海虹桥国际商务区综合体开发项目",
"工程地点": "上海市闵行区申长路168号",
"工程规模": "总建筑面积25万平方米，包含2栋200米超甲级写字楼、商业综合体及配套设施",
"招标规模": "勘察、设计、监理、施工总承包及12项专业分包工程",
"总投资额": "人民币48.9亿元",
"合同价款选择": "A",
"暂定总价": "2850000",
"大写金额": "贰佰捌拾伍万元整",
"增值税税率": "6%",
"不含税价": "2688679.25",
"增值税额": "161320.75",
"下浮率": "35%",
}
    """

    def __init__(self, model_name: str = "qwen-plus"):
        self.agent = Agent(
            name="docx_blank_definer",
            instructions=self.SYSTEM_PROMPT,
        )
        self.run_config = RunConfig(
            model_provider=openai_provider,
            model_settings=ModelSettings(
                temperature=0.0,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                top_p=1,
                max_tokens=4096,
            ),
            model=MODEL_MAPPING_DICT[model_name],
        )
        logger.info(f"使用模型: {model_name} {MODEL_MAPPING_DICT[model_name]}")

    def define_blank(self, blank_info: str, model_json_schema: str) -> DocxBlankDefineResult:
        """
        根据blank_info 和 model_json_schema 定义blank
        Args:
            blank_info (dict): 空白信息
            model_json_schema (str): 数据模型
        Returns:
            str: 定义的blank
        """
        user_prompt = f"""
        ## 任务
        请根据空缺的信息,以及数据模型来判断并填写空缺的值。
        ## 空缺信息
        {blank_info}

        ## 数据模型
        {model_json_schema}
        """
        agent_result = Runner.run_sync(
            self.agent,
            input=user_prompt,
            max_turns=10,
            run_config=self.run_config,
        )
        return self.extract_LLM_result(str(agent_result.final_output))

    def extract_LLM_result(self, agent_result: str) -> DocxBlankDefineResult:
        """
        从LLM的返回结果中提取出DocxBlankDefineResult
        """
        logger.info(f"LLM返回结果: {agent_result}")
        reason = "unknown"
        conclusion = "unknown"
        try:
            if reason_match := re.search(r"<原因>(.*?)</原因>", agent_result, re.DOTALL):
                reason = reason_match.group(1).strip()
            if conclusion_match := re.search(r"<结论>(.*?)</结论>", agent_result, re.DOTALL):
                conclusion = conclusion_match.group(1).strip()
        except Exception as e:
            logger.error(f"提取LLM返回结果失败: {e}")
        return DocxBlankDefineResult(reason=reason, conclusion=conclusion)
