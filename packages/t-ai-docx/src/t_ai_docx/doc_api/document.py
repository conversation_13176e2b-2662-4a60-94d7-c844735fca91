import re

from docx import Document
from docx.text.paragraph import Paragraph
from loguru import logger


def inspect_doc(doc):
    """inspect doc"""
    for index, paragraph in enumerate(doc.paragraphs):
        # text.append(paragraph.text)
        logger.info(f"p-{index}: {paragraph.text}")
        for run in paragraph.runs:
            run.underline
            # inf

    for tb_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idex, cell in enumerate(row.cells):
                text_in_cell = ""
                for paragraph in cell.paragraphs:
                    # text.append(paragraph.text)
                    text_in_cell += paragraph.text
                logger.info(f"tb-{tb_idx}, row-{row_idx}, cell-{cell_idex}: {text_in_cell}")
