import io
import tempfile
from datetime import datetime
from optparse import Option
from pathlib import Path
from typing import Callable, Optional, TypeAlias

from cmi_docx import add_comment
from docx import Document
from loguru import logger
from pydantic import BaseModel, Field

from ..common.utils import (
    copy_file_with_timestamp_append,
    get_file_name_with_timestamp_append,
)
from ..oss_client import OSS_BUCKET_CLIENT
from .doc_agent import DocxBlankDefineAgent, DocxBlankDefineResult
from .doc_blank_finder import BlankInfo, DocxBlankFinder

FILL_WRAP_FN: TypeAlias = Callable[[str], str]


def default_fill_wrap_fn(text: str) -> str:
    return f"AI:{text}"


class DocxFillProgress(BaseModel):
    total: int = Field(description="总空缺数")
    current: int = Field(default=0, description="当前空缺数")
    speed: float = Field(default=0.0, description="填充速度: 多少秒1个空缺")
    start_time: datetime = Field(description="填充时间")
    cost_time: float = Field(default=0.0, description="填充耗时")

    def step(self):
        self.current += 1
        self.cost_time = (datetime.now() - self.start_time).total_seconds()
        self.speed = round(self.cost_time / self.current, 2)

    def report(self) -> str:
        return f"已填充{self.current}/{self.total}个空缺, 填充速度:{self.speed}秒/个, 耗时:{self.cost_time}秒"

    def is_finished(self) -> bool:
        return self.current >= self.total


class DocxOperator:
    def __init__(
        self,
        docx_path: str,
        model_json_schema: str,
        can_fill_unknown: bool = True,
        is_add_comment: bool = True,
        model_name: str = "qwen-plus",
        fill_wrap_fn: FILL_WRAP_FN = default_fill_wrap_fn,
    ) -> None:
        """
        初始化docx操作器

        Args:
            docx_path (str): docx文件的路径
            model_json_schema (str): 数据模型的定义和数据部分
        """
        if docx_path.startswith("http"):
            # 网络的oss路径
            oss_key = OSS_BUCKET_CLIENT.get_obj_key_from_url(docx_path)
            result = OSS_BUCKET_CLIENT.get_object(oss_key)
            file_name = OSS_BUCKET_CLIENT.get_oss_object_name(result, oss_key)
            if file_name is None:
                file_name = "未知.docx"
            self.target_file_name = get_file_name_with_timestamp_append(file_name)
            if result.body is None:
                raise ValueError("文件内容为空")
            docx_data = result.body.read()
            self.doc = Document(io.BytesIO(docx_data))
            self.is_oss_doc = True
            self.docx_path = docx_path
        else:
            # 本地路径
            self.is_oss_doc = False
            self.docx_path = str(copy_file_with_timestamp_append(Path(docx_path)))
            self.doc = Document(self.docx_path)
        # 组合finder
        self.doc_blank_finder = DocxBlankFinder(self.doc)
        # 组合agent
        self.doc_blank_define_agent = DocxBlankDefineAgent(model_name=model_name)
        self.model_name = model_name

        # config
        self.model_json_schema = model_json_schema
        self.can_fill_unknown = can_fill_unknown
        self.is_add_comment = is_add_comment
        self.stop_flag = False
        self.fill_wrap_fn = fill_wrap_fn

    def find_next_blank(self) -> Optional[BlankInfo]:
        """
        找到下一个空白位置 主要由finder完成

        todo：目前的对象是一个dict 可以考虑变为model
        {
            'position': {
                'in_table': True,
                'table_index': t_idx,
                'row_index': r_idx,
                'cell_index': c_idx,
                'paragraph_index': p_idx,
                'start_run_index': blank_info['start_run_index'],
                'end_run_index': blank_info['end_run_index']
            },
            'insertion_point': {
                'table_index': t_idx,
                'row_index': r_idx,
                'cell_index': c_idx,
                'paragraph_index': p_idx,
                'run_index': blank_info['end_run_index']
            },
            'context': {
                'paragraph_text': paragraph.text,
                'blank_text': blank_info['text']
            },
            'type': blank_info['type'],  # 添加空缺类型
            'next_position': {
                'in_table': True,
                'table_index': t_idx,
                'row_index': r_idx,
                'cell_index': c_idx,
                'paragraph_index': p_idx,
                'run_index': blank_info['end_run_index'] + 1
            }
        }
        """
        return self.doc_blank_finder.next_blank()

    def define_blank_val(self, blank_info: BlankInfo) -> DocxBlankDefineResult:
        """
        根据blank_info 和 model_json_schema 找到blank_info对应的value

        这里使用LLM来完成

        Args:
            blank_info (dict): 空白信息
        Returns:
            str: 找到的value
        """
        return self.doc_blank_define_agent.define_blank(
            blank_info=str(blank_info), model_json_schema=self.model_json_schema
        )

    def fill_blank(self, blank_info: BlankInfo, value: DocxBlankDefineResult):
        """
        根据blank_info 和 value 填写空白处
        Args:
            blank_info (dict): 空白信息
            value (str): 填充的值
        """
        return self.doc_blank_finder.fill_blank(
            blank_info=blank_info,
            value=self.fill_wrap_fn(value.conclusion),
            reason=value.reason,
            is_add_comment=self.is_add_comment,
        )

    def get_docx_path(self):
        oss_object_key = OSS_BUCKET_CLIENT.get_default_oss_key(self.target_file_name)
        return OSS_BUCKET_CLIENT.get_object_url(oss_object_key)

    def save(self) -> str:
        """
        就地保存文档
        """
        if self.is_oss_doc:
            # 网络的oss路径
            docx_stream = io.BytesIO()
            self.doc.save(docx_stream)
            oss_object_key = OSS_BUCKET_CLIENT.put_object(
                self.target_file_name,
                docx_stream.getvalue(),
            )
            return OSS_BUCKET_CLIENT.get_object_url(oss_object_key)
        else:
            self.doc.save(self.docx_path)
            return self.docx_path

    def stop(self):
        """
        停止填充
        """
        self.stop_flag = True

    def reset_fill_progress(self, total: int):
        """
        重置填充进度
        """
        self.fill_progress = DocxFillProgress(
            total=total,
            current=0,
            speed=0.0,
            start_time=datetime.now(),
            cost_time=0.0,
        )

    def step_fill_progress(self):
        self.fill_progress.step()

    def report_fill_progress(self):
        logger.info(self.fill_progress.report())

    def get_fill_progress(self) -> DocxFillProgress:
        return self.fill_progress

    def get_config(self) -> dict:
        return {
            "model_name": self.model_name,
        }

    @staticmethod
    def calculate_blank_indexes_in_paragraphs(blank_infos: list[BlankInfo]) -> None:
        """
        为空缺列表批量计算每个空缺在段落中的索引

        Args:
            blank_infos: 空缺信息列表，此方法会直接修改列表中对象的blank_index_in_paragraph值
        """
        # 按照段落位置分组
        paragraph_groups: dict[tuple[int, ...], list[BlankInfo]] = {}

        for blank_info in blank_infos:
            # 创建段落的唯一标识
            if blank_info.position.in_table:
                # 表格中的段落：表格索引+行索引+单元格索引+段落索引
                para_key = (
                    blank_info.position.block_index,
                    blank_info.position.row_index,
                    blank_info.position.cell_index,
                    blank_info.position.paragraph_index,
                )
            else:
                # 普通段落：段落索引
                para_key = (blank_info.position.block_index,)

            if para_key not in paragraph_groups:
                paragraph_groups[para_key] = []
            paragraph_groups[para_key].append(blank_info)

        # 对每个段落组内的空缺按照text_start_index排序，并设置索引
        for para_key, blanks_in_para in paragraph_groups.items():
            # 按照文本起始位置排序
            blanks_in_para.sort(key=lambda blank: blank.position.text_start_index)

            # 设置每个空缺在段落中的索引
            for index, blank_info in enumerate(blanks_in_para):
                blank_info.position.blank_index_in_paragraph = index + 1

    def do_fill(self, debug: bool = False):
        """
        执行填充
        """
        result_path = ""
        blanks = []
        while True:
            # 找到下一个空缺
            blank_info = self.find_next_blank()
            if blank_info is None:
                break
            # 下划线带字的跳过
            if blank_info.is_underline_format_with_fill_blank():
                continue
            blanks.append(blank_info)
        self.calculate_blank_indexes_in_paragraphs(blanks)
        self.reset_fill_progress(len(blanks))
        self.report_fill_progress()
        for index, blank_info in enumerate(blanks):
            if self.stop_flag:
                logger.info(">>>检测到停止信号 停止填充")
                break
            logger.info(f"正在处理空缺:{blank_info}")
            if debug:
                value = DocxBlankDefineResult(reason="测试场景", conclusion="测试值")
            else:
                value = self.define_blank_val(blank_info)
            logger.info(f"确定空缺处的值为:{value}")
            # # 是否跳过未知值？
            # if value.is_unknown():
            #     if not self.can_fill_unknown:
            #         logger.warning("空缺处值判断为未知,目前设置为跳过")
            #         continue
            # 填充空缺
            self.fill_blank(blank_info, value)
            logger.info(f"填充值: {value.conclusion}  到空缺处完成")
            # 保存文档
            result_path = self.save()
            logger.info(f"保存文档到:{result_path}完成")
            self.step_fill_progress()
            self.report_fill_progress()
        return result_path

    def do_fill_2(self, debug: bool = False):
        """
        执行填充
        找一个空缺，然后填充，然后保存，然后继续找下一个空缺，直到没有空缺为止
        """
        result_path = ""
        self.reset_fill_progress(-1)  # 这个模式下没有办法提前知道total
        para_blank_count_map: dict[str, int] = {}
        while True:
            if self.stop_flag:
                logger.info(">>>检测到停止信号 停止填充")
                break
            # 找到下一个空缺
            blank_info = self.find_next_blank()
            if blank_info is None:
                break
            # 下划线带字的跳过
            if blank_info.is_underline_format_with_fill_blank():
                continue
            # 设置blank_index_in_paragraph
            para_key = blank_info.get_para_key()
            if para_key not in para_blank_count_map:
                para_blank_count_map[para_key] = 0
            para_blank_count_map[para_key] += 1
            blank_info.set_blank_index_in_paragraph(para_blank_count_map[para_key])
            logger.info(f"正在处理空缺:{blank_info}")
            if debug:
                value = DocxBlankDefineResult(reason="测试场景", conclusion="测试值")
            else:
                value = self.define_blank_val(blank_info)
            logger.info(f"确定空缺处的值为:{value}")
            # 填充空缺
            self.fill_blank(blank_info, value)
            logger.info(f"填充值: {value.conclusion}  到空缺处完成")
            # 保存文档
            result_path = self.save()
            logger.info(f"保存文档到:{result_path}完成")
            self.step_fill_progress()
            self.report_fill_progress()
        return result_path

    def parallel_fill_blanks(self):
        """
        新增方法：并发收集所有blank并并发调用define_blank_val，最后串行填充
        """
        import concurrent.futures

        blanks = []
        self.doc_blank_finder.reset()
        while True:
            blank_info = self.find_next_blank()
            if blank_info is None:
                break
            if blank_info.is_underline_format_with_fill_blank():
                continue
            blanks.append(blank_info)
        if not blanks:
            logger.info("没有需要填充的空缺，直接返回")
            return
        logger.info(f"共找到符合要求的空缺:{len(blanks)}个")
        blank_result_map = {}
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_blank = {
                executor.submit(thread_safe_wrapper, self.define_blank_val, blank): blank_idx
                for blank_idx, blank in enumerate(blanks)
            }
            for future in concurrent.futures.as_completed(future_to_blank):
                blank_idx = future_to_blank[future]
                blank = blanks[blank_idx]
                try:
                    value = future.result()
                    logger.info(f"blank-{blank_idx}已经找到值 {value}")
                except Exception as exc:
                    logger.error(f"空缺{blank} 获取值异常: {exc}")
                    value = ""
                blank_result_map[blank_idx] = value
        for blank_idx, value in blank_result_map.items():
            self.fill_blank(blanks[blank_idx], value)
        result_path = self.save()
        logger.info(f"保存文档到:{result_path}完成")


def thread_safe_wrapper(func, *args, **kwargs):
    """线程安全的函数包装器，创建事件循环"""
    import asyncio

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return func(*args, **kwargs)
    finally:
        loop.close()
