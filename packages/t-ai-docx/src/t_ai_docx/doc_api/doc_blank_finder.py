import re
from typing import Dict, List, Optional, Union

from cmi_docx import add_comment
from docx import Document, document
from docx.document import Document as _Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import Table, _Cell
from docx.text.paragraph import Paragraph
from pydantic import BaseModel, Field

from .doc_helper import iter_block_items


class Position(BaseModel):
    """文档中的位置信息"""

    block_index: int = Field(default=0, description="文档块索引(用于iter_block_items遍历)")
    in_table: bool = Field(default=False, description="是否在表格中")
    table_index: int = Field(default=0, description="表格索引")
    row_index: int = Field(default=0, description="行索引")
    cell_index: int = Field(default=0, description="单元格索引")
    paragraph_index: int = Field(default=0, description="段落索引")
    run_index: int = Field(default=0, description="运行索引")


class BlankPosition(BaseModel):
    """文档中空缺的具体位置信息"""

    block_index: int = Field(description="文档块索引")
    in_table: bool = Field(description="是否在表格中")
    table_index: int = Field(
        default=0,
        description="表格索引，仅当in_table=True时有效，已废弃，使用block_index",
    )
    row_index: int = Field(default=0, description="行索引，仅当in_table=True时有效")
    cell_index: int = Field(default=0, description="单元格索引，仅当in_table=True时有效")
    paragraph_index: int = Field(
        description="段落索引,如果是段落这个和block_index相同 如果是表格这个是表格内的段落索引"
    )
    start_run_index: int = Field(description="空缺起始run索引")
    end_run_index: int = Field(description="空缺结束run索引")
    text_start_index: int = Field(description="空缺在段落文本中的起始字符位置索引")
    text_end_index: int = Field(description="空缺在段落文本中的结束字符位置索引")
    blank_index_in_paragraph: int | None = Field(default=None, description="空缺在段落中的索引(1开始)")


class BlankPositionRecord(BaseModel):
    """文档中空缺位置记录"""

    start_run_index: int = Field(description="空缺起始run索引")
    end_run_index: int = Field(description="空缺结束run索引")
    text: str = Field(description="空缺处文本")
    text_ctx: str = Field(description="空缺处上下文")
    type: str = Field(
        description="空缺类型：underscore_char(下划线字符)、underline_format(下划线格式)或bracket(方括号)"
    )
    text_start_index: int = Field(description="空缺在段落文本中的起始字符位置索引")
    text_end_index: int = Field(description="空缺在段落文本中的结束字符位置索引")
    # todo:如果一个run 有多个相同或者不同的空缺类型的话 可以有个字段承载？方便替换 或者可以变为一种type来判断


class InsertionPoint(BaseModel):
    """适合插入内容的位置信息"""

    block_index: int = Field(description="文档块索引")
    table_index: int = Field(default=0, description="表格索引，仅当位于表格中时有效，已废弃，使用block_index")
    row_index: int = Field(default=0, description="行索引，仅当位于表格中时有效")
    cell_index: int = Field(default=0, description="单元格索引，仅当位于表格中时有效")
    paragraph_index: int = Field(
        description="段落索引,如果是段落这个和block_index相同 如果是表格这个是表格内的段落索引"
    )
    run_index: int = Field(description="run索引")


class Context(BaseModel):
    """空缺的上下文信息"""

    paragraph_text: str = Field(description="段落完整文本")
    blank_text: str = Field(description="空缺文本")
    blank_text_ctx: str = Field(description="空缺文本上下文")
    cell_context: Optional[str] = Field(default=None, description="单元格上下文信息，包含周围单元格内容")


class BlankInfo(BaseModel):
    """空缺的完整信息"""

    position: BlankPosition = Field(description="空缺在文档中的位置")
    # insertion_point: InsertionPoint = Field(description="适合插入内容的位置")
    context: Context = Field(description="相关上下文信息")
    type: str = Field(
        description="空缺类型：underscore_char(下划线字符)、underline_format(下划线格式)或bracket(方括号)"
    )
    next_position: Position = Field(description="下一个查找位置")

    def get_para_key(self) -> str:
        if self.position.in_table:
            return f"{self.position.block_index}_{self.position.row_index}_{self.position.cell_index}_{self.position.paragraph_index}"
        else:
            return f"{self.position.block_index}"

    def set_blank_index_in_paragraph(self, blank_index_in_paragraph: int):
        self.position.blank_index_in_paragraph = blank_index_in_paragraph

    def format_info(self) -> str:
        """返回格式化的空缺信息字符串"""
        lines = ["\n以下是空缺在文档中的信息:"]

        # 显示位置类型（表格/主文档）
        lines.append(f"空缺位置: {self.position.in_table and '表格中空缺' or '段落中空缺'}")
        # 显示空缺类型和内容
        type_descriptions = {
            "underscore_char": "docx下划线字符",
            "underline_format": "docx下划线格式",
            "bracket": "docx方括号字符",
        }
        lines.append(f"空缺类型: {type_descriptions.get(self.type, self.type)}")
        # 如果在表格中，显示表格位置信息
        if self.position.in_table:
            lines.append(f"位于表格: {self.position.table_index}")
            lines.append(f"第{self.position.row_index}行")
            lines.append(f"第{self.position.cell_index}单元格")
            lines.append(f"第{self.position.paragraph_index}个段落")
            lines.append(f"跨越run{self.position.start_run_index}-{self.position.end_run_index}")
            if self.position.blank_index_in_paragraph:
                lines.append(
                    f"这是段落中的第{self.position.blank_index_in_paragraph}个空缺，字符开始于{self.position.text_start_index}结束于{self.position.text_end_index}"
                )
            else:
                lines.append(f"空缺字符开始于{self.position.text_start_index}结束于{self.position.text_end_index}")
            # 如果有单元格上下文信息，也显示出来
            if self.context.cell_context:
                lines.append(f"当前单元格的上下文: {self.context.cell_context}")
        else:
            # 显示段落和run位置
            lines.append(f"位于段落: {self.position.paragraph_index}")
            lines.append(f"跨越run{self.position.start_run_index}-{self.position.end_run_index}")
            if self.position.blank_index_in_paragraph:
                lines.append(
                    f"这是段落中的第{self.position.blank_index_in_paragraph}个空缺，字符开始于{self.position.text_start_index}结束于{self.position.text_end_index}"
                )
            else:
                lines.append(f"空缺字符开始于{self.position.text_start_index}结束于{self.position.text_end_index}")
        lines.append(f"空缺的文本: '{self.context.blank_text}'")
        lines.append(f"空缺文本的上下文: '{self.context.blank_text_ctx}'")
        lines.append(f"空缺所在段落文本: '{self.context.paragraph_text}'")

        return "\n".join(lines)

    def __str__(self) -> str:
        """使对象可直接打印，返回格式化信息"""
        return self.format_info()

    def is_table_blank(self) -> bool:
        """
        判断空缺是否在表格中
        """
        return self.position.in_table

    def is_paragraph_blank(self) -> bool:
        """
        判断空缺是否在段落中
        """
        return not self.is_table_blank()

    def is_underline_format(self) -> bool:
        """
        判断空缺是否是下划线格式
        """
        return self.type == "underline_format"

    def is_blank_text_empty(self) -> bool:
        """
        判断空缺文本是否为空
        """
        return self.context.blank_text.strip() == ""

    def is_underline_format_with_fill_blank(self) -> bool:
        """
        判断空缺是否是下划线字符 并且这个字符有值
        这个场景对于判断是否已经预先填写非常有用
        """
        return self.is_underline_format() and not self.is_blank_text_empty()


class DocxBlankFinder:
    """
    用于查找Word文档中空缺项的类
    可以识别以下类型的空缺:
    1. 下划线字符 (__) - 任意长度
    2. 带有下划线格式的文本
    3. 方括号中的空格 [   ]
    """

    def __init__(self, doc: _Document):
        """初始化查找器并加载文档"""
        self.document = doc
        # 缓存文档结构，避免重复遍历
        self._blocks: List = []
        self._structures_cached = False
        self.reset()

    @staticmethod
    def from_doc_path(doc_path: str):
        """
        从docx文件路径初始化查找器并加载文档
        Args:
            doc_path (str): Word文档路径
        """
        doc = Document(doc_path)
        return DocxBlankFinder(doc)

    def reset(self):
        """重置查找位置到文档开始"""
        self.current_position = Position(
            block_index=0,
            table_index=0,
            row_index=0,
            cell_index=0,
            paragraph_index=0,
            run_index=0,
            in_table=False,
        )
        self.reached_end = False

    def _ensure_cached_structures(self):
        """确保文档结构已缓存"""
        if not self._structures_cached:
            self._blocks = list(iter_block_items(self.document))
            self._structures_cached = True

    def next_blank(self) -> Optional[BlankInfo]:
        """
        查找并返回下一个空缺项

        返回:
            包含空缺信息的BlankInfo对象或找不到更多空缺时返回None
        """
        if self.reached_end:
            return None

        # 从当前位置继续查找
        result = self._find_next_blank()

        # 更新位置用于下次搜索
        if result:
            self.current_position = result.next_position
        else:
            self.reached_end = True

        return result

    def _find_next_blank(self) -> Optional[BlankInfo]:
        """内部方法：从当前位置查找下一个空缺"""
        # 确保文档结构已缓存
        self._ensure_cached_structures()

        # 从当前块位置开始遍历
        for block_idx in range(self.current_position.block_index, len(self._blocks)):
            block = self._blocks[block_idx]

            if isinstance(block, Paragraph):
                # 处理段落块
                # 如果是同一个块，从上次的run_index开始
                start_run_idx = self.current_position.run_index if block_idx == self.current_position.block_index else 0

                if blank_pos_in_p := self._find_blank_in_paragraph(block, start_run_idx):
                    return BlankInfo(
                        position=BlankPosition(
                            block_index=block_idx,
                            in_table=False,
                            paragraph_index=block_idx,  # 表示block索引
                            start_run_index=blank_pos_in_p.start_run_index,
                            end_run_index=blank_pos_in_p.end_run_index,
                            text_start_index=blank_pos_in_p.text_start_index,
                            text_end_index=blank_pos_in_p.text_end_index,
                        ),
                        # insertion_point=InsertionPoint(
                        #     block_index=block_idx,
                        #     paragraph_index=block_idx,
                        #     run_index=blank_pos_in_p.end_run_index,
                        # ),
                        context=Context(
                            paragraph_text=block.text,
                            blank_text=blank_pos_in_p.text,
                            blank_text_ctx=blank_pos_in_p.text_ctx,
                        ),
                        type=blank_pos_in_p.type,
                        next_position=Position(
                            block_index=block_idx,
                            in_table=False,
                            run_index=blank_pos_in_p.end_run_index + 1,
                        ),
                    )

            elif isinstance(block, Table):
                # 处理表格块
                table = block

                # 确定起始位置
                start_row = self.current_position.row_index if block_idx == self.current_position.block_index else 0

                for r_idx in range(start_row, len(table.rows)):
                    row = table.rows[r_idx]

                    start_cell = (
                        self.current_position.cell_index
                        if (block_idx == self.current_position.block_index and r_idx == self.current_position.row_index)
                        else 0
                    )

                    for c_idx in range(start_cell, len(row.cells)):
                        cell = row.cells[c_idx]

                        start_para = (
                            self.current_position.paragraph_index
                            if (
                                block_idx == self.current_position.block_index
                                and r_idx == self.current_position.row_index
                                and c_idx == self.current_position.cell_index
                            )
                            else 0
                        )

                        for p_idx in range(start_para, len(cell.paragraphs)):
                            paragraph = cell.paragraphs[p_idx]

                            start_run_idx = (
                                self.current_position.run_index
                                if (
                                    block_idx == self.current_position.block_index
                                    and r_idx == self.current_position.row_index
                                    and c_idx == self.current_position.cell_index
                                    and p_idx == self.current_position.paragraph_index
                                )
                                else 0
                            )
                            if blank_pos_in_p := self._find_blank_in_paragraph(paragraph, start_run_idx):
                                # 收集单元格上下文信息
                                cell_context = self._collect_cell_context(table, r_idx, c_idx)

                                return BlankInfo(
                                    position=BlankPosition(
                                        block_index=block_idx,
                                        in_table=True,
                                        table_index=block_idx,  # 表示block索引
                                        row_index=r_idx,
                                        cell_index=c_idx,
                                        paragraph_index=p_idx,  # 表示单元格内的段落索引
                                        start_run_index=blank_pos_in_p.start_run_index,
                                        end_run_index=blank_pos_in_p.end_run_index,
                                        text_start_index=blank_pos_in_p.text_start_index,
                                        text_end_index=blank_pos_in_p.text_end_index,
                                    ),
                                    # insertion_point=InsertionPoint(
                                    #     block_index=block_idx,
                                    #     table_index=block_idx,
                                    #     row_index=r_idx,
                                    #     cell_index=c_idx,
                                    #     paragraph_index=p_idx,
                                    #     run_index=blank_pos_in_p.end_run_index,
                                    # ),
                                    context=Context(
                                        paragraph_text=paragraph.text,
                                        blank_text=blank_pos_in_p.text,
                                        blank_text_ctx=blank_pos_in_p.text_ctx,
                                        cell_context=cell_context,
                                    ),
                                    type=blank_pos_in_p.type,
                                    next_position=Position(
                                        block_index=block_idx,
                                        in_table=True,
                                        table_index=block_idx,
                                        row_index=r_idx,
                                        cell_index=c_idx,
                                        paragraph_index=p_idx,
                                        run_index=blank_pos_in_p.end_run_index + 1,
                                    ),
                                )

        # 如果搜索完整个文档仍未找到
        return None

    def _find_blank_in_paragraph(self, paragraph: Paragraph, start_run_idx: int = 0) -> Optional[BlankPositionRecord]:
        """
        在段落中查找空缺 - 线性遍历run的方式，从左到右遍历run 来匹配所有的空缺场景 并重置start_run_idx 直到结束

        空缺的场景：
        1. docx下划线格式
        格式属性(如下划线)总是应用于整个run，而不可能只应用于run内的部分文本。
        也就是说判断run为下划线格式的话 他就只能是下划线格式 不会再有其他的了
        也就是说这里就1个场景:
        判断连续的run 是否都为下划线格式 如果是表明这是空缺
        2. docx中的下划线字符
        这个就是一个下划线的字符与其他的字符没有区别 所以需要从字符角度来判断
        有两个场景:
        A. 下划线包含在一个run中 比如 我___要去吃东西了。 必须检测是包含关系也就是说必须判断__左右都有东西这种情况(在一个run里) 可以用正则
        B. 下划线跨run。比如 ____ 这个可能有多个run 这种场景前一个run的结尾必须是_(不一定整个run都是_) 且后面的run必须前匹配run的场景(包括完全匹配run)
        3. docx中的中括号 [空白字符] 或者 【空白字符】
        这个就是一个下划线的字符与其他的字符没有区别 所以需要从字符角度来判断 但是又和2不同中间可以是空白字符
        有两个场景:
        A. [空白字符] 或者 【空白字符】 包含在一个run中 比如 我[空白字符]/【空白字符】要去吃东西了。 必须检测是包含关系也就是说必须判断[空白字符]/【空白字符】左右都有东西这种情况(在一个run里) 可以用正则
        B. [空白字符] 或者 【空白字符】跨run 这种场景前一个run的结尾必须是[/【(不一定整个run都是[) 后一个run的开头必须是]/】 或者空白字符 直到run不是开头不是的为止 再通过正则来找到连续的[空白字符] 或者 【空白字符】

        注意:
        目前对应只能找到空缺在一个run里的或者空缺跨多个run的 但是由于这里只到run的维度所以一个run 如果有多个空缺的场景目前就全部找到了
        可能可以在替换的时候同事替换多个值来解决

        """
        runs = paragraph.runs
        if start_run_idx >= len(runs):
            return None

        # 计算到start_run_idx之前的所有文本长度，作为基准位置
        text_offset = sum(len(runs[k].text) for k in range(start_run_idx))

        i = start_run_idx
        while i < len(runs):
            run = runs[i]

            # 1. 检查下划线格式
            # 判断连续的下划线格式 直到不满足为止
            if check_run_is_underline_format(run):
                start_idx = i
                end_idx = i

                # 向后查找连续的下划线格式
                for j in range(i + 1, len(runs)):
                    next_run = runs[j]
                    if check_run_is_underline_format(next_run):
                        end_idx = j
                    else:
                        break

                # 收集下划线文本
                blank_text = "".join([runs[k].text for k in range(start_idx, end_idx + 1)])

                # 计算文本位置
                text_start = text_offset + sum(len(runs[k].text) for k in range(start_run_idx, start_idx))
                text_end = text_start + len(blank_text)

                return BlankPositionRecord(
                    start_run_index=start_idx,
                    text=blank_text,
                    text_ctx=blank_text,  # 对于下划线格式而言 空缺文本上下文就是空缺文本本身 因为这些空缺文本占满了run
                    type="underline_format",
                    end_run_index=end_idx,
                    text_start_index=text_start,
                    text_end_index=text_end,
                )

            # 2. 检查下划线字符
            # 2A. 下划线包含在一个run 比如 我___要去吃东西了。
            # 包含 AA__AA 和 __AA.不包含 AA__ 和 ____ 因为这样有可能是连续的
            # todo: 考虑overlap的情况？ 比如一个run里多组...
            if underscore_match := re.search(r"(_+)[^_]+", run.text):
                # 计算文本位置
                current_run_start = text_offset + sum(len(runs[k].text) for k in range(start_run_idx, i))
                text_start = current_run_start + underscore_match.start(1)
                text_end = current_run_start + underscore_match.end(1)

                return BlankPositionRecord(
                    start_run_index=i,
                    end_run_index=i,
                    text=underscore_match.group(1),
                    text_ctx=run.text,
                    type="underscore_char",
                    text_start_index=text_start,
                    text_end_index=text_end,
                )
            # 2B. 检查跨run的下划线字符
            # 下划线跨run。比如AA__ 或者 ____ 下面可能会有连续的run的场景
            if run.text.endswith("_"):
                # 找到包含下划线的开始位置
                start_idx = i
                end_idx = i

                # 向后查找所有连续前缀为_的run 如果全为__ 记录继续 如果 部分为__ 记录后break
                for j in range(i + 1, len(runs)):
                    if re.match(r"^_+$", runs[j].text):  # 完全匹配
                        end_idx = j
                    elif re.match(r"^_+[^_]+", runs[j].text):  # 部分匹配  但是如果是 AA__AA 就没办法了
                        end_idx = j
                        break
                    else:
                        break

                # 收集所有这些run的文本
                combined_text = "".join([runs[k].text for k in range(start_idx, end_idx + 1)])

                # 在组合文本中查找下划线
                if underscore_match := re.search(r"_+", combined_text):
                    # 计算文本位置
                    current_run_start = text_offset + sum(len(runs[k].text) for k in range(start_run_idx, start_idx))
                    text_start = current_run_start + underscore_match.start()
                    text_end = current_run_start + underscore_match.end()

                    return BlankPositionRecord(
                        start_run_index=start_idx,
                        end_run_index=end_idx,
                        text=underscore_match.group(),
                        text_ctx=combined_text,  # 整个组合文字
                        type="underscore_char",
                        text_start_index=text_start,
                        text_end_index=text_end,
                    )

            # 3. 检查方括号
            #  3A. [空白字符] 或者 【空白字符】 包含在一个run中 比如 我[空白字符]/【空白字符】要去吃东西了。 必须检测是包含关系也就是说必须判断[空白字符]/【空白字符】左右都有东西这种情况(在一个run里) 可以用正则
            # 连续两个括号场景[][]
            if bracket_match := re.search(r"([\[【]\s*[\]】])", run.text):
                # 计算文本位置
                current_run_start = text_offset + sum(len(runs[k].text) for k in range(start_run_idx, i))
                text_start = current_run_start + bracket_match.start(1)
                text_end = current_run_start + bracket_match.end(1)

                return BlankPositionRecord(
                    start_run_index=i,
                    end_run_index=i,
                    text=bracket_match.group(1),
                    text_ctx=run.text,
                    type="bracket",
                    text_start_index=text_start,
                    text_end_index=text_end,
                )
            # 3B 检查跨run的下划线字符
            # [空白字符] 或者 【空白字符】跨run
            # 这种场景前一个run的结尾必须是[/【(不一定整个run都是[) 后一个run的开头必须是]/】
            # 或者空白字符 直到run不是开头不是的为止 再通过正则来找到连续的[空白字符] 或者 【空白字符】
            if run.text.endswith("[") or run.text.endswith("【"):
                # 找到包含左括号的开始位置
                start_idx = i
                end_idx = i

                # 向后查找直到找到包含右括号的run
                # 如果全部\s 记录 继续
                # 如果为\s*] 或者\s*】记录break
                bracket_found = False
                for j in range(i + 1, len(runs)):
                    if re.match(r"^\s*$", runs[j].text):  # 全是空白字符
                        end_idx = j
                    elif re.match(r"^\s*[\]】]", runs[j].text):  # 空白右边的 可能是 ]
                        end_idx = j
                        bracket_found = True
                        break
                    else:
                        break

                # 只有找到右括号才继续处理
                if bracket_found:
                    # 收集所有这些run的文本
                    combined_text = "".join([runs[k].text for k in range(start_idx, end_idx + 1)])

                    # 在组合文本中查找方括号模式 因为run可能包含除了括号以外的
                    if bracket_match := re.search(r"[\[【]\s*[\]】]", combined_text):
                        # 计算文本位置
                        current_run_start = text_offset + sum(
                            len(runs[k].text) for k in range(start_run_idx, start_idx)
                        )
                        text_start = current_run_start + bracket_match.start()
                        text_end = current_run_start + bracket_match.end()

                        return BlankPositionRecord(
                            start_run_index=start_idx,
                            end_run_index=end_idx,
                            text=bracket_match.group(),
                            text_ctx=combined_text,
                            type="bracket",
                            text_start_index=text_start,
                            text_end_index=text_end,
                        )

            # 继续检查下一个run
            i += 1

        # 没有找到空缺
        return None

    def _collect_cell_context(self, table: Table, row_index: int, cell_index: int) -> str:
        """
        收集表格单元格周围的上下文信息

        Args:
            table: 表格对象
            row_index: 当前行索引
            cell_index: 当前单元格索引

        Returns:
            包含周围单元格信息的上下文字符串
        """
        context_parts = []

        # 获取当前单元格的完整文本（所有段落的累积）
        current_cell = table.rows[row_index].cells[cell_index]
        current_cell_text = current_cell.text.strip()
        context_parts.append(f"当前单元格[{row_index},{cell_index}]: {current_cell_text}")

        # 收集周围单元格信息
        directions = [
            (-1, 0, "上方"),  # 上
            (0, -1, "左边"),  # 左
            (0, 1, "右边"),  # 右
            (1, 0, "下边"),  # 下
        ]

        direction_info = []
        for dr, dc, direction in directions:
            new_row = row_index + dr
            new_col = cell_index + dc

            # 检查索引是否有效
            if 0 <= new_row < len(table.rows) and 0 <= new_col < len(table.rows[new_row].cells):
                try:
                    cell = table.rows[new_row].cells[new_col]
                    cell_text = cell.text.strip()
                    if cell_text:  # 有内容
                        direction_info.append(f"{direction}: {cell_text}")
                    else:  # 无内容
                        direction_info.append(f"{direction}: 无")
                except Exception:
                    # 如果访问单元格出错
                    direction_info.append(f"{direction}: 无")
            else:
                # 索引超出范围
                direction_info.append(f"{direction}: 无")

        # 添加方向信息
        if direction_info:
            context_parts.append(" | ".join(direction_info))

        # 如果是表格的第一行，可能是表头，添加表头信息
        if row_index == 0:
            try:
                header_cells = []
                for i, cell in enumerate(table.rows[0].cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        header_cells.append(f"列{i}: {cell_text}")
                if header_cells:
                    context_parts.append(f"表头信息: {', '.join(header_cells)}")
            except Exception:
                pass

        return " | ".join(context_parts)

    def fill_blank(
        self,
        blank_info: BlankInfo,
        value: str,
        reason: str = "",
        is_add_comment: bool = True,
    ):
        """
        使用给定值填充文档中的空缺
        现在使用基于block的索引系统

        Args:
            blank_info (BlankInfo): 空缺信息
            value (str): 用于填充空缺的文本 如果是一个run 多个空缺的场景 则考虑,号隔开做替换(后面) re.sub 可以做连续的正则替换
        """
        # 确保文档结构已缓存
        self._ensure_cached_structures()

        # 获取段落对象
        block_idx = blank_info.position.block_index
        block = self._blocks[block_idx]

        if blank_info.position.in_table:
            # 如果空缺在表格中
            if not isinstance(block, Table):
                raise ValueError(f"Block at index {block_idx} is not a table")

            table = block
            cell = table.rows[blank_info.position.row_index].cells[blank_info.position.cell_index]
            paragraph = cell.paragraphs[blank_info.position.paragraph_index]
        else:
            # 如果空缺在主文档中
            if not isinstance(block, Paragraph):
                raise ValueError(f"Block at index {block_idx} is not a paragraph")

            paragraph = block

        # 获取run对象
        runs = paragraph.runs
        start_run_idx = blank_info.position.start_run_index
        end_run_idx = blank_info.position.end_run_index
        location = None
        # 根据空缺类型处理填充
        if blank_info.type == "underscore_char":
            # 下划线附近追加__表示下划线替换
            value = f"__{value}__"
            # 处理下划线字符类型
            # 如果所有下划线在同一个run中
            if start_run_idx == end_run_idx:
                run = runs[start_run_idx]
                run.text = run.text.replace(blank_info.context.blank_text, value)
                location = run
            else:
                # 如果下划线跨越多个run
                # 在第一个run中替换下划线开始部分
                first_run = runs[start_run_idx]
                underscore_pos = first_run.text.find("_")
                if underscore_pos != -1:
                    first_run.text = first_run.text[:underscore_pos] + value
                else:
                    first_run.text = value

                # 清除中间runs的文本
                for i in range(start_run_idx + 1, end_run_idx):
                    runs[i].text = ""

                # 清除最后一个run中的下划线部分
                last_run = runs[end_run_idx]
                underscore_end_pos = last_run.text.rfind("_") + 1
                if underscore_end_pos > 0:
                    last_run.text = last_run.text[underscore_end_pos:]
                else:
                    last_run.text = ""
                location = (first_run, last_run)
        elif blank_info.type == "underline_format":
            # 处理下划线格式类型
            # 保留第一个run并用新值替换其文本，但保持其格式
            first_run = runs[start_run_idx]
            first_run.text = value

            # 删除其他下划线格式的runs（如果有）
            for i in range(start_run_idx + 1, end_run_idx + 1):
                runs[i].text = ""

            location = first_run

        elif blank_info.type == "bracket":
            # 处理方括号类型
            # 如果方括号在同一个run中
            # 统一先追加中文括号
            value = f"【{value}】"
            if start_run_idx == end_run_idx:
                run = runs[start_run_idx]
                # 使用正则表达式替换方括号及其内容
                run.text = re.sub(r"[\[【]\s*[\]】]", value, run.text, count=1)
                location = run
            else:
                # 如果方括号跨越多个run
                # 在第一个run中替换开始括号部分
                first_run = runs[start_run_idx]
                bracket_pos = first_run.text.find("[") if "[" in first_run.text else first_run.text.find("【")
                if bracket_pos != -1:
                    first_run.text = first_run.text[:bracket_pos] + value
                else:
                    first_run.text = value

                # 清除中间runs的文本
                for i in range(start_run_idx + 1, end_run_idx):
                    runs[i].text = ""

                # 清除最后一个run中的结束括号部分
                last_run = runs[end_run_idx]
                end_bracket_pos = last_run.text.find("]") if "]" in last_run.text else last_run.text.find("】")
                if end_bracket_pos != -1:
                    if end_bracket_pos + 1 < len(last_run.text):
                        last_run.text = last_run.text[end_bracket_pos + 1 :]
                    else:
                        last_run.text = ""
                else:
                    last_run.text = ""
                location = (first_run, last_run)
        if is_add_comment and location:
            add_comment(
                docx_doc=self.document,
                location=location,
                author="AI",
                text=get_comment_text(blank_info, value, reason),
            )


def get_comment_text(blank_info: BlankInfo, value: str, reason: str) -> str:
    """
    获取注释文本
    """
    blank_location = "表格" if blank_info.position.in_table else "段落"
    return f"""
    这是一个 {blank_location} 中的空缺,
    判断思路为:{reason}
    """


def check_run_is_underline_format(run) -> bool:
    return hasattr(run.font, "underline") and run.font.underline and run.font.underline is not False
