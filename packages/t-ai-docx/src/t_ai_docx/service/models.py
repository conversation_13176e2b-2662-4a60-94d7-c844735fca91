from pydantic import BaseModel, Field


class DocGenrateReq(BaseModel):
    doc_template_file_url: str = Field(..., description="Docx template file url")
    doc_model_info: str = Field(..., description="Docx model info")
    can_fill_unknown: bool = Field(default=True, description="Can fill unknown")
    is_add_comment: bool = Field(default=True, description="Is add comment")
    model_name: str = Field(default="qwen-plus", description="Model name")


class DocGenrateRes(BaseModel):
    docUrl: str = Field(..., description="Docx file url", alias="doc_url")
    taskId: str = Field(..., description="Task id", alias="task_id")
