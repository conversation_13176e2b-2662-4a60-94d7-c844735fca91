import asyncio
import threading
from uuid import uuid4

from loguru import logger

from ..doc_api import DocxOperator
from .models import DocGenrateReq, DocGenrateRes

task_manger: dict[str, DocxOperator] = {}


def thread_safe_wrapper(func, *args, **kwargs):
    """线程安全的函数包装器，创建事件循环"""
    import asyncio

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return func(*args, **kwargs)
    finally:
        loop.close()


def do_docx_generate_sync(req: DocGenrateReq) -> DocGenrateRes:
    doc_opertor = DocxOperator(
        docx_path=req.doc_template_file_url,
        model_json_schema=req.doc_model_info,
        can_fill_unknown=req.can_fill_unknown,
        is_add_comment=req.is_add_comment,
        model_name=req.model_name,
    )
    task_id = str(uuid4())
    task_manger[task_id] = doc_opertor
    result_path = doc_opertor.get_docx_path()

    def _do_fill():
        try:
            doc_opertor.do_fill_2()
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            raise e
        finally:
            if task_id in task_manger:
                del task_manger[task_id]

    thread = threading.Thread(target=thread_safe_wrapper, args=(_do_fill,))
    thread.start()
    return DocGenrateRes(doc_url=result_path, task_id=task_id)


async def do_docx_generate(req: DocGenrateReq) -> DocGenrateRes:
    return await asyncio.get_running_loop().run_in_executor(None, do_docx_generate_sync, req)


async def get_generate_docx_progress(task_id: str):
    if task_id not in task_manger:
        return {
            "status": "failed",
            "message": "任务不存在或已暂停",
        }
    task = task_manger[task_id]
    progress = task.get_fill_progress()
    if progress.is_finished():
        return {
            "status": "success",
            "message": "任务已完成",
            "progress": progress.model_dump(),
            "config": task.get_config(),
        }
    return {
        "status": "running",
        "message": "任务进行中",
        "progress": progress.model_dump(),
        "config": task.get_config(),
    }


async def stop_docx_generate(task_id: str):
    if task_id not in task_manger:
        return {
            "status": "failed",
            "message": "任务不存在或已暂停",
        }
    task_manger[task_id].stop()
    if task_id in task_manger:
        del task_manger[task_id]
    return {
        "status": "success",
        "message": "停止任务成功",
    }
