import re
import time
from pathlib import Path
from typing import Optional
from urllib.parse import unquote, urlparse

import alibabacloud_oss_v2 as oss
from alibabacloud_oss_v2.types import BodyType
from loguru import logger

from .docx_settings import settings


class OSSBucketClient:
    def __init__(self, prefix: str):
        self.client = get_oss_client()
        self.bucket_name = settings.oss_settings.bucket
        self.prefix = prefix  # 路径

    def list_prefix(self):
        return self.list(prefix=self.prefix)

    def list(self, prefix=None):
        return self.client.list_objects_v2(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=prefix))

    @staticmethod
    def get_obj_key_from_url(url: str):
        """
        从url中提取对象的key
        标准的格式为:
        https://<bucket>.<endpoint>/<object-key>
        """
        parsed_url = urlparse(url)
        return parsed_url.path.lstrip("/")  # 去掉最前面的 '/'

    @staticmethod
    def get_filename_from_content_disposition(content_disposition: Optional[str]):
        if not content_disposition:
            return None
        # 首先尝试解析 filename* 参数(RFC 5987 格式)，它通常包含完整的 UTF-8 编码
        filename_star_match = re.search(r"filename\*=UTF-8''([^;]*)", content_disposition)
        if filename_star_match:
            return unquote(filename_star_match.group(1))

        # 如果没有 filename*，尝试解析常规 filename 参数
        filename_match = re.search(r"filename=([^;]*)", content_disposition)
        if filename_match:
            filename = filename_match.group(1)
            # 移除可能的引号
            if filename.startswith('"') and filename.endswith('"'):
                filename = filename[1:-1]
            return unquote(filename)

        return None

    @staticmethod
    def get_filename_from_oss_key(oss_key: Optional[str]):
        """
        从oss key中提取文件名
        """
        if not oss_key:
            return None
        return oss_key.split("/")[-1]

    @staticmethod
    def get_oss_object_name(get_object_result: oss.GetObjectResult, oss_key: Optional[str] = None):
        try:
            name = OSSBucketClient.get_filename_from_content_disposition(get_object_result.content_disposition)
            if name:
                return name
        except:  # noqa: E722
            pass
        return OSSBucketClient.get_filename_from_oss_key(oss_key)

    def put_object(
        self,
        file_name: str,
        body: Optional[BodyType],
    ) -> str:
        oss_object_key = self.get_default_oss_key(file_name)
        self.client.put_object(
            oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=oss_object_key,
                body=body,
            )
        )
        return oss_object_key

    def get_object_url(self, oss_key: str) -> str:
        # 这里假设你存了 bucket_name 和 endpoint
        return f"https://{self.bucket_name}.{settings.oss_settings.endpoint}/{oss_key}"

    def get_default_oss_key(self, file_name: str):
        return f"{self.prefix}/{file_name}"

    def get_object(
        self,
        obj_key: str,
    ) -> oss.GetObjectResult:
        """
        获取文件
        Args:
            obj_key (str): 符合oss 标准的 oss key
        Returns:
            oss.GetObjectResult: oss 文件
        """
        return self.client.get_object(
            oss.GetObjectRequest(
                bucket=self.bucket_name,  # 指定存储空间名称
                key=obj_key,  # 指定对象键名
            )
        )

    def get_object_by_url(
        self,
        url: str,
    ) -> oss.GetObjectResult:
        """
        获取文件
        Args:
            url (str): 文件url
        Returns:
            oss.GetObjectResult: oss 文件
        """
        obj_key = self.get_obj_key_from_url(url)
        return self.get_object(obj_key)

    def download_file(
        self,
        obj_key: str,
        target_local_dir: str,
        file_name: Optional[str] = None,
    ) -> str:
        """
        下载文件

        Args:
            obj_key (str): 符合oss 标准的 oss key
            target_local_dir (str): 目标本地dir
            file_name (Optional[str], optional): 文件名.
        Returns:
            str: 目标文件路径
        """
        result = self.get_object(obj_key)
        t_p = Path(target_local_dir)
        t_p.mkdir(parents=True, exist_ok=True)
        if not file_name:
            file_name = OSSBucketClient.get_oss_object_name(result, obj_key)
            if not file_name:
                file_name = f"unknown_{int(time.time())}"
        target_local_path = str(t_p / file_name)
        with result.body as body_stream:
            total_size = 0

            with open(target_local_path, "wb") as f:
                for chunk in body_stream.iter_bytes(block_size=256 * 1024):
                    f.write(chunk)
                    total_size += len(chunk)
        logger.info(f"文件下载完成，保存至路径：{target_local_path}")
        return target_local_path

    def download_file_by_url(
        self,
        url: str,
        target_local_dir: str,
        file_name: Optional[str] = None,
    ):
        obj_key = self.get_obj_key_from_url(url)
        self.download_file(obj_key, target_local_dir, file_name)


def get_oss_client():
    """
    参考:
    https://help.aliyun.com/zh/oss/developer-reference/get-started-with-oss-sdk-for-python-v2?spm=a2c4g.11186623.help-menu-31815.d_5_2_2_0_0.5a737d4aDKvR0u&scm=20140722.H_2860504._.OR_help-T_cn~zh-V_1
    """
    credentials_provider = oss.credentials.StaticCredentialsProvider(
        access_key_id=settings.oss_settings.oss_access_key_id,
        access_key_secret=settings.oss_settings.oss_access_key_secret,
    )
    cfg = oss.config.load_default()
    cfg.credentials_provider = credentials_provider
    cfg.region = settings.oss_settings.region
    cfg.endpoint = settings.oss_settings.endpoint
    return oss.Client(cfg)


OSS_BUCKET_CLIENT = OSSBucketClient(prefix="demos/donghang/AI/doc_generatation")
