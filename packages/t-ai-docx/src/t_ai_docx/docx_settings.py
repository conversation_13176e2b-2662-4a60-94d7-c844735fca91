from enum import Enum

from pydantic import Field
from pydantic_settings import BaseSettings


class LogLevel(str, Enum):
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogSettings(BaseSettings):
    level: LogLevel = Field(default=LogLevel.INFO, description="日志级别", alias="LOG_LEVEL")
    backtrace: bool = Field(default=True, description="日志是否堆栈", alias="LOG_BACKTRACE")
    diagnose: bool = Field(
        default=True,
        description="日志打印堆栈的时候是否打印局部变量",
        alias="LOG_DIAGNOSE",
    )


class OpenAISettings(BaseSettings):
    api_key: str = Field(
        default="t_80b4544ad4eb4130b424986ad9c4186f",
        alias="OPENAI_API_KEY",
        description="OpenAI Compatible API key",
    )
    base_url: str = Field(
        default="https://ai-proxy.erda.cloud/v1",
        alias="OPENAI_BASE_URL",
        description="OpenAI API base url",
    )


class OSSSettings(BaseSettings):
    oss_access_key_id: str = Field(
        default="LTAI5tSNj6WQqLNGA2ZzjaYL",
        alias="OSS_ACCESS_KEY_ID",
        description="OSS access key id",
    )
    oss_access_key_secret: str = Field(
        default="******************************",
        alias="OSS_ACCESS_KEY_SECRET",
        description="OSS access key secret",
    )
    bucket: str = Field(
        default="terminus-new-trantor",
        alias="OSS_BUCKET",
        description="OSS bucket name",
    )
    endpoint: str = Field(
        default="oss-cn-hangzhou.aliyuncs.com",
        alias="OSS_ENDPOINT",
        description="OSS endpoint",
    )
    region: str = Field(
        default="cn-hangzhou",
        alias="OSS_REGION",
        description="OSS region",
    )


class AppSettings(BaseSettings):
    openai_settings: OpenAISettings = OpenAISettings()
    log_settings: LogSettings = LogSettings()
    oss_settings: OSSSettings = OSSSettings()

    class Config:
        env_file_encoding = "utf-8"
        case_sensitive = False


settings = AppSettings()
