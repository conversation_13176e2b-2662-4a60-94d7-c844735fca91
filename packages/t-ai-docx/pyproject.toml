[project]
name = "t-ai-docx"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alibabacloud-oss-v2>=1.1.1",
    "loguru>=0.7.3",
    "openai-agents>=0.2.5",
    "pydantic-settings>=2.9.1",
    "python-docx>=1.1.2",
    "pydantic>=2.11.4",
    "cmi-docx"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_docx"]
