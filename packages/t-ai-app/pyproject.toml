[project]
name = "t-ai-app"
version = "0.1.0"
description = "t-ai-app包 全局单例 设置等"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # internal
    "t_ai_common",

    # external
    "pydantic-settings>=2.8.1",
    "pydantic>=2.11.4",
    "loguru>=0.7.3",
    "fastapi[standard]>=0.115.11",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_app"]
