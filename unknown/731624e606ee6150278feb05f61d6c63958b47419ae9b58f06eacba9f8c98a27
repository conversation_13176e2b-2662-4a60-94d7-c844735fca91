import asyncio
import json
import re
import time
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from loguru import logger
from t_ai_agent_ops.model.evaluation import AgentConfig

# 导入 HTTP 客户端
from t_ai_agent_ops.utils.agent_http_client import AgentHttpClient
from t_ai_app.ctx import ReqCtx


class AgentService:
    """Agent 服务"""

    def __init__(self, referer: str = None):
        """初始化 Agent 服务

        Args:
            referer: HTTP 请求的 referer，用于动态生成 base URL
        """
        self.referer = referer
        self.http_client = AgentHttpClient(referer=referer)

    async def _get_portal_url_from_referer(self, referer: str, portal_key: str) -> Optional[str]:
        """从 referer 中解析并获取门户地址

        Args:
            referer: HTTP 请求的 referer
            portal_key: 门户模块的 key

        Returns:
            Optional[str]: 门户地址，如果解析失败则返回 None
        """
        try:
            # 解析 referer 中的域名信息
            # 示例: https://t-erp-console-dev.app.terminus.io/team/203/portal/TERP_PORTAL/portalManage
            pattern = r"https://([^/]+)"
            match = re.search(pattern, referer)

            if not match:
                logger.warning(f"无法从 referer 中解析域名信息: {referer}")
                return None

            domain = match.group(1)  # t-erp-console-dev.app.terminus.io
            team_id = ReqCtx.get_header().t_ai_source_team
            logger.debug(f"team_id: {team_id}")

            # 构建获取模块配置的 API URL
            api_url = f"https://{domain}/api/trantor/console/module/{portal_key}"

            logger.debug(f"api_url: {api_url}")
            # 从线程上下文中获取 cookie 信息
            req_ctx_header = ReqCtx.get_header()
            cookie = None
            if req_ctx_header and req_ctx_header.t_ai_source_cookie:
                cookie = req_ctx_header.t_ai_source_cookie
                logger.debug(f"从线程上下文获取到 cookie: {cookie[:50]}...")
            else:
                logger.warning("线程上下文中未找到 cookie 信息")

            # 设置请求头
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "en-US,en;q=0.9,ko;q=0.8",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Pragma": "no-cache",
                "Referer": referer,
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "Trantor2-App": portal_key,
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
            }

            # 只有在 team_id 有值时才添加团队相关的 headers
            if team_id is not None and str(team_id).strip():
                headers["Trantor2-Team"] = str(team_id)
                headers["Trantor2-TrantorTeamDTO"] = str(team_id)

            # 如果从线程上下文获取到 cookie，添加到请求头中
            if cookie is not None:
                cookie_str = str(cookie)
                if cookie_str.strip():
                    headers["Cookie"] = cookie_str

            # 发送请求获取模块配置
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success") and data.get("data", {}).get("props", {}).get("config", {}).get(
                            "loginCallbackUrl"
                        ):
                            portal_url = data["data"]["props"]["config"]["loginCallbackUrl"]
                            logger.debug(f"成功获取门户地址: {portal_url}")
                            return portal_url
                        else:
                            logger.warning(f"API 响应中未找到门户地址: {data}")
                    else:
                        logger.error(f"获取模块配置失败: {response.status} {response.text}")
        except Exception as e:
            import traceback

            logger.error(traceback.format_exc())
            logger.error(f"获取门户地址时发生错误: {str(e)}")

        return None

    async def call_agent_direct(
        self,
        session_id: str,
        agent_config: AgentConfig,
        input_content: str,
        portal_key: str = None,
    ) -> Dict[str, Any]:
        """调用 Agent 服务并获取 token 消耗信息

        Args:
            session_id: 会话ID
            agent_config: Agent配置
            input_content: 输入内容
            referer: HTTP 请求的 referer，用于动态生成 base URL

        Returns:
            Dict[str, Any]: 包含响应内容和 token 信息的字典，格式为 {"content": str, "totalTokens": int, "inputTokens": int, "outputTokens": int}
        """
        try:
            # 从 agent_config 中获取 agent_key
            agent_key = agent_config.key if hasattr(agent_config, "key") else agent_config.get("key")

            if not agent_key:
                raise ValueError("Agent 配置中缺少 key 字段")

            referer = ReqCtx.get_header().t_ai_source_referer
            logger.debug(f"调用 Agent HTTP API: {agent_key}")
            logger.debug(f"输入内容: {input_content}")
            logger.debug(f"Referer: {referer}")

            # 从 referer 中获取门户地址
            portal_referer = None
            if referer and portal_key:
                portal_referer = await self._get_portal_url_from_referer(referer, portal_key)
                if portal_referer:
                    logger.debug(f"解析到门户地址: {portal_referer}")
            logger.debug(f"portal_referer: {portal_referer}")
            http_client = AgentHttpClient(referer=portal_referer)

            # 使用 HTTP 客户端调用 Agent API
            result = await http_client.call_agent(
                agent_key=agent_key, session_id=session_id, user_content=input_content
            )

            logger.debug(f"Agent API 调用成功: {result}")
            return result

        except Exception as e:
            import traceback

            logger.error(traceback.format_exc())
            logger.error(f"调用 Agent 失败: {str(e)}")
            raise ValueError(f"调用 Agent 失败: {str(e)}")

    def calculate_accuracy(self, actual_outputs: List[Dict[str, Any]], expected_outputs: List[Dict[str, Any]]) -> float:
        """计算准确率"""
        if not actual_outputs or not expected_outputs:
            return 0.0

        correct = 0
        total = min(len(actual_outputs), len(expected_outputs))

        for actual, expected in zip(actual_outputs, expected_outputs):
            actual_content = actual.get("content", "").strip().lower()
            expected_content = str(expected).strip().lower()

            # 简单的文本匹配算法
            if actual_content and expected_content:
                # 包含匹配
                if expected_content in actual_content or actual_content in expected_content:
                    correct += 1
                # 关键词匹配
                elif self._calculate_text_similarity(actual_content, expected_content) > 0.5:
                    correct += 1

        return correct / total if total > 0 else 0.0

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单的词汇重叠）"""
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def calculate_metrics(
        self, results: List[Dict[str, Any]], expected_outputs: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """计算评测指标"""
        metrics = {
            "totalCount": len(results),
            "successCount": 0,
            "errorCount": 0,
            "accuracy": 0.0,
            "avgResponseTime": 0.0,
        }

        success_results = []
        response_times = []

        for result in results:
            if "error" in result:
                metrics["errorCount"] += 1
            else:
                metrics["successCount"] += 1
                success_results.append(result)

                # 收集响应时间
                if "responseTime" in result:
                    response_times.append(result["responseTime"])

        # 计算准确率
        if success_results and expected_outputs:
            metrics["accuracy"] = self.calculate_accuracy(success_results, expected_outputs)

        # 计算平均响应时间
        if response_times:
            metrics["avgResponseTime"] = sum(response_times) / len(response_times)

        return metrics
