# T-AI Agent Ops

一个专业的 AI Agent 评测和监控平台，支持数据集管理、Agent 性能评测、Langfuse 集成追踪等功能。

## ✨ 核心特性

### 🚀 高效 Agent 调用
- **本地直接调用**：优先使用本地 `run_streamed` 方法，避免 HTTP 网络开销
- **自动降级机制**：当本地服务不可用时自动降级到 HTTP 调用
- **性能优势**：减少网络延迟和序列化开销，提升 50%+ 执行效率
- **并发优化**：支持智能并发控制，本地调用和 HTTP 调用使用不同策略

### 📊 智能评测系统
- **多维度指标**：准确率、成功率、响应时间、错误统计
- **智能文本相似度**：包含匹配 + 词汇重叠度算法
- **流式处理**：支持大规模数据集的高效评测
- **实时监控**：任务进度追踪和状态监控

### 🔍 完整可观测性
- **Langfuse 深度集成**：与 Langfuse 评测系统完全集成
  - 自动数据集同步到 Langfuse
  - 创建完整的评测运行 (Dataset Runs)
  - 项目级别的详细追踪记录
  - 多维度评分和指标记录
- **传统追踪支持**：在深度集成不可用时自动降级到基础追踪
- **多租户支持**：按 Agent Key 进行数据隔离
- **丰富报告**：生成详细的评测报告和统计分析

## 🏗️ 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   评测任务管理   │    │   数据集管理      │    │ Langfuse深度集成 │
│                │    │                 │    │                │
│ • 任务创建      │    │ • 数据集CRUD     │    │ • 数据集同步    │
│ • 进度监控      │◄──►│ • 项目管理       │◄──►│ • 评测运行创建   │
│ • 状态管理      │    │ • 批量操作       │    │ • 项目级追踪    │
│ • 报告生成      │    │ • Langfuse同步   │    │ • 多维度评分    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  ▼
                  ┌─────────────────────────────────┐
                  │      高效 Agent 调用引擎        │
                  │                                │
                  │  ┌─────────────┐  ┌────────────┐ │
                  │  │ 本地直接调用 │  │ HTTP调用   │ │
                  │  │ (优先使用)  │  │ (降级方案) │ │
                  │  │ • 无网络开销 │  │ • 兼容性好 │ │
                  │  │ • 高性能    │  │ • 远程调用 │ │
                  │  └─────────────┘  └────────────┘ │
                  └─────────────────────────────────┘
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd packages/t-ai-agent-ops
pip install -r requirements.txt
```

### 2. 配置环境

创建 `.env` 文件：

```env
# Langfuse 配置
LANGFUSE_SECRET_KEY=your_secret_key
LANGFUSE_PUBLIC_KEY=your_public_key
LANGFUSE_HOST=https://your-langfuse-instance.com

# 可选：Agent 服务配置 (仅在本地服务不可用时需要)
AGENT_BASE_URL=http://localhost:8000
```

### 3. 创建数据集

```python
import asyncio
from t_ai_agent_ops.service.dataset_service import DatasetService

async def create_dataset():
    dataset_service = DatasetService()

    dataset_data = {
        "name": "AI问答评测数据集",
        "description": "用于评测AI问答性能",
        "items": [
            {
                "input": {"question": "什么是机器学习？"},
                "expected_output": {"answer": "机器学习是AI的分支..."},
            }
        ]
    }

    dataset = await dataset_service.create_dataset(dataset_data)
    return dataset["id"]
```

### 4. 配置 Agent

```python
agent_config = {
    "type": "Agent",
    "key": "GEN_MD$qa_evaluation_agent",
    "name": "智能问答助理",
    "props": {
        "model": {
            "providerType": "Azure",
            "name": "gpt-4.1",
            "type": "text_to_text",
            "setting": {
                "temperature": 0.3,
                "maxTokens": 2048
            }
        },
        "systemPrompt": "你是一个专业的技术问答助理。",
        "greetings": "你好，我是问答助理。"
    }
}

# 创建评测任务
task_data = {
    "name": "高效智能问答评测",
    "model_type": "gpt-4.1",
    "evaluation_type": "accuracy",
    "execution_time": "2024-01-20T10:00:00",
    "dataset_id": dataset_id,
    "agent_config": agent_config,
    "agent_base_url": "http://localhost:8000",  # 仅作为备用
    "agent_key": "my_qa_agent"
}

response = await client.post("/v1/evaluation/tasks/with-agent", json=task_data)
task = response.json()
task_id = task["id"]
```

### 5. 运行高效评测

```python
# 🚀 推荐方式：简单运行（使用本地直接调用）
response = await client.post(f"/v1/evaluation/tasks/{task_id}/run-simple")
result = response.json()

# 或者自定义运行参数
run_data = {
    "agent_base_url": "http://localhost:8000",  # 仅在本地服务不可用时使用
    "headers": {
        "T-AI-SOURCE-APP": "GEN_MD",
        "T-AI-SOURCE-TEAM": "203"
    }
}
response = await client.post(f"/v1/evaluation/tasks/{task_id}/run", json=run_data)
result = response.json()
```

### 6. 查看详细评测结果

```python
# 获取评测报告
response = await client.get(f"/v1/evaluation/tasks/{task_id}/report")
report = response.json()

print(f"Agent: {report['agent_name']}")
print(f"调用方式: {report.get('call_method', '未知')}")
print(f"准确率: {report['metrics']['accuracy']:.2%}")
print(f"成功率: {report['result_summary']['success_rate']:.2%}")
print(f"平均响应时间: {report['result_summary']['avg_response_time']:.2f}s")
print(f"Langfuse 追踪: {report['langfuse_trace_id']}")
```

## 🔧 API 文档

### 评测任务管理

| 接口 | 方法 | 描述 |
|------|------|------|
| `/v1/evaluation/tasks` | POST | 创建评测任务 |
| `/v1/evaluation/tasks/with-agent` | POST | 创建带Agent配置的评测任务 |
| `/v1/evaluation/tasks` | GET | 获取任务列表（支持agent_key过滤） |
| `/v1/evaluation/tasks/{task_id}` | GET | 获取任务详情 |
| `/v1/evaluation/tasks/{task_id}/run-simple` | POST | 🚀 简单运行评测（推荐） |
| `/v1/evaluation/tasks/{task_id}/run` | POST | 自定义运行评测 |
| `/v1/evaluation/tasks/{task_id}/report` | GET | 获取评测报告 |
| `/v1/evaluation/tasks/{task_id}/status` | GET | 获取任务状态 |

### 数据集管理

| 接口 | 方法 | 描述 |
|------|------|------|
| `/v1/datasets` | POST | 创建数据集 |
| `/v1/datasets` | GET | 获取数据集列表（支持agent_key过滤） |
| `/v1/datasets/{dataset_id}` | GET | 获取数据集详情 |

## ⚡ 性能优化原理

### 本地直接调用 vs HTTP 调用

```python
# 🚀 高效方式：本地直接调用
from t_ai_agent.agent_service import run_streamed

async def call_agent_direct(agent_request):
    # 直接调用本地方法，无网络开销
    response = await run_streamed(agent_request)
    return await process_stream_response(response)

# 📡 备用方式：HTTP 调用
async def call_agent_http(base_url, agent_request):
    # 通过 HTTP 调用远程服务
    async with httpx.AsyncClient() as client:
        response = await client.post(f"{base_url}/api/ai/agent/run_streamed",
                                   json=agent_request)
        return await process_http_response(response)
```

### 性能对比

| 指标 | 本地直接调用 | HTTP 调用 |
|------|-------------|-----------|
| **网络延迟** | ✅ 无 | ❌ 10-50ms |
| **序列化开销** | ✅ 最小 | ❌ JSON 序列化 |
| **连接开销** | ✅ 无 | ❌ TCP 连接建立 |
| **内存使用** | ✅ 低 | ❌ 中等 |
| **并发性能** | ✅ 高 | ❌ 受限于连接池 |
| **总体性能提升** | **50%+** | 基准 |

### 自动降级机制

```python
class AgentService:
    def __init__(self):
        # 检测本地服务可用性
        try:
            from t_ai_agent.agent_service import run_streamed
            self.use_local_agent = True
            logger.info("✅ 本地 Agent 服务可用，优先使用高效调用")
        except ImportError:
            self.use_local_agent = False
            logger.warning("⚠️ 本地 Agent 服务不可用，将使用 HTTP 调用")

    async def batch_call_agent_stream(self, ...):
        if self.use_local_agent:
            # 使用高效的本地调用
            return await self.batch_call_agent_direct(...)
        else:
            # 降级到 HTTP 调用
            return await self.batch_call_agent_http(...)
```

## 📊 智能评测算法

### 文本相似度计算

```python
def calculate_text_similarity(actual: str, expected: str) -> float:
    """
    智能文本相似度算法
    结合包含匹配和词汇重叠度
    """
    # 1. 包含匹配（高权重）
    if expected.lower() in actual.lower() or actual.lower() in expected.lower():
        return 0.8  # 高相似度

    # 2. 词汇重叠度计算
    words1 = set(actual.lower().split())
    words2 = set(expected.lower().split())

    if not words1 or not words2:
        return 0.0

    intersection = words1.intersection(words2)
    union = words1.union(words2)

    return len(intersection) / len(union)
```

### 多维度评测指标

```python
metrics = {
    "total_count": 总测试数,
    "success_count": 成功执行数,
    "error_count": 执行失败数,
    "accuracy": 准确率,
    "avg_response_time": 平均响应时间,
    "success_rate": 成功率
}
```

## 🔍 Langfuse 集成

### 深度集成架构

T-AI Agent Ops 与 Langfuse 评测系统实现了完整的深度集成：

```python
# 🔄 自动数据集同步
langfuse_dataset = await langfuse_client.sync_dataset_to_langfuse(
    local_dataset=dataset,
    force_update=False
)

# 🚀 创建完整评测运行
evaluation_run = await langfuse_client.run_integrated_evaluation(
    evaluation_task_id=task_id,
    dataset_name=dataset_name,
    agent_config=agent_config,
    evaluation_results=results,
    metrics=metrics
)
```

### 集成层级

1. **数据集同步**
   - 自动将本地数据集同步到 Langfuse
   - 包含完整的项目数据和元数据
   - 避免重复同步，智能增量更新

2. **评测运行创建**
   - 为每次评测创建独立的 Dataset Run
   - 包含完整的评测元数据和配置信息
   - 支持评测历史和版本对比

3. **项目级追踪**
   - 为每个测试项目创建独立的 Trace
   - 记录详细的输入输出和执行信息
   - 支持错误追踪和性能分析

4. **多维度评分**
   - 自动计算并记录准确率评分
   - 响应时间性能评分
   - 总体评测指标汇总

### 完整追踪记录

```python
# 创建评测运行
evaluation_run = await langfuse_client.create_evaluation_run(
    name="T-AI评测-{task_id}",
    dataset_name=dataset_name,
    metadata={
        "evaluation_task_id": task_id,
        "agent_config": agent_config,
        "source": "t-ai-agent-ops"
    }
)

# 为每个测试项创建追踪
for item, result in zip(dataset_items, results):
    trace = await langfuse_client.create_trace(
        name=f"{run_name}-项目{i+1}",
        input=dataset_item["input"],
        output=result["content"],
        metadata={
            "evaluation_run_id": run_id,
            "dataset_item_id": dataset_item["id"]
        }
    )

    # 创建数据集运行项目链接
    await langfuse_client.create_dataset_run_item(
        run_id=run_id,
        dataset_item_id=dataset_item["id"],
        trace_id=trace["id"]
    )

    # 记录评分
    await langfuse_client.create_score(
        trace_id=trace["id"],
        name="accuracy",
        value=accuracy_score,
        comment="项目准确率评分"
    )

# 创建汇总追踪
summary_trace = await langfuse_client.create_trace(
    name=f"{run_name}-汇总",
    input={"dataset_name": dataset_name},
    output={"metrics": metrics},
    tags=["evaluation", "summary"]
)
```

### 自动降级机制

系统具有智能的降级机制：

1. **优先使用深度集成**
   - 数据集同步成功 ✅ → 使用完整评测运行
   - 创建 Dataset Runs 和详细追踪

2. **自动降级到基础追踪**
   - 数据集同步失败 ⚠️ → 降级到传统追踪
   - 仍然记录基本的 Trace、Span、Score

3. **优雅错误处理**
   - Langfuse 服务不可用 → 继续本地评测
   - 记录警告但不影响评测流程

### 评测报告增强

评测报告包含完整的 Langfuse 集成信息：

```json
{
  "langfuse_integration": {
    "enabled": true,
    "evaluation_run_id": "run_123456",
    "langfuse_url": "https://cloud.langfuse.com/datasets/qa-dataset/runs/run_123456",
    "dataset_name": "qa-dataset",
    "integrated": true
  },
  "call_method": "direct",
  "metrics": {...},
  "result_summary": {...}
}
```

## 🛠️ 故障排除

### 本地服务不可用

如果遇到 "本地 Agent 服务不可用" 的警告：

1. **检查依赖安装**：
   ```bash
   pip install t-ai-agent
   ```

2. **验证模块导入**：
   ```python
   try:
       from t_ai_agent.agent_service import run_streamed
       print("✅ 本地服务可用")
   except ImportError as e:
       print(f"❌ 导入失败: {e}")
   ```

3. **手动配置 HTTP 调用**：
   ```python
   # 如果本地服务确实不可用，系统会自动降级到 HTTP 调用
   # 确保提供正确的 agent_base_url
   task_data["agent_base_url"] = "http://your-agent-service:8000"
   ```

### 性能调优建议

1. **并发设置**：
   ```python
   # 本地调用：可以使用更高并发
   max_concurrent = 5 if use_local_agent else 2

   # HTTP 调用：使用保守设置
   max_concurrent = 2
   ```

2. **超时配置**：
   ```python
   # 本地调用通常更快，可以设置较短超时
   timeout = 15.0 if use_local_agent else 30.0
   ```

3. **监控指标**：
   ```python
   # 关注这些性能指标
   - 平均响应时间
   - 成功率
   - 并发数
   - 错误率
   ```

## 📈 使用技巧

### 1. 选择最佳运行方式

```python
# 🚀 推荐：简单运行（零配置，高性能）
POST /v1/evaluation/tasks/{task_id}/run-simple

# 🔧 高级：自定义运行（可配置参数）
POST /v1/evaluation/tasks/{task_id}/run
```

### 2. 优化数据集设计

```python
# ✅ 好的设计
{
    "input": {"question": "明确具体的问题"},
    "expected_output": {"answer": "期望的具体答案"},
    "metadata": {"difficulty": "easy", "category": "基础"}
}

# ❌ 避免
{
    "input": "模糊的输入",
    "expected_output": "模糊的期望"
}
```

### 3. 监控和调试

```python
# 查看 Langfuse 追踪
print(f"Langfuse Trace: {report['langfuse_trace_id']}")

# 监控性能指标
print(f"调用方式: {report.get('call_method')}")
print(f"响应时间: {report['metrics']['avg_response_time']:.2f}s")
```

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境设置

```bash
# 克隆仓库
git clone <repository>
cd packages/t-ai-agent-ops

# 安装开发依赖
pip install -r requirements.txt
pip install -e .

# 运行测试
python -m pytest tests/

# 运行示例
python example_usage.py
```

## �� 许可证

MIT License
