import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session

from ..utils.timezone_utils import china_now
from .database import Dataset, DatasetItem, EvaluationTask


class DatasetCRUD:
    """数据集CRUD操作"""

    @staticmethod
    def create_dataset(
        db: Session,
        name: str,
        agent_key: str,
        description: Optional[str] = None,
        meta_info: Optional[str] = None,
        created_by: Optional[str] = None,
        team_id: Optional[str] = None,
    ) -> Dataset:
        """创建数据集"""
        db_dataset = Dataset(
            name=name,
            agent_key=agent_key,
            description=description,
            meta_info=meta_info,
            created_by=created_by,
            team_id=team_id,
            version=1,
        )
        db.add(db_dataset)
        db.commit()
        db.refresh(db_dataset)
        return db_dataset

    @staticmethod
    def get_dataset(db: Session, dataset_id: int) -> Optional[Dataset]:
        """获取数据集"""
        return db.query(Dataset).filter(Dataset.id == dataset_id).first()

    @staticmethod
    def get_dataset_by_id(db: Session, dataset_id: int) -> Optional[Dataset]:
        """通过ID获取数据集"""
        return db.query(Dataset).filter(Dataset.id == dataset_id).first()

    @staticmethod
    def get_dataset_by_name(db: Session, dataset_name: str) -> Optional[Dataset]:
        """通过名称获取数据集"""
        return db.query(Dataset).filter(Dataset.name == dataset_name).first()

    @staticmethod
    def get_datasets(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        agent_key: Optional[str] = None,
        team_id: Optional[str] = None,
        sort_by: str = "updated_at",
        sort_order: str = "desc",
    ) -> List[Dataset]:
        """获取数据集列表
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数
            agent_key: 按 agent key 过滤
            team_id: 按团队ID过滤
            sort_by: 排序字段
            sort_order: 排序方向
        """
        query = db.query(Dataset)
        if agent_key:
            query = query.filter(Dataset.agent_key == agent_key)
        if team_id:
            query = query.filter(Dataset.team_id == team_id)

        # 添加排序
        if sort_by == "created_at":
            if sort_order.lower() == "asc":
                query = query.order_by(Dataset.created_at.asc())
            else:
                query = query.order_by(Dataset.created_at.desc())
        elif sort_by == "updated_at":
            if sort_order.lower() == "asc":
                query = query.order_by(Dataset.updated_at.asc())
            else:
                query = query.order_by(Dataset.updated_at.desc())
        elif sort_by == "name":
            if sort_order.lower() == "asc":
                query = query.order_by(Dataset.name.asc())
            else:
                query = query.order_by(Dataset.name.desc())
        else:
            # 默认按更新时间倒序
            query = query.order_by(Dataset.updated_at.desc())

        return query.offset(skip).limit(limit).all()

    @staticmethod
    def get_datasets_count(db: Session, agent_key: Optional[str] = None, team_id: Optional[str] = None) -> int:
        """获取数据集总数
        Args:
            db: 数据库会话
            agent_key: 按 agent key 过滤
            team_id: 按团队ID过滤
        """
        query = db.query(Dataset)
        if agent_key:
            query = query.filter(Dataset.agent_key == agent_key)
        if team_id:
            query = query.filter(Dataset.team_id == team_id)
        return query.count()

    @staticmethod
    def update_dataset(
        db: Session,
        dataset_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        agent_key: Optional[str] = None,
        meta_info: Optional[str] = None,
        updated_by: Optional[str] = None,
        team_id: Optional[str] = None,
    ) -> Optional[Dataset]:
        """更新数据集"""
        db_dataset = DatasetCRUD.get_dataset(db, dataset_id)
        if not db_dataset:
            return None

        if name is not None:
            db_dataset.name = name
        if description is not None:
            db_dataset.description = description
        if agent_key is not None:
            db_dataset.agent_key = agent_key
        if meta_info is not None:
            db_dataset.meta_info = meta_info
        if updated_by is not None:
            db_dataset.updated_by = updated_by
        if team_id is not None:
            db_dataset.team_id = team_id

        # 增加版本号
        db_dataset.version += 1
        db_dataset.updated_at = china_now()
        db.commit()
        db.refresh(db_dataset)
        return db_dataset

    @staticmethod
    def delete_dataset(db: Session, dataset_id: int) -> bool:
        """删除数据集"""
        db_dataset = DatasetCRUD.get_dataset(db, dataset_id)
        if not db_dataset:
            return False

        db.delete(db_dataset)
        db.commit()
        return True


class DatasetItemCRUD:
    """数据集项目CRUD操作"""

    @staticmethod
    def create_item(
        db: Session,
        dataset_id: int,
        input_data: str,
        expected_output: Optional[str] = None,
        meta_info: Optional[str] = None,
        created_by: Optional[str] = None,
        team_id: Optional[str] = None,
    ) -> DatasetItem:
        """创建数据集项目"""
        db_item = DatasetItem(
            dataset_id=dataset_id,
            input_data=input_data,
            expected_output=expected_output,
            meta_info=meta_info,
            created_by=created_by,
            team_id=team_id,
            version=1,
        )
        db.add(db_item)
        db.commit()
        db.refresh(db_item)
        return db_item

    @staticmethod
    def get_items(db: Session, dataset_id: int, skip: int = 0, limit: int = 100) -> List[DatasetItem]:
        """获取数据集项目列表"""
        return db.query(DatasetItem).filter(DatasetItem.dataset_id == dataset_id).offset(skip).limit(limit).all()

    @staticmethod
    def update_item(
        db: Session,
        item_id: int,
        input_data: Optional[str] = None,
        expected_output: Optional[str] = None,
        meta_info: Optional[str] = None,
        updated_by: Optional[str] = None,
        team_id: Optional[str] = None,
    ) -> Optional[DatasetItem]:
        """更新数据集项目"""
        db_item = db.query(DatasetItem).filter(DatasetItem.id == item_id).first()
        if not db_item:
            return None

        if input_data is not None:
            db_item.input_data = input_data
        if expected_output is not None:
            db_item.expected_output = expected_output
        if meta_info is not None:
            db_item.meta_info = meta_info
        if updated_by is not None:
            db_item.updated_by = updated_by
        if team_id is not None:
            db_item.team_id = team_id

        # 增加版本号
        db_item.version += 1
        db_item.updated_at = china_now()
        db.commit()
        db.refresh(db_item)
        return db_item


class EvaluationTaskCRUD:
    """评估任务CRUD操作"""

    @staticmethod
    def create_task(
        db: Session,
        dataset_id: int,
        name: str,
        portal_key: Optional[str] = None,
        agent_key: Optional[str] = None,
        description: Optional[str] = None,
        meta_info: Optional[str] = None,
        created_by: Optional[str] = None,
        team_id: Optional[str] = None,
    ) -> EvaluationTask:
        """创建评估任务"""
        db_task = EvaluationTask(
            dataset_id=dataset_id,
            name=name,
            agent_key=agent_key,
            description=description,
            meta_info=meta_info,
            portal_key=portal_key,
            status="pending",
            created_by=created_by,
            team_id=team_id,
            version=1,
        )
        db.add(db_task)
        db.commit()
        db.refresh(db_task)
        return db_task

    @staticmethod
    def get_task(db: Session, task_id: int) -> Optional[EvaluationTask]:
        """获取评估任务"""
        return db.query(EvaluationTask).filter(EvaluationTask.id == task_id).first()

    @staticmethod
    def get_tasks_by_dataset(db: Session, dataset_id: int, skip: int = 0, limit: int = 100) -> List[EvaluationTask]:
        """获取数据集相关的评估任务列表"""
        return db.query(EvaluationTask).filter(EvaluationTask.dataset_id == dataset_id).offset(skip).limit(limit).all()

    @staticmethod
    def get_tasks(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        agent_key: Optional[str] = None,
        team_id: Optional[str] = None,
        sort_by: str = "updated_at",
        sort_order: str = "desc",
    ) -> List[EvaluationTask]:
        """获取评估任务列表，支持按agent_key和team_id过滤和排序"""
        query = db.query(EvaluationTask)
        if agent_key:
            query = query.filter(EvaluationTask.agent_key == agent_key)
        if team_id:
            query = query.filter(EvaluationTask.team_id == team_id)

        # 添加排序
        if sort_by == "created_at":
            if sort_order.lower() == "asc":
                query = query.order_by(EvaluationTask.created_at.asc())
            else:
                query = query.order_by(EvaluationTask.created_at.desc())
        elif sort_by == "updated_at":
            if sort_order.lower() == "asc":
                query = query.order_by(EvaluationTask.updated_at.asc())
            else:
                query = query.order_by(EvaluationTask.updated_at.desc())
        elif sort_by == "name":
            if sort_order.lower() == "asc":
                query = query.order_by(EvaluationTask.name.asc())
            else:
                query = query.order_by(EvaluationTask.name.desc())
        elif sort_by == "status":
            if sort_order.lower() == "asc":
                query = query.order_by(EvaluationTask.status.asc())
            else:
                query = query.order_by(EvaluationTask.status.desc())
        else:
            # 默认按更新时间倒序
            query = query.order_by(EvaluationTask.updated_at.desc())

        return query.offset(skip).limit(limit).all()

    @staticmethod
    def get_tasks_count(db: Session, agent_key: Optional[str] = None, team_id: Optional[str] = None) -> int:
        """获取评估任务总数，支持按agent_key和team_id过滤"""
        query = db.query(EvaluationTask)
        if agent_key:
            query = query.filter(EvaluationTask.agent_key == agent_key)
        if team_id:
            query = query.filter(EvaluationTask.team_id == team_id)
        return query.count()

    @staticmethod
    def update_task_status(
        db: Session,
        task_id: int,
        status: str,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        progress: Optional[float] = None,
        updated_by: Optional[str] = None,
    ) -> Optional[EvaluationTask]:
        """更新评估任务状态"""
        db_task = EvaluationTaskCRUD.get_task(db, task_id)
        if not db_task:
            return None

        db_task.status = status
        if result is not None:
            db_task.result = result
        if error_message is not None:
            db_task.error_message = error_message
        if progress is not None:
            db_task.progress = progress
        if updated_by is not None:
            db_task.updated_by = updated_by

        # 增加版本号
        db_task.version += 1
        db_task.updated_at = china_now()
        db.commit()
        db.refresh(db_task)
        return db_task

    @staticmethod
    def update_task_progress(
        db: Session, task_id: int, progress: float, updated_by: Optional[str] = None
    ) -> Optional[EvaluationTask]:
        """更新评估任务进度"""
        db_task = EvaluationTaskCRUD.get_task(db, task_id)
        if not db_task:
            return None

        # 确保进度值在 0.0 到 1.0 之间
        progress = max(0.0, min(1.0, progress))
        db_task.progress = progress
        if updated_by is not None:
            db_task.updated_by = updated_by

        # 增加版本号
        db_task.version += 1
        db_task.updated_at = china_now()

        db.commit()
        db.refresh(db_task)
        return db_task

    @staticmethod
    def update_task(
        db: Session,
        task_id: int,
        name: Optional[str] = None,
        evaluation_type: Optional[str] = None,
        dataset_id: Optional[int] = None,
        agent_key: Optional[str] = None,
        agent_config: Optional[str] = None,
        langfuse_trace_id: Optional[str] = None,
        langfuse_metadata: Optional[str] = None,
        evaluation_results: Optional[str] = None,
        metrics: Optional[str] = None,
        meta_info: Optional[str] = None,
        updated_by: Optional[str] = None,
        team_id: Optional[str] = None,
    ) -> Optional[EvaluationTask]:
        """通用的评估任务更新方法"""
        db_task = EvaluationTaskCRUD.get_task(db, task_id)
        if not db_task:
            return None

        # 更新基本字段
        if name is not None:
            db_task.name = name
        if dataset_id is not None:
            db_task.dataset_id = dataset_id
        if agent_key is not None:
            db_task.agent_key = agent_key
        if evaluation_results is not None:
            db_task.result = evaluation_results
        if updated_by is not None:
            db_task.updated_by = updated_by
        if team_id is not None:
            db_task.team_id = team_id

        # 更新 meta_info 字段
        if db_task.meta_info is None:
            db_task.meta_info = "{}"

        # 解析现有的 meta_info
        try:
            current_meta = json.loads(db_task.meta_info) if db_task.meta_info else {}
        except (json.JSONDecodeError, TypeError):
            current_meta = {}

        # 更新 meta_info 字段
        if evaluation_type is not None:
            current_meta["evaluationType"] = evaluation_type
        if agent_config is not None:
            current_meta["agentConfig"] = agent_config
        if langfuse_trace_id is not None:
            current_meta["langfuseTraceId"] = langfuse_trace_id
        if langfuse_metadata is not None:
            current_meta["langfuseMetadata"] = langfuse_metadata
        if metrics is not None:
            current_meta["metrics"] = metrics

        # 更新 meta_info
        if current_meta:
            db_task.meta_info = json.dumps(current_meta, ensure_ascii=False)

        # 增加版本号
        db_task.version += 1
        db_task.updated_at = china_now()

        db.commit()
        db.refresh(db_task)
        return db_task
