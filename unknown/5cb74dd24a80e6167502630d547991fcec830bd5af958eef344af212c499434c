import asyncio
import threading
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import httpx
from loguru import logger
from pydantic import BaseModel, Field

from .agent_factory import get_agent_from_cache
from .model.agent_dsl import AgentMeta


@dataclass
class WarmupConfig:
    """Agent预热配置"""

    # 基础配置
    warmup_enabled: bool = False
    agent_metadata_domain: str = "http://localhost:8080"  # 只配置域名
    warmup_project_codes: Optional[List[str]] = None  # 需要预热的项目代码列表

    # 写死的URL路径
    _agent_metadata_path: str = "/api/trantor/devops/service/agent/page"

    # 固定配置（不可配置）
    agent_metadata_headers: Optional[Dict[str, str]] = None
    agent_metadata_timeout: int = 30
    warmup_batch_size: int = 5
    warmup_delay: float = 0.1
    warmup_retry_count: int = 3
    warmup_retry_delay: float = 1.0
    log_level: str = "INFO"

    def __post_init__(self):
        if self.agent_metadata_headers is None:
            self.agent_metadata_headers = {"Content-Type": "application/json", "Accept": "application/json"}
        if self.warmup_project_codes is None:
            self.warmup_project_codes = []

    @property
    def agent_metadata_url(self) -> str:
        """获取完整的Agent元数据查询URL"""
        return f"{self.agent_metadata_domain.rstrip('/')}{self._agent_metadata_path}"


class AgentMetadataClient:
    """Agent元数据客户端"""

    def __init__(self, config: WarmupConfig):
        self.config = config
        self.client = httpx.AsyncClient(timeout=httpx.Timeout(self.config.agent_metadata_timeout))

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    async def get_agent_metadata_by_project(self, project_code: str, page_handler):
        """
        按项目代码获取Agent元数据（支持分页查询和每页回调处理）

        Args:
            project_code: 项目代码
            page_handler: 每页数据的回调处理函数
        """
        page = 1
        page_size = 100  # 每页大小
        total_fetched = 0

        try:
            while True:
                logger.debug(f"Fetching agent metadata for project {project_code}, page {page}...")

                # 构建分页参数，包含teamCode
                params: Dict[str, Any] = {"pageNo": page, "pageSize": page_size, "teamCode": project_code}

                response = await self.client.get(
                    self.config.agent_metadata_url, headers=self.config.agent_metadata_headers, params=params
                )
                response.raise_for_status()

                data = response.json()

                # 处理不同的响应格式
                current_page_data = []
                has_more = False

                # 处理元数据中心的 Response<Paging<Agent>> 格式
                if isinstance(data, dict) and "success" in data and "data" in data:
                    # 检查请求是否成功
                    if not data.get("success", False):
                        logger.error(f"API request failed for project {project_code}: {data}")
                        break

                    # 获取 Paging<Agent> 数据
                    paging_data = data["data"]
                    if isinstance(paging_data, dict) and "data" in paging_data and "total" in paging_data:
                        current_page_data = paging_data["data"] if isinstance(paging_data["data"], list) else []
                        total_count = paging_data["total"]

                        # 判断是否还有更多数据
                        has_more = total_fetched + len(current_page_data) < total_count
                    else:
                        logger.warning(
                            f"Invalid Paging structure in response data for project {project_code}: {paging_data}"
                        )
                        break
                else:
                    logger.warning(
                        f"Unexpected response format for project {project_code}, expected Response<Paging<Agent>> structure: {type(data)}"
                    )
                    break

                if not current_page_data:
                    logger.debug(f"No more data found for project {project_code} on page {page}")
                    break

                logger.debug(
                    f"Project {project_code}, Page {page}: Found {len(current_page_data)} agents, total fetched: {total_fetched + len(current_page_data)}, total available: {total_count}"
                )

                # 立即处理当前页数据
                await page_handler(current_page_data, project_code, page)
                total_fetched += len(current_page_data)

                if not has_more:
                    logger.debug(f"Reached end of data for project {project_code} at page {page}")
                    break

                page += 1
                # 添加页面间延迟，避免请求过快
                await asyncio.sleep(0.1)

        except httpx.HTTPStatusError as e:
            logger.error(
                f"HTTP error when fetching agent metadata for project {project_code}: {e.response.status_code} - {e.response.text}"
            )
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error when fetching agent metadata for project {project_code}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error when fetching agent metadata for project {project_code}: {e}")
            raise


class AgentWarmupService:
    """Agent预热服务"""

    def __init__(self, config: WarmupConfig):
        self.config = config
        self.metadata_client = AgentMetadataClient(config)

    async def update_single_agent_cache(self, agent_metadata: Dict[str, Any]):
        """
        更新单个Agent的缓存

        Args:
            agent_metadata: Agent元数据
        """
        start_time = time.time()
        agent_key = agent_metadata.get("key", "unknown")
        agent_name = agent_metadata.get("name", "unknown")

        try:
            # 解析Agent元数据
            agent_meta = AgentMeta.from_dict(agent_metadata)

            # 预热/更新Agent缓存
            logger.debug(f"Updating cache for agent: {agent_key}")
            agent, _ = await get_agent_from_cache(agent_meta)

            warmup_time = time.time() - start_time
            logger.info(f"✅ 预热成功: {agent_key} ({agent_name}) - {warmup_time:.2f}s")

        except Exception as e:
            warmup_time = time.time() - start_time
            logger.error(f"❌ 预热失败: {agent_key} ({agent_name}) - {warmup_time:.2f}s - {str(e)}")

    async def warmup_agents_batch(self, agent_metadata_list: List[Dict[str, Any]]):
        """
        批量预热Agents

        Args:
            agent_metadata_list: Agent元数据列表
        """

        async def warmup_single_agent(agent_metadata: Dict[str, Any]):
            """预热单个Agent"""
            start_time = time.time()
            agent_key = agent_metadata.get("key", "unknown")
            agent_name = agent_metadata.get("name", "unknown")

            try:
                # 解析Agent元数据
                agent_meta = AgentMeta.from_dict(agent_metadata)

                # 预热Agent（会自动缓存）
                logger.debug(f"Warming up agent: {agent_key}")
                agent, _ = await get_agent_from_cache(agent_meta)

                warmup_time = time.time() - start_time
                logger.info(f"✅ 预热成功: {agent_key} ({agent_name}) - {warmup_time:.2f}s")

            except Exception as e:
                warmup_time = time.time() - start_time
                logger.error(f"❌ 预热失败: {agent_key} ({agent_name}) - {warmup_time:.2f}s - {str(e)}")

        for i in range(0, len(agent_metadata_list), self.config.warmup_batch_size):
            batch = agent_metadata_list[i : i + self.config.warmup_batch_size]

            logger.info(f"🔄 开始预热批次 {i // self.config.warmup_batch_size + 1}，共 {len(batch)} 个 Agents")

            # 并发预热当前批次
            tasks = [warmup_single_agent(agent_metadata) for agent_metadata in batch]
            await asyncio.gather(*tasks, return_exceptions=True)

            # 批次间延迟
            if i + self.config.warmup_batch_size < len(agent_metadata_list):
                await asyncio.sleep(self.config.warmup_delay)

    async def warmup_agents_by_projects(self):
        """
        按项目代码预热Agents，每页查询后立即预热
        """
        start_time = time.time()

        try:
            async with self.metadata_client:
                # 如果配置了特定的项目代码，按项目预热；否则预热所有项目
                project_codes = self.config.warmup_project_codes or [""]

                for project_code in project_codes:
                    logger.info(f"🚀 开始预热项目: {project_code}")

                    # 定义每页数据的处理函数
                    async def page_handler(page_data: List[Dict[str, Any]], proj_code: str, page_num: int):
                        if not page_data:
                            return

                        logger.info(f"📄 项目 {proj_code}，第 {page_num} 页：开始预热 {len(page_data)} 个 Agents")

                        # 立即预热当前页的Agents
                        await self.warmup_agents_batch(page_data)

                    # 按项目查询并逐页预热
                    await self.metadata_client.get_agent_metadata_by_project(project_code, page_handler)

                    logger.info(f"✅ 项目 {project_code} 预热完成")

                # 统计总体结果
                total_time = time.time() - start_time
                logger.info(f"🎉 所有项目 Agent 预热完成，总耗时: {total_time:.2f}s")

        except Exception as e:
            logger.error(f"❌ 项目预热失败: {e}")
            raise

    async def warmup_all_agents(self):
        """
        预热所有Agents，统一使用按项目预热方法
        """
        await self.warmup_agents_by_projects()


# 便利函数
async def warmup_all_agents(config: Optional[WarmupConfig] = None):
    """
    预热所有Agents的便利函数

    Args:
        config: 预热配置，如果为None则使用默认配置
    """
    if config is None:
        config = WarmupConfig()

    if not config.warmup_enabled:
        logger.info("Agent warmup is disabled")
        return

    service = AgentWarmupService(config)
    await service.warmup_all_agents()


async def update_agent_cache(agent_metadata: Dict[str, Any], config: Optional[WarmupConfig] = None):
    """
    更新单个Agent缓存的便利函数

    Args:
        agent_metadata: Agent元数据
        config: 预热配置，如果为None则使用默认配置
    """
    if config is None:
        config = get_warmup_config()

    if not config.warmup_enabled:
        logger.info("Agent warmup is disabled")
        return

    service = AgentWarmupService(config)
    await service.update_single_agent_cache(agent_metadata)


# 应用集成功能
def get_warmup_config() -> WarmupConfig:
    """从全局设置获取预热配置"""
    from t_ai_app import G

    settings = G.APP_SETTING.agent_warmup
    headers = {"Content-Type": "application/json", "Accept": "application/json"}

    # 解析项目代码列表
    project_codes = []
    if settings.warmup_project_codes:
        project_codes = [code.strip() for code in settings.warmup_project_codes.split(",") if code.strip()]

    return WarmupConfig(
        warmup_enabled=settings.warmup_enabled,
        agent_metadata_domain=settings.agent_metadata_domain,
        warmup_project_codes=project_codes,
        agent_metadata_headers=headers,
    )


async def startup_agent_warmup(custom_config: Optional[WarmupConfig] = None):
    """
    项目启动时的Agent预热函数

    Args:
        custom_config: 自定义配置，如果为None则使用全局设置配置
    """
    try:
        config = custom_config if custom_config else get_warmup_config()

        if not config.warmup_enabled:
            logger.info("Agent warmup is disabled, skipping...")
            return

        logger.info("🚀 Starting agent warmup process...")

        # 执行预热
        await warmup_all_agents(config)

        logger.info("🎉 Agent warmup process completed successfully")

    except Exception as e:
        logger.error(f"❌ Agent warmup process failed: {e}")
        # 预热失败不影响应用启动


async def startup_agent_warmup_async():
    """异步启动Agent预热功能"""
    try:
        from t_ai_app import G

        # 检查预热是否启用
        if not G.APP_SETTING.agent_warmup.warmup_enabled:
            logger.info("Agent warmup is disabled, skipping...")
            return

        logger.info("Starting agent warmup in background...")
        await startup_agent_warmup()
        logger.info("Agent warmup completed successfully")

    except Exception as e:
        logger.error(f"Agent warmup failed: {e}")
        # 预热失败不影响应用启动


def start_warmup_in_background():
    """在后台线程中启动预热任务"""

    def run_warmup():
        try:
            # 创建新的事件循环用于预热任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(startup_agent_warmup_async())
        except Exception as e:
            logger.error(f"Background warmup thread failed: {e}")
        finally:
            loop.close()

    # 创建后台线程执行预热
    warmup_thread = threading.Thread(target=run_warmup, daemon=True, name="agent-warmup")
    warmup_thread.start()
    logger.info("Agent warmup started in background thread")
