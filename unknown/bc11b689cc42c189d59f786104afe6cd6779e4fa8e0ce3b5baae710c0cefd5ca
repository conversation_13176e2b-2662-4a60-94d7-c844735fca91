import json


def safe_json_loads(s: str):
    try:
        return json.loads(s)
    except json.JSONDecodeError:
        fixed = auto_fix_brackets(s)
        return json.loads(fixed)


def auto_fix_brackets(s: str) -> str:
    stack = []
    for ch in s:
        if ch in "{[":
            stack.append(ch)
        elif ch == "}":
            if stack and stack[-1] == "{":
                stack.pop()
        elif ch == "]":
            if stack and stack[-1] == "[":
                stack.pop()
    for opener in reversed(stack):
        if opener == "{":
            s += "}"
        elif opener == "[":
            s += "]"
    return s
