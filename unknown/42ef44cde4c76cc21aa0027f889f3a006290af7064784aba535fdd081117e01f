from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from ..utils.timezone_utils import china_now


class DatasetItem(BaseModel):
    """数据集项目模型"""

    input: str = Field(..., description="输入数据")
    expected_output: Optional[str] = Field(None, alias="expectedOutput", description="期望输出")
    metadata: Optional[str] = Field(None, description="元数据")

    model_config = ConfigDict(populate_by_name=True)


class DatasetItemCreate(DatasetItem):
    """创建数据集项目的请求模型"""

    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class DatasetItemUpdate(BaseModel):
    """更新数据集项目的请求模型"""

    id: int = Field(..., description="项目ID")
    input: Optional[str] = Field(None, description="输入数据")
    expected_output: Optional[str] = Field(None, alias="expectedOutput", description="期望输出")
    metadata: Optional[str] = Field(None, description="元数据")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class DatasetItemInDB(DatasetItem):
    """数据库中的数据集项目模型"""

    id: int = Field(..., description="项目ID")
    dataset_id: int = Field(..., alias="datasetId", description="所属数据集ID")
    created_at: datetime = Field(default_factory=china_now, alias="createdAt")
    updated_at: datetime = Field(default_factory=china_now, alias="updatedAt")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")
    version: int = Field(default=1, description="版本号")

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class DatasetCreate(BaseModel):
    """创建数据集请求模型"""

    name: str = Field(..., description="数据集名称")
    description: Optional[str] = Field(None, description="数据集描述")
    agent_key: str = Field(..., alias="agentKey", description="关联的 agent key")
    metadata: Optional[str] = Field(None, description="数据集元数据")
    items: Optional[List[DatasetItemCreate]] = Field(None, description="数据集项目列表")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class DatasetUpdate(BaseModel):
    """更新数据集请求模型"""

    name: Optional[str] = Field(None, description="数据集名称")
    description: Optional[str] = Field(None, description="数据集描述")
    agent_key: Optional[str] = Field(None, alias="agentKey", description="关联的 agent key")
    metadata: Optional[str] = Field(None, description="数据集元数据")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class DatasetInDB(BaseModel):
    """数据库中的数据集模型"""

    id: int = Field(..., description="数据集ID")
    name: str = Field(..., description="数据集名称")
    description: Optional[str] = Field(None, description="数据集描述")
    agent_key: str = Field(..., alias="agentKey", description="关联的 agent key")
    metadata: Optional[str] = Field(None, description="数据集元数据")
    created_at: datetime = Field(default_factory=china_now, alias="createdAt")
    updated_at: datetime = Field(default_factory=china_now, alias="updatedAt")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")
    version: int = Field(default=1, description="版本号")

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)
