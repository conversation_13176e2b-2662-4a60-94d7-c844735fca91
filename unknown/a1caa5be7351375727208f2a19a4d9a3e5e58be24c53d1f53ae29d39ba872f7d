import asyncio
from dataclasses import dataclass
from typing import Any, Literal, Optional, Tuple

import httpx
from openai import OpenAI
from openai.types.responses import ResponseInputFileParam, ResponseInputImageParam
from t_ai_app import G
from t_ai_common.utils.common import parse_file_name, parse_file_type


@dataclass
class AgentAttachment:
    type: Literal["image", "file"]
    data: str
    filename: Optional[str] = "unknown"

    def to_agent_input(self):
        if self.type == "image":
            return ResponseInputImageParam(type="input_image", detail="auto", image_url=self.data)
        elif self.type == "file":
            return ResponseInputFileParam(type="input_file", file_id=self.data, filename=self.filename)
        else:
            raise ValueError(f"Unsupported attachment type: {self.type}")


async def process_attachments(attachments):
    tasks = [process_attachment(att) for att in attachments]
    return [att for att in await asyncio.gather(*tasks) if att]


async def process_attachment(att) -> Optional[AgentAttachment]:
    # 附件支持两种格式，一种是字典列表，一种是字符串列表
    if isinstance(att, dict):
        attachment_url = att.get("encodeUrl") or att.get("url")
        file_type = att["fileType"]
        filename = att.get("fileName", "unknown")
    else:
        attachment_url = att
        file_type = parse_file_type(attachment_url)
        filename = parse_file_name(attachment_url)

    if not attachment_url:
        return None

    if file_type == "image":
        return AgentAttachment(type="image", data=attachment_url, filename=filename)
    else:
        file_data = await fetch_attachment(attachment_url)
        if not file_data:
            return None
        file_object = await upload_file()
        if file_object is None:
            return None
        return AgentAttachment(type="file", data=file_object.id, filename=filename)


async def fetch_attachment(url: str) -> bytes | None:
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        if response.status_code != 200:
            return None
        return response.content


async def create_client(model_id: str = None) -> OpenAI:
    """创建 OpenAI 客户端实例"""
    kwargs = {
        "api_key": G.APP_SETTING.ai_proxy.ai_proxy_api_key,
        "base_url": G.APP_SETTING.ai_proxy.ai_proxy_base_url,
    }
    if model_id:
        kwargs["default_headers"] = {"X-AI-Proxy-Model-Id": model_id}
    return OpenAI(**kwargs)


async def create_file(client: OpenAI, purpose: str, file_data: bytes) -> Optional[Any]:
    """创建文件对象"""
    if not file_data:
        return None
    return client.files.create(
        file=file_data,
        purpose=purpose,
    )


async def create_client_and_file(purpose: str, file_data: bytes) -> Tuple[OpenAI, Optional[Any]]:
    """创建客户端和文件对象（向后兼容）"""
    client = await create_client()
    file_object = await create_file(client, purpose, file_data)
    return client, file_object


async def upload_file() -> Any | None:
    # 目前使用 t_ai_web.routers.attachment.analyze_attachment 解析文件
    return None
