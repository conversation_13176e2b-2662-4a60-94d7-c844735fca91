from fastapi import Fast<PERSON>I
from fastapi.middleware.cors import CORSMiddleware
from t_ai_agent_ops.api.dataset import router as dataset_router
from t_ai_agent_ops.api.evaluation import router as evaluation_router
from t_ai_web.middlewares.req_ctx_middleware import ReqCtxMiddleWare


def create_app() -> FastAPI:
    """创建 FastAPI 应用"""
    app = FastAPI(
        title="T-AI Agent Ops API",
        description="T-AI Agent Operations API for evaluation tasks and test datasets",
        version="1.0.0",
    )

    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该设置具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 添加请求上下文中间件
    app.add_middleware(ReqCtxMiddleWare)

    # 注册路由
    app.include_router(evaluation_router)
    app.include_router(dataset_router)

    return app


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(create_app(), host="0.0.0.0", port=8000)
