from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from t_ai_agent_ops.model.common import PaginationParams, error_response, success_response
from t_ai_agent_ops.model.dataset import (
    DatasetCreate,
    DatasetItemCreate,
    DatasetItemUpdate,
    DatasetUpdate,
)
from t_ai_agent_ops.service.dataset_service import DatasetService
from t_ai_agent_ops.utils.req_ctx_helper import get_current_team_id

router = APIRouter(prefix="/v1/datasets")


# 依赖注入
def get_dataset_service() -> DatasetService:
    return DatasetService()


@router.post("", response_model=Dict[str, Any], tags=["测试数据集"])
async def create_dataset(
    dataset_create: DatasetCreate, service: DatasetService = Depends(get_dataset_service)
) -> Dict[str, Any]:
    """创建测试数据集"""
    try:
        result = await service.create_dataset(dataset_create)
        return success_response(data=result)
    except Exception as e:
        return error_response(message=str(e))


@router.get("", response_model=Dict[str, Any], tags=["测试数据集"])
async def list_datasets(
    agentKey: Optional[str] = Query(None, description="根据 agent key 过滤数据集"),
    pageNumber: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(20, ge=1, le=100, description="每页条数，默认20条，最大100条"),
    sortBy: str = Query("updated_at", description="排序字段，支持：updated_at, created_at, name"),
    sortOrder: str = Query("desc", description="排序方向，asc 或 desc"),
    service: DatasetService = Depends(get_dataset_service),
) -> Dict[str, Any]:
    """获取测试数据集分页列表，支持按更新时间倒序排序"""
    try:
        # 验证排序参数
        valid_sort_fields = ["updated_at", "created_at", "name"]
        if sortBy not in valid_sort_fields:
            return error_response(message=f"无效的排序字段，支持：{', '.join(valid_sort_fields)}")

        if sortOrder.lower() not in ["asc", "desc"]:
            return error_response(message="排序方向必须是 asc 或 desc")

        pagination = PaginationParams(pageNumber=pageNumber, pageSize=pageSize, sortBy=sortBy, sortOrder=sortOrder)
        # 添加团队ID过滤
        pagination.team_id = get_current_team_id()
        result = await service.list_datasets(pagination=pagination, agent_key=agentKey)
        return success_response(data=result.dict())
    except Exception as e:
        return error_response(message=str(e))


@router.get("/{dataset_id}", response_model=Dict[str, Any], tags=["测试数据集"])
async def get_dataset(dataset_id: int, service: DatasetService = Depends(get_dataset_service)) -> Dict[str, Any]:
    """获取测试数据集详情"""
    try:
        result = await service.get_dataset(dataset_id)
        if not result:
            return error_response(message="Dataset not found")
        return success_response(data=result)
    except Exception as e:
        return error_response(message=str(e))


@router.put("/{dataset_id}", response_model=Dict[str, Any], tags=["测试数据集"])
async def update_dataset(
    dataset_id: int, dataset_update: DatasetUpdate, service: DatasetService = Depends(get_dataset_service)
) -> Dict[str, Any]:
    """更新测试数据集"""
    try:
        result = await service.update_dataset(dataset_id, dataset_update)
        return success_response(data=result)
    except Exception as e:
        return error_response(message=str(e))


@router.delete("/{dataset_id}", response_model=Dict[str, Any], tags=["测试数据集"])
async def delete_dataset(dataset_id: int, service: DatasetService = Depends(get_dataset_service)) -> Dict[str, Any]:
    """删除测试数据集"""
    try:
        await service.delete_dataset(dataset_id)
        return success_response(message="Dataset deleted successfully")
    except Exception as e:
        return error_response(message=str(e))


@router.post("/{dataset_id}/items", response_model=Dict[str, Any], tags=["测试数据集"])
async def create_dataset_item(
    dataset_id: int, item_create: DatasetItemCreate, service: DatasetService = Depends(get_dataset_service)
) -> Dict[str, Any]:
    """创建数据集项目"""
    try:
        # Get dataset details to extract the name
        dataset = await service.get_dataset(dataset_id)
        dataset_id = dataset.get("id")
        if not dataset_id:
            return error_response(message="Dataset not found")
        result = await service.create_dataset_item(dataset_id, item_create)
        return success_response(data=result)
    except Exception as e:
        return error_response(message=str(e))


@router.get("/{dataset_id}/items", response_model=Dict[str, Any], tags=["测试数据集"])
async def get_dataset_items(dataset_id: int, service: DatasetService = Depends(get_dataset_service)) -> Dict[str, Any]:
    """获取数据集项目列表"""
    try:
        if dataset_id is None:
            return error_response(message="Dataset id is required")

        result = await service.get_dataset_items(dataset_id)
        return success_response(data=result)
    except Exception as e:
        return error_response(message=str(e))


@router.put("/{dataset_id}/items", response_model=Dict[str, Any], tags=["测试数据集"])
async def update_dataset_item(
    dataset_id: int, item_update: DatasetItemUpdate, service: DatasetService = Depends(get_dataset_service)
) -> Dict[str, Any]:
    """更新数据集项目"""
    try:
        result = await service.update_dataset_item(dataset_id, item_update.id, item_update)
        if not result:
            return error_response(message="数据集或项目不存在")
        return success_response(data=result)
    except Exception as e:
        return error_response(message=str(e))
