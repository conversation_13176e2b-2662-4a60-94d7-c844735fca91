from typing import Any, Dict, List, Optional

from loguru import logger
from sqlalchemy.orm import Session
from t_ai_agent_ops.model.common import PaginatedResponse, PaginationParams
from t_ai_agent_ops.model.crud import DatasetCRUD, DatasetItemCRUD
from t_ai_agent_ops.model.dataset import DatasetCreate, DatasetItemCreate, DatasetItemUpdate, DatasetUpdate
from t_ai_agent_ops.model.db_config import get_db
from t_ai_agent_ops.utils.req_ctx_helper import get_current_team_id, get_current_user_id, get_current_user_name


class DatasetService:
    def __init__(self):
        pass

    async def create_dataset(self, dataset_create: DatasetCreate) -> Dict[str, Any]:
        """创建数据集"""
        db = next(get_db())
        try:
            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            # 创建数据集
            dataset = DatasetCRUD.create_dataset(
                db=db,
                name=dataset_create.name,
                agent_key=dataset_create.agent_key,
                description=dataset_create.description,
                meta_info=dataset_create.metadata,
                created_by=current_user_id or current_user_name or dataset_create.created_by,
                team_id=current_team_id or dataset_create.team_id,
            )

            # 如果提供了项目列表，则创建数据集项目
            if dataset_create.items:
                for item in dataset_create.items:
                    DatasetItemCRUD.create_item(
                        db=db,
                        dataset_id=dataset.id,
                        input_data=item.input,
                        expected_output=item.expected_output,
                        meta_info=item.metadata,
                        created_by=current_user_id or current_user_name or item.created_by,
                        team_id=current_team_id or item.team_id,
                    )

            return {
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "agentKey": dataset.agent_key,
                "metaInfo": dataset.meta_info,
                "createdAt": dataset.created_at.isoformat(),
                "updatedAt": dataset.updated_at.isoformat(),
                "createdBy": dataset.created_by,
                "updatedBy": dataset.updated_by,
                "teamId": dataset.team_id,
                "version": dataset.version,
            }
        finally:
            db.close()

    async def get_dataset(self, dataset_id: int) -> Optional[Dict[str, Any]]:
        """获取数据集详情"""
        db = next(get_db())
        try:
            dataset = DatasetCRUD.get_dataset(db, dataset_id)
            if not dataset:
                return None

            # 获取数据集的所有 items
            items = DatasetItemCRUD.get_items(db, dataset.id)

            return {
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "agentKey": dataset.agent_key,
                "metaInfo": dataset.meta_info,
                "createdAt": dataset.created_at.isoformat(),
                "updatedAt": dataset.updated_at.isoformat(),
                "createdBy": dataset.created_by,
                "updatedBy": dataset.updated_by,
                "teamId": dataset.team_id,
                "version": dataset.version,
                "items": [
                    {
                        "id": item.id,
                        "input": item.input_data,  # SQLAlchemy 会自动反序列化 JSON
                        "expectedOutput": item.expected_output,  # SQLAlchemy 会自动反序列化 JSON
                        "metaInfo": item.meta_info,  # SQLAlchemy 会自动反序列化 JSON
                        "createdAt": item.created_at.isoformat(),
                        "updatedAt": item.updated_at.isoformat(),
                        "createdBy": item.created_by,
                        "updatedBy": item.updated_by,
                        "teamId": item.team_id,
                        "version": item.version,
                    }
                    for item in items
                ],
            }
        finally:
            db.close()

    async def list_datasets(
        self, pagination: PaginationParams, agent_key: Optional[str] = None
    ) -> PaginatedResponse[Dict[str, Any]]:
        """获取数据集分页列表
        Args:
            pagination: 分页参数
            agent_key: 按 agent key 过滤
        """
        db = next(get_db())
        try:
            # 从 ReqCtx 获取当前团队ID
            current_team_id = get_current_team_id()

            # 获取总数
            total = DatasetCRUD.get_datasets_count(db, agent_key=agent_key, team_id=current_team_id)

            # 获取数据，支持排序
            datasets = DatasetCRUD.get_datasets(
                db,
                skip=pagination.skip,
                limit=pagination.limit,
                agent_key=agent_key,
                team_id=current_team_id,
                sort_by=pagination.sortBy,
                sort_order=pagination.sortOrder,
            )

            # 转换为字典格式
            data_items = [
                {
                    "id": dataset.id,
                    "name": dataset.name,
                    "description": dataset.description,
                    "agentKey": dataset.agent_key,
                    "metaInfo": dataset.meta_info,
                    "createdAt": dataset.created_at.isoformat(),
                    "updatedAt": dataset.updated_at.isoformat(),
                    "createdBy": dataset.created_by,
                    "updatedBy": dataset.updated_by,
                    "teamId": dataset.team_id,
                    "version": dataset.version,
                }
                for dataset in datasets
            ]

            return PaginatedResponse.create(data_items, total, pagination.pageNumber, pagination.pageSize)
        finally:
            db.close()

    async def update_dataset(self, dataset_id: int, dataset_update: DatasetUpdate) -> Optional[Dict[str, Any]]:
        """更新数据集"""
        db = next(get_db())
        try:
            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            updated_dataset = DatasetCRUD.update_dataset(
                db=db,
                dataset_id=dataset_id,
                name=dataset_update.name,
                description=dataset_update.description,
                agent_key=dataset_update.agent_key,
                meta_info=dataset_update.metadata,
                updated_by=current_user_id or current_user_name or dataset_update.updated_by,
                team_id=current_team_id or dataset_update.team_id,
            )

            if not updated_dataset:
                return None

            return {
                "id": updated_dataset.id,
                "name": updated_dataset.name,
                "description": updated_dataset.description,
                "agentKey": updated_dataset.agent_key,
                "metaInfo": updated_dataset.meta_info,
                "createdAt": updated_dataset.created_at.isoformat(),
                "updatedAt": updated_dataset.updated_at.isoformat(),
                "createdBy": updated_dataset.created_by,
                "updatedBy": updated_dataset.updated_by,
                "teamId": updated_dataset.team_id,
                "version": updated_dataset.version,
            }
        finally:
            db.close()

    async def delete_dataset(self, dataset_id: int) -> bool:
        """删除数据集"""
        db = next(get_db())
        try:
            return DatasetCRUD.delete_dataset(db, dataset_id)
        finally:
            db.close()

    async def create_dataset_item(self, dataset_id: int, item_create: DatasetItemCreate) -> Optional[Dict[str, Any]]:
        """创建数据集项目"""
        db = next(get_db())
        try:
            dataset = DatasetCRUD.get_dataset(db, dataset_id)
            if not dataset:
                return None

            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            item = DatasetItemCRUD.create_item(
                db=db,
                dataset_id=dataset.id,
                input_data=item_create.input,
                expected_output=item_create.expected_output,
                meta_info=item_create.metadata,
                created_by=current_user_id or current_user_name or item_create.created_by,
                team_id=current_team_id or item_create.team_id,
            )

            return {
                "id": item.id,
                "datasetId": item.dataset_id,
                "input": item.input_data,  # SQLAlchemy 会自动反序列化 JSON
                "expectedOutput": item.expected_output,  # SQLAlchemy 会自动反序列化 JSON
                "metaInfo": item.meta_info,  # SQLAlchemy 会自动反序列化 JSON
                "createdAt": item.created_at.isoformat(),
                "updatedAt": item.updated_at.isoformat(),
                "createdBy": item.created_by,
                "updatedBy": item.updated_by,
                "teamId": item.team_id,
                "version": item.version,
            }
        finally:
            db.close()

    async def get_dataset_items(self, dataset_id: int, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """获取数据集项目列表"""
        db = next(get_db())
        try:
            dataset = DatasetCRUD.get_dataset_by_id(db, dataset_id)
            if not dataset:
                return []

            items = DatasetItemCRUD.get_items(db, dataset.id, skip=skip, limit=limit)
            return [
                {
                    "id": item.id,
                    "datasetId": item.dataset_id,
                    "input": item.input_data,  # SQLAlchemy 会自动反序列化 JSON
                    "expectedOutput": item.expected_output,  # SQLAlchemy 会自动反序列化 JSON
                    "metaInfo": item.meta_info,  # SQLAlchemy 会自动反序列化 JSON
                    "createdAt": item.created_at.isoformat(),
                    "updatedAt": item.updated_at.isoformat(),
                    "createdBy": item.created_by,
                    "updatedBy": item.updated_by,
                    "teamId": item.team_id,
                    "version": item.version,
                }
                for item in items
            ]
        finally:
            db.close()

    async def update_dataset_item(
        self, dataset_id: int, item_id: int, item_update: DatasetItemUpdate
    ) -> Optional[Dict[str, Any]]:
        """更新数据集项目"""
        db = next(get_db())
        try:
            # 验证数据集是否存在
            dataset = DatasetCRUD.get_dataset(db, dataset_id)
            if not dataset:
                return None

            # 从 ReqCtx 获取当前用户和团队信息
            current_user_id = get_current_user_id()
            current_user_name = get_current_user_name()
            current_team_id = get_current_team_id()

            # 更新数据集项目
            item = DatasetItemCRUD.update_item(
                db=db,
                item_id=item_id,
                input_data=item_update.input,
                expected_output=item_update.expected_output,
                meta_info=item_update.metadata,
                updated_by=current_user_id or current_user_name or item_update.updated_by,
                team_id=current_team_id or item_update.team_id,
            )

            if not item:
                return None

            return {
                "id": item.id,
                "datasetId": item.dataset_id,
                "input": item.input_data,  # SQLAlchemy 会自动反序列化 JSON
                "expectedOutput": item.expected_output,  # SQLAlchemy 会自动反序列化 JSON
                "metaInfo": item.meta_info,  # SQLAlchemy 会自动反序列化 JSON
                "createdAt": item.created_at.isoformat(),
                "updatedAt": item.updated_at.isoformat(),
                "createdBy": item.created_by,
                "updatedBy": item.updated_by,
                "teamId": item.team_id,
                "version": item.version,
            }
        finally:
            db.close()
