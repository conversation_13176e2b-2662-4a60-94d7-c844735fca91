import uuid
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from fastapi import APIRouter, Body, Depends, Query, Request
from loguru import logger
from pydantic import BaseModel, ConfigDict, Field, ValidationInfo, field_validator, validator

from ..model.common import PaginationParams, error_response, success_response
from ..model.evaluation import (
    AgentConfig,
    EvaluationTask,
    EvaluationTaskCreate,
    EvaluationTaskUpdate,
)
from ..service.evaluation_service import EvaluationService, get_evaluation_service


def get_evaluation_service_with_referer() -> EvaluationService:
    return get_evaluation_service()


from ..utils.req_ctx_helper import get_current_team_id, get_current_user_id
from ..utils.serializer import serialize_db_model

router = APIRouter(prefix="/v1/evaluation")

# 单例服务实例
_evaluation_service = EvaluationService()


class CreateAndRunTaskRequest(BaseModel):
    """创建并运行评测任务的请求模型"""

    name: str = Field(..., description="任务名称")
    evaluation_type: str | None = Field(None, alias="evaluationType", description="评估类型")
    dataset_id: int = Field(..., alias="datasetId", description="测试数据集ID")
    agent_config: AgentConfig = Field(..., alias="agentConfig", description="Agent 配置")
    evaluation_rounds: int = Field(
        1, alias="evaluationRounds", ge=1, le=100, description="评估轮次，默认为1，最大100轮"
    )
    portal_key: str = Field(..., alias="portalKey", description="Portal Key")
    model_config = ConfigDict(
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "name": "评测任务示例",
                "evaluationType": "accuracy",
                "datasetId": 1,
                "evaluationRounds": 3,
                "agentConfig": {"key": "agent-key", "name": "Agent名称", "type": "Agent类型", "props": {}},
            }
        },
    )

    @property
    def agent_key(self) -> str:
        """从 agent_config 中获取 agent_key"""
        return self.agent_config.key


@router.post("/tasks", response_model=Dict[str, Any], tags=["评测任务"])
async def create_and_run_task(
    request: Request,
    task_request: CreateAndRunTaskRequest,
    service: EvaluationService = Depends(get_evaluation_service_with_referer),
) -> Dict[str, Any]:
    """创建评估任务并异步执行"""
    try:
        from datetime import datetime

        # 创建任务对象
        current_user_id = get_current_user_id()
        current_team_id = get_current_team_id()

        task_create = EvaluationTaskCreate(
            name=task_request.name,
            evaluation_type=task_request.evaluation_type,
            dataset_id=task_request.dataset_id,
            agent_key=task_request.agent_config.key,
            agent_config=task_request.agent_config,
            evaluation_rounds=task_request.evaluation_rounds,
            portal_key=task_request.portal_key,
            created_by=current_user_id,
            team_id=current_team_id,
        )

        # 创建任务并在后台异步执行
        task = await service.create_and_run_task(task_create)
        if task is None:
            return error_response(message="创建评测任务失败")

        return success_response(data=serialize_db_model(task), message="评测任务已创建并开始在后台运行")
    except Exception as e:
        import traceback

        traceback.print_exc()
        return error_response(message=f"创建评测任务失败: {str(e)}")


@router.get("/tasks/{task_id}", response_model=Dict[str, Any], tags=["评测任务"])
async def get_task(
    request: Request, task_id: int, service: EvaluationService = Depends(get_evaluation_service_with_referer)
) -> Dict[str, Any]:
    """获取评测任务详情"""
    try:
        task = await service.get_task(task_id)
        if task is None:
            return error_response(message="评测任务不存在")
        return success_response(data=serialize_db_model(task))
    except Exception as e:
        return error_response(message=str(e))


@router.get("/tasks", response_model=Dict[str, Any], tags=["评测任务"])
async def list_tasks(
    request: Request,
    agentKey: Optional[str] = Query(None, description="按 Agent Key 过滤"),
    pageNumber: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(20, ge=1, le=100, description="每页条数，默认20条，最大100条"),
    sortBy: str = Query("updated_at", description="排序字段，支持：updated_at, created_at, name, status"),
    sortOrder: str = Query("desc", description="排序方向，asc 或 desc"),
    service: EvaluationService = Depends(get_evaluation_service_with_referer),
) -> Dict[str, Any]:
    """获取评测任务分页列表，支持按更新时间倒序排序"""
    try:
        # 验证排序参数
        valid_sort_fields = ["updated_at", "created_at", "name", "status"]
        if sortBy not in valid_sort_fields:
            return error_response(message=f"无效的排序字段，支持：{', '.join(valid_sort_fields)}")

        if sortOrder.lower() not in ["asc", "desc"]:
            return error_response(message="排序方向必须是 asc 或 desc")

        pagination = PaginationParams(pageNumber=pageNumber, pageSize=pageSize, sortBy=sortBy, sortOrder=sortOrder)
        # 添加团队ID过滤
        pagination.team_id = get_current_team_id()
        result = await service.list_tasks(pagination=pagination, agent_key=agentKey)
        # 序列化数据项并隐藏 agent config
        serialized_result = result.dict()
        serialized_data = []
        for task in result.data:
            task_data = serialize_db_model(task)
            # 隐藏 metaInfo 中的 agentConfig
            if task_data.get("metaInfo") and isinstance(task_data["metaInfo"], dict):
                task_data["metaInfo"] = {k: v for k, v in task_data["metaInfo"].items() if k != "agentConfig"}
            serialized_data.append(task_data)
        serialized_result["data"] = serialized_data
        return success_response(data=serialized_result)
    except Exception as e:
        return error_response(message=str(e))


@router.put("/tasks/{task_id}", response_model=Dict[str, Any], tags=["评测任务"])
async def update_task(
    request: Request,
    task_id: str,
    task_update: EvaluationTaskUpdate,
    service: EvaluationService = Depends(get_evaluation_service_with_referer),
) -> Dict[str, Any]:
    """更新评测任务"""
    try:
        task = await service.update_task(task_id, task_update)
        if not task:
            return error_response(message="Task not found")
        return success_response(data=serialize_db_model(task))
    except ValueError as e:
        return error_response(message=str(e))
    except Exception as e:
        return error_response(message=f"Error updating task: {str(e)}")


@router.delete("/tasks/{task_id}", response_model=Dict[str, Any], tags=["评测任务"])
async def delete_task(
    request: Request, task_id: str, service: EvaluationService = Depends(get_evaluation_service_with_referer)
) -> Dict[str, Any]:
    """删除评测任务"""
    success = await service.delete_task(task_id)
    if not success:
        return error_response(message="Task not found")
    return success_response(message="Task deleted successfully")


@router.get("/report", response_model=Dict[str, Any], tags=["评测报告"])
async def get_evaluation_report(
    request: Request, task: EvaluationTask, service: EvaluationService = Depends(get_evaluation_service_with_referer)
) -> Dict[str, Any]:
    """获取评测报告"""
    try:
        report = await service.get_evaluation_report(task)
        return success_response(data=report)
    except ValueError as e:
        return error_response(message=str(e))
    except Exception as e:
        return error_response(message=f"获取评测报告失败: {str(e)}")


@router.get("/tasks/{task_id}/status", response_model=Dict[str, Any], tags=["评测任务"])
async def get_task_status(
    request: Request, task_id: int, service: EvaluationService = Depends(get_evaluation_service_with_referer)
) -> Dict[str, Any]:
    """获取评测任务状态"""
    try:
        task = await service.get_task(task_id)
        if task is None:
            return error_response(message="评测任务不存在")

        return success_response(
            data={
                "taskId": task_id,
                "status": task.status,
                "progress": task.progress,
                "error": task.error_message if task.status == "failed" else None,
                "createdAt": task.created_at.isoformat() if task.created_at else None,
                "updatedAt": task.updated_at.isoformat() if task.updated_at else None,
                "completedAt": task.completed_at.isoformat() if task.completed_at else None,
            }
        )
    except Exception as e:
        return error_response(message=str(e))
