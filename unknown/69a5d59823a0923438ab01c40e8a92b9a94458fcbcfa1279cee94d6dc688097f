from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

T = TypeVar("T")


class ApiResponse(Generic[T]):
    """通用 API 响应模型"""

    def __init__(self, data: Optional[T] = None, success: bool = True, message: str = ""):
        self.data = data
        self.success = success
        self.message = message

    def dict(self) -> dict:
        """转换为字典格式"""
        return {"data": self.data, "success": self.success, "message": self.message}


class PaginationParams(BaseModel):
    """分页参数模型"""

    pageNumber: int = Field(default=1, ge=1, description="页码，从1开始")
    pageSize: int = Field(default=20, ge=1, le=100, description="每页条数，默认20条，最大100条")
    sortBy: str = Field(default="updated_at", description="排序字段，默认为 updated_at")
    sortOrder: str = Field(default="desc", description="排序方向，asc 或 desc，默认为 desc")
    team_id: Optional[str] = Field(default=None, description="团队ID，用于过滤数据")

    @property
    def skip(self) -> int:
        """计算跳过的记录数"""
        return (self.pageNumber - 1) * self.pageSize

    @property
    def limit(self) -> int:
        """获取限制条数"""
        return self.pageSize


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""

    data: List[T] = Field(..., description="数据列表")
    total: int = Field(..., description="总条数")
    pageNumber: int = Field(..., description="当前页码")
    pageSize: int = Field(..., description="每页条数")
    totalPages: int = Field(..., description="总页数")
    hasNext: bool = Field(..., description="是否有下一页")
    hasPrev: bool = Field(..., description="是否有上一页")

    @classmethod
    def create(cls, data: List[T], total: int, pageNumber: int, pageSize: int):
        """创建分页响应"""
        totalPages = (total + pageSize - 1) // pageSize  # 向上取整
        return cls(
            data=data,
            total=total,
            pageNumber=pageNumber,
            pageSize=pageSize,
            totalPages=totalPages,
            hasNext=pageNumber < totalPages,
            hasPrev=pageNumber > 1,
        )


def success_response(data: Any = None, message: str = "success") -> Dict[str, Any]:
    """成功响应"""
    return {"success": True, "message": message, "data": data}


def error_response(message: str = "error", code: int = 500) -> Dict[str, Any]:
    """错误响应"""
    return {"success": False, "message": message, "code": code}
