from datetime import datetime, timedelta, timezone

# 定义东八区时区
CHINA_TZ = timezone(timedelta(hours=8))


def china_now() -> datetime:
    """获取当前东八区时间"""
    return datetime.now(CHINA_TZ)


def utc_to_china(utc_dt: datetime) -> datetime:
    """将UTC时间转换为东八区时间"""
    if utc_dt.tzinfo is None:
        # 如果没有时区信息，假设是UTC时间
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)
    return utc_dt.astimezone(CHINA_TZ)


def china_to_utc(china_dt: datetime) -> datetime:
    """将东八区时间转换为UTC时间"""
    if china_dt.tzinfo is None:
        # 如果没有时区信息，假设是东八区时间
        china_dt = china_dt.replace(tzinfo=CHINA_TZ)
    return china_dt.astimezone(timezone.utc)
