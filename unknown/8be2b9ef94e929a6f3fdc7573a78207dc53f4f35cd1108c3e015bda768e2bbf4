import json
from datetime import datetime

from sqlalchemy import JSO<PERSON>, Column, DateTime, Float, ForeignKey, Integer, String, Text, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

from ..utils.timezone_utils import china_now

Base = declarative_base()


class Dataset(Base):
    """数据集表"""

    __tablename__ = "datasets"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False, comment="数据集名称")
    description = Column(Text, nullable=True, comment="数据集描述")
    agent_key = Column(String(255), nullable=False, comment="关联的 agent key")
    meta_info = Column(Text, nullable=True, comment="数据集元数据")
    created_at = Column(DateTime, default=china_now, comment="创建时间")
    updated_at = Column(DateTime, default=china_now, onupdate=china_now, comment="更新时间")
    created_by = Column(String(255), nullable=True, comment="创建者")
    updated_by = Column(String(255), nullable=True, comment="更新者")
    team_id = Column(String(255), nullable=True, comment="团队ID")
    version = Column(Integer, default=1, comment="版本号")

    # 建立与数据集项目的关系
    items = relationship("DatasetItem", back_populates="dataset", cascade="all, delete-orphan")
    # 建立与评估任务的关系
    evaluation_tasks = relationship("EvaluationTask", back_populates="dataset")


class DatasetItem(Base):
    """数据集项目表"""

    __tablename__ = "dataset_items"

    id = Column(Integer, primary_key=True, autoincrement=True)
    dataset_id = Column(Integer, ForeignKey("datasets.id"), nullable=False, comment="所属数据集ID")
    input_data = Column(Text, nullable=False, comment="输入数据")
    expected_output = Column(Text, nullable=True, comment="期望输出")
    meta_info = Column(Text, nullable=True, comment="元数据")
    created_at = Column(DateTime, default=china_now, comment="创建时间")
    updated_at = Column(DateTime, default=china_now, onupdate=china_now, comment="更新时间")
    created_by = Column(String(255), nullable=True, comment="创建者")
    updated_by = Column(String(255), nullable=True, comment="更新者")
    team_id = Column(String(255), nullable=True, comment="团队ID")
    version = Column(Integer, default=1, comment="版本号")

    # 建立与数据集的关系
    dataset = relationship("Dataset", back_populates="items")


class EvaluationTask(Base):
    """评估任务表"""

    __tablename__ = "evaluation_tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    dataset_id = Column(Integer, ForeignKey("datasets.id"), nullable=False, comment="关联数据集ID")
    name = Column(String(255), nullable=False, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    agent_key = Column(String(255), nullable=True, comment="关联的 agent key")
    portal_key = Column(String(255), nullable=True, comment="Portal Key")
    status = Column(String(50), nullable=False, default="pending", comment="任务状态")
    progress = Column(Float, nullable=False, default=0.0, comment="任务进度，0.0-1.0之间的浮点数")
    meta_info = Column(Text, nullable=True, comment="任务元数据")
    result = Column(JSON, nullable=True, comment="评估结果")
    error_message = Column(Text, nullable=True, comment="错误信息")
    created_at = Column(DateTime, default=china_now, comment="创建时间")
    updated_at = Column(DateTime, default=china_now, onupdate=china_now, comment="更新时间")
    created_by = Column(String(255), nullable=True, comment="创建者")
    updated_by = Column(String(255), nullable=True, comment="更新者")
    team_id = Column(String(255), nullable=True, comment="团队ID")
    version = Column(Integer, default=1, comment="版本号")

    # 建立与数据集的关系
    dataset = relationship("Dataset", back_populates="evaluation_tasks")
