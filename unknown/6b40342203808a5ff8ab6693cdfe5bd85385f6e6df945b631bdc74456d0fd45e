"""add agent_key to evaluation_tasks

Revision ID: 20250626_1103
Revises: 458c42fe18a5
Create Date: 2024-01-01 00:00:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250626_1103"
down_revision: Union[str, None] = "458c42fe18a5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "evaluation_tasks", sa.Column("agent_key", sa.String(length=255), nullable=True, comment="关联的 agent key")
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("evaluation_tasks", "agent_key")
    # ### end Alembic commands ###
