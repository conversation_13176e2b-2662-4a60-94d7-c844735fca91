from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from ..utils.timezone_utils import china_now


class AgentConfig(BaseModel):
    """Agent 配置模型"""

    type: str = Field(default="Agent", description="Agent 类型")
    key: str = Field(..., description="Agent Key")
    name: str = Field(..., description="Agent 名称")
    props: Optional[Dict[str, Any]] = Field(None, description="Agent 属性配置")
    handoffs: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="移交配置")
    children: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="子 Agent")


class EvaluationTask(BaseModel):
    """评测任务模型"""

    id: str = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    evaluation_type: str | None = Field(None, alias="evaluationType", description="评估类型")
    cost: float = Field(..., description="费用消耗")
    dataset_id: int = Field(..., alias="datasetId", description="测试数据集ID")
    agent_key: Optional[str] = Field(None, alias="agentKey", description="Agent Key")
    agent_config: Optional[AgentConfig] = Field(None, alias="agentConfig", description="Agent 配置")
    langfuse_trace_id: Optional[str] = Field(None, alias="langfuseTraceId", description="Langfuse追踪ID")
    langfuse_metadata: Optional[Dict[str, Any]] = Field(
        None, alias="langfuseMetadata", description="Langfuse集成元数据"
    )
    evaluation_results: Optional[List[Dict[str, Any]]] = Field(None, alias="evaluationResults", description="评测结果")
    metrics: Optional[Dict[str, float]] = Field(None, description="评测指标")
    created_at: datetime = Field(default_factory=china_now, alias="createdAt", description="创建时间")
    updated_at: datetime = Field(default_factory=china_now, alias="updatedAt", description="更新时间")
    completed_at: Optional[datetime] = Field(None, alias="completedAt", description="完成时间")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)


class EvaluationTaskCreate(BaseModel):
    """创建评测任务请求模型"""

    name: str = Field(..., description="任务名称")
    evaluation_type: str | None = Field(None, alias="evaluationType", description="评估类型")
    description: Optional[str] = Field(None, description="任务描述")
    dataset_id: int = Field(..., alias="datasetId", description="测试数据集ID")
    agent_key: Optional[str] = Field(None, alias="agentKey", description="Agent Key")
    agent_config: Optional[AgentConfig] = Field(None, alias="agentConfig", description="Agent 配置")
    evaluation_rounds: int = Field(
        1, alias="evaluationRounds", ge=1, le=100, description="评估轮次，默认为1，最大100轮"
    )
    referer: Optional[str] = Field(None, alias="referer", description="Referer")
    portal_key: Optional[str] = Field(None, alias="portalKey", description="Portal Key")
    created_by: Optional[str] = Field(None, alias="createdBy", description="创建者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")
    model_config = ConfigDict(populate_by_name=True)


class EvaluationTaskUpdate(BaseModel):
    """更新评测任务请求模型"""

    name: Optional[str] = Field(None, description="任务名称")
    evaluation_type: Optional[str] = Field(None, alias="evaluationType", description="评估类型")
    execution_time: Optional[datetime] = Field(None, alias="executionTime", description="执行时间")
    dataset_id: Optional[int] = Field(None, alias="datasetId", description="测试数据集ID")
    agent_key: Optional[str] = Field(None, alias="agentKey", description="Agent Key")
    agent_config: Optional[AgentConfig] = Field(None, alias="agentConfig", description="Agent 配置")
    langfuse_trace_id: Optional[str] = Field(None, alias="langfuseTraceId", description="Langfuse追踪ID")
    langfuse_metadata: Optional[Dict[str, Any]] = Field(
        None, alias="langfuseMetadata", description="Langfuse集成元数据"
    )
    evaluation_results: Optional[List[Dict[str, Any]]] = Field(None, alias="evaluationResults", description="评测结果")
    metrics: Optional[Dict[str, float]] = Field(None, description="评测指标")
    updated_by: Optional[str] = Field(None, alias="updatedBy", description="更新者")
    team_id: Optional[str] = Field(None, alias="teamId", description="团队ID")

    model_config = ConfigDict(populate_by_name=True)
