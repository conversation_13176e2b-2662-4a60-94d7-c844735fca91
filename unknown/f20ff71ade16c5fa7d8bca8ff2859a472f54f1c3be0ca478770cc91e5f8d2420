"""
对话历史处理辅助函数
"""

import json
from typing import Any, Dict, List, Optional

from loguru import logger

from ..model.agent_context import AgentExecutionContext


class ConversationHelper:
    """对话历史处理辅助类"""

    @staticmethod
    def get_conversation_history(context: AgentExecutionContext) -> List[Dict[str, Any]]:
        """
        获取input_items对话历史（用户输入和部分助手回复）

        Args:
            context: Agent执行上下文

        Returns:
            List[Dict]: 对话历史列表
        """
        return context.get_conversation_history()

    @staticmethod
    def get_full_conversation_history(context: AgentExecutionContext) -> List[Dict[str, Any]]:
        """
        获取完整的对话历史，包括当前会话中的所有消息

        Args:
            context: Agent执行上下文

        Returns:
            List[Dict]: 完整的对话历史列表
        """
        return context.get_full_conversation_history()

    @staticmethod
    def get_input_items_history(context: AgentExecutionContext) -> List[Dict[str, Any]]:
        """
        明确获取input_items历史（用户输入和部分助手回复）

        Args:
            context: Agent执行上下文

        Returns:
            List[Dict]: input_items历史列表
        """
        return context.get_input_items_history()

    @staticmethod
    def get_user_messages(context: AgentExecutionContext, use_full_history: bool = False) -> List[str]:
        """
        获取所有用户消息

        Args:
            context: Agent执行上下文
            use_full_history: 是否使用完整对话历史，默认False（使用input_items）

        Returns:
            List[str]: 用户消息文本列表
        """
        user_messages = []

        if use_full_history:
            conversation_history = context.get_full_conversation_history()
        else:
            conversation_history = context.get_conversation_history()

        for message in conversation_history:
            if message.get("role") == "user":
                content = message.get("content", "")
                try:
                    # 解析content JSON
                    content_list = json.loads(content) if isinstance(content, str) else content
                    if isinstance(content_list, list):
                        for content_item in content_list:
                            if content_item.get("type") == "text":
                                text = content_item.get("text", "")
                                if text:
                                    user_messages.append(text)
                    elif isinstance(content_list, dict) and content_list.get("type") == "text":
                        text = content_list.get("text", "")
                        if text:
                            user_messages.append(text)
                except (json.JSONDecodeError, TypeError):
                    # 如果不是JSON格式，直接作为文本处理
                    if isinstance(content, str) and content:
                        user_messages.append(content)

        return user_messages

    @staticmethod
    def get_assistant_messages(context: AgentExecutionContext, use_full_history: bool = False) -> List[str]:
        """
        获取所有助手消息

        Args:
            context: Agent执行上下文
            use_full_history: 是否使用完整对话历史，默认False（使用input_items）

        Returns:
            List[str]: 助手消息文本列表
        """
        assistant_messages = []

        if use_full_history:
            conversation_history = context.get_full_conversation_history()
        else:
            conversation_history = context.get_conversation_history()

        for message in conversation_history:
            if message.get("role") == "assistant":
                content = message.get("content", "")
                try:
                    # 解析content JSON
                    content_json = json.loads(content) if isinstance(content, str) else content
                    if isinstance(content_json, dict) and content_json.get("type") == "text":
                        text = content_json.get("text", "")
                        if text:
                            assistant_messages.append(text)
                except (json.JSONDecodeError, TypeError):
                    # 如果不是JSON格式，直接作为文本处理
                    if isinstance(content, str) and content:
                        assistant_messages.append(content)

        return assistant_messages

    @staticmethod
    def get_last_user_message(context: AgentExecutionContext, use_full_history: bool = False) -> Optional[str]:
        """
        获取最后一条用户消息

        Args:
            context: Agent执行上下文
            use_full_history: 是否使用完整对话历史，默认False（使用input_items）

        Returns:
            Optional[str]: 最后一条用户消息，如果没有则返回None
        """
        user_messages = ConversationHelper.get_user_messages(context, use_full_history)
        return user_messages[-1] if user_messages else None

    @staticmethod
    def get_conversation_summary(
        context: AgentExecutionContext, max_messages: int = 10, use_full_history: bool = False
    ) -> str:
        """
        获取对话摘要

        Args:
            context: Agent执行上下文
            max_messages: 最大消息数量
            use_full_history: 是否使用完整对话历史，默认False（使用input_items）

        Returns:
            str: 对话摘要
        """
        if use_full_history:
            conversation_history = context.get_full_conversation_history()
        else:
            conversation_history = context.get_conversation_history()

        # 获取最近的消息
        recent_messages = (
            conversation_history[-max_messages:] if len(conversation_history) > max_messages else conversation_history
        )

        summary_parts = []
        for i, message in enumerate(recent_messages):
            role = message.get("role", "unknown")
            content = message.get("content", "")

            # 简化内容提取
            text_content = ""
            try:
                if isinstance(content, str):
                    content_data = json.loads(content)
                else:
                    content_data = content

                if isinstance(content_data, list):
                    for item in content_data:
                        if item.get("type") == "text":
                            text_content = item.get("text", "")
                            break
                elif isinstance(content_data, dict):
                    if content_data.get("type") == "text":
                        text_content = content_data.get("text", "")
                    else:
                        text_content = str(content_data)
            except (json.JSONDecodeError, TypeError):
                text_content = str(content)

            if text_content:
                summary_parts.append(f"{role}: {text_content[:100]}{'...' if len(text_content) > 100 else ''}")

        return "\n".join(summary_parts)

    @staticmethod
    def count_messages_by_role(context: AgentExecutionContext, use_full_history: bool = False) -> Dict[str, int]:
        """
        统计各角色的消息数量

        Args:
            context: Agent执行上下文
            use_full_history: 是否使用完整对话历史，默认False（使用input_items）

        Returns:
            Dict[str, int]: 各角色的消息数量
        """
        if use_full_history:
            conversation_history = context.get_full_conversation_history()
        else:
            conversation_history = context.get_conversation_history()

        role_counts = {}

        for message in conversation_history:
            role = message.get("role", "unknown")
            role_counts[role] = role_counts.get(role, 0) + 1

        return role_counts


def get_conversation_history_tool_function():
    """
    创建一个获取对话历史的工具函数示例
    """
    from agents import RunContextWrapper, function_tool

    @function_tool(name="get_conversation_history", description="获取当前对话的历史记录，包括input_items和完整对话历史")
    async def get_conversation_history(context: RunContextWrapper, include_full_history: bool = False) -> str:
        """
        获取对话历史

        Args:
            include_full_history: 是否包含完整对话历史，默认False（只返回input_items历史）
        """
        agent_exec_ctx: AgentExecutionContext = context.context

        try:
            # 获取input_items对话历史
            input_history = ConversationHelper.get_input_items_history(agent_exec_ctx)

            # 获取input_items的统计信息
            input_role_counts = ConversationHelper.count_messages_by_role(agent_exec_ctx, use_full_history=False)
            input_user_messages = ConversationHelper.get_user_messages(agent_exec_ctx, use_full_history=False)

            result = {
                "input_items_history": {
                    "total_messages": len(input_history),
                    "role_counts": input_role_counts,
                    "user_messages": input_user_messages,
                    "last_user_message": ConversationHelper.get_last_user_message(
                        agent_exec_ctx, use_full_history=False
                    ),
                    "summary": ConversationHelper.get_conversation_summary(agent_exec_ctx, use_full_history=False),
                }
            }

            # 如果需要，获取完整对话历史
            if include_full_history:
                full_history = ConversationHelper.get_full_conversation_history(agent_exec_ctx)
                full_role_counts = ConversationHelper.count_messages_by_role(agent_exec_ctx, use_full_history=True)
                full_user_messages = ConversationHelper.get_user_messages(agent_exec_ctx, use_full_history=True)
                full_assistant_messages = ConversationHelper.get_assistant_messages(
                    agent_exec_ctx, use_full_history=True
                )

                result["full_conversation_history"] = {
                    "total_messages": len(full_history),
                    "role_counts": full_role_counts,
                    "user_messages": full_user_messages,
                    "assistant_messages": full_assistant_messages,
                    "last_user_message": ConversationHelper.get_last_user_message(
                        agent_exec_ctx, use_full_history=True
                    ),
                    "summary": ConversationHelper.get_conversation_summary(agent_exec_ctx, use_full_history=True),
                }

                # 比较两种历史的差异
                result["comparison"] = {
                    "input_items_length": len(input_history),
                    "full_history_length": len(full_history),
                    "additional_messages_in_full": len(full_history) - len(input_history),
                }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.exception(f"Error getting conversation history: {e}")
            return f"Error getting conversation history: {str(e)}"

    return get_conversation_history
