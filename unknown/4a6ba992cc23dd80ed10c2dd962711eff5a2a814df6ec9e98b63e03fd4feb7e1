from datetime import datetime
from typing import Any, Dict


def serialize_datetime(dt: datetime) -> str:
    """序列化日期时间为 ISO 格式字符串"""
    return dt.isoformat() if dt else None


def snake_to_camel(snake_str: str) -> str:
    """将snake_case转换为camelCase"""
    components = snake_str.split("_")
    return components[0] + "".join(word.capitalize() for word in components[1:])


def serialize_db_model(model: Any) -> Dict[str, Any]:
    """将数据库模型序列化为字典，并将字段名转换为camelCase"""
    import json

    result = {}
    for key, value in model.__dict__.items():
        if not key.startswith("_"):  # 跳过 SQLAlchemy 内部属性
            # 转换字段名为camelCase
            camel_key = snake_to_camel(key)
            if isinstance(value, datetime):
                result[camel_key] = serialize_datetime(value)
            elif key == "meta_info" and value:
                # 尝试解析 meta_info JSON 字符串
                try:
                    result[camel_key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    result[camel_key] = value
            else:
                result[camel_key] = value
    return result
