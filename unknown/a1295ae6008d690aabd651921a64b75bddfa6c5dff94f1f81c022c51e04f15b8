import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from loguru import logger


class LangfuseClient:
    def __init__(self):
        # 优先使用 LANGFUSE_HOST，然后是 LANGFUSE_API_URL，最后是默认值
        self.base_url = os.getenv("LANGFUSE_HOST") or os.getenv("LANGFUSE_API_URL", "http://localhost:3000")
        self.public_key = os.getenv("LANGFUSE_PUBLIC_KEY", "pk-lf-1234")
        self.secret_key = os.getenv("LANGFUSE_SECRET_KEY", "sk-lf-1234")

        logger.info(f"Initializing LangfuseClient with base_url: {self.base_url}")

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        json: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        api_version: str = "public",
    ) -> Dict[str, Any]:
        url = f"{self.base_url}/api/{api_version}{endpoint}"
        auth = (self.public_key, self.secret_key)

        logger.debug(f"Making {method} request to {url}")

        async with httpx.AsyncClient() as client:
            response = await client.request(
                method=method,
                url=url,
                json=json,
                params=params,
                auth=auth,
                headers={"Content-Type": "application/json"},
            )

            try:
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Langfuse API error: {str(e)}")
                logger.error(f"Response status: {response.status_code}")
                logger.error(f"Response text: {response.text}")
                raise

    # 数据集管理
    async def create_dataset(
        self, name: str, description: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建数据集"""
        request_data = {"name": name}
        if description is not None:
            request_data["description"] = description
        if metadata is not None:
            request_data["metadata"] = metadata

        logger.debug(f"Creating Langfuse dataset: {name}")

        return await self._make_request("POST", "/datasets", json=request_data, api_version="public")

    async def get_dataset(self, dataset_name: str) -> Dict[str, Any]:
        """获取数据集详情"""
        logger.debug(f"Getting dataset by name: {dataset_name}")
        return await self._make_request("GET", f"/datasets/{dataset_name}", api_version="public")

    async def get_dataset_by_name(self, dataset_name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取数据集"""
        try:
            datasets = await self.list_datasets()
            for dataset in datasets:
                if dataset.get("name") == dataset_name:
                    return dataset
            return None
        except Exception as e:
            logger.error(f"Failed to get dataset by name {dataset_name}: {str(e)}")
            return None

    async def list_datasets(self) -> List[Dict[str, Any]]:
        """获取数据集列表"""
        response = await self._make_request("GET", "/datasets", api_version="public")
        return response.get("data", [])

    async def update_dataset(
        self,
        dataset_name: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """更新数据集"""
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if description is not None:
            update_data["description"] = description
        if metadata is not None:
            update_data["metadata"] = metadata

        return await self._make_request("PATCH", f"/datasets/{dataset_name}", json=update_data, api_version="public")

    async def delete_dataset(self, dataset_name: str) -> None:
        """删除数据集"""
        await self._make_request("DELETE", f"/datasets/{dataset_name}", api_version="public")

    async def get_dataset_items(self, dataset_name: str, page: int = 1, limit: int = 100) -> List[Dict[str, Any]]:
        """获取数据集项目列表
        Args:
            dataset_name: 数据集名称
            page: 页码，从1开始
            limit: 每页项目数量
        """
        params = {"datasetName": dataset_name, "page": page, "limit": limit}

        logger.debug(f"Getting dataset items with params: {params}")
        response = await self._make_request("GET", "/dataset-items", params=params, api_version="public")

        items = response.get("data", [])
        logger.debug(f"Got {len(items)} dataset items")
        return items

    async def create_dataset_item(
        self,
        dataset_name: str,
        input: Dict[str, Any],
        expected_output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        source_trace_id: Optional[str] = None,
        source_observation_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """创建数据集项目
        Args:
            dataset_name: 数据集名称
            input: 输入数据
            expected_output: 期望输出
            metadata: 元数据
            source_trace_id: 来源追踪ID
            source_observation_id: 来源观察ID
        """
        request_data = {"datasetName": dataset_name, "input": input, "status": "ACTIVE"}

        if expected_output is not None:
            request_data["expectedOutput"] = expected_output
        if metadata is not None:
            request_data["metadata"] = metadata
        if source_trace_id is not None:
            request_data["sourceTraceId"] = source_trace_id
        if source_observation_id is not None:
            request_data["sourceObservationId"] = source_observation_id

        logger.debug(f"Creating dataset item: {request_data}")
        return await self._make_request("POST", "/dataset-items", json=request_data, api_version="public")

    async def sync_dataset_to_langfuse(
        self, local_dataset: Dict[str, Any], force_update: bool = False
    ) -> Dict[str, Any]:
        """同步本地数据集到 Langfuse"""
        dataset_name = local_dataset["name"]

        try:
            # 检查数据集是否已存在
            existing_dataset = await self.get_dataset_by_name(dataset_name)

            if existing_dataset and not force_update:
                logger.info(f"Dataset '{dataset_name}' already exists in Langfuse, skipping sync")
                return existing_dataset

            # 创建或更新数据集
            if not existing_dataset:
                logger.info(f"Creating new dataset in Langfuse: {dataset_name}")
                langfuse_dataset = await self.create_dataset(
                    name=dataset_name,
                    description=local_dataset.get("description"),
                    metadata={
                        **local_dataset.get("metadata", {}),
                        "source": "t-ai-agent-ops",
                        "sync_time": datetime.now().isoformat(),
                    },
                )
            else:
                logger.info(f"Updating existing dataset in Langfuse: {dataset_name}")
                langfuse_dataset = await self.update_dataset(
                    dataset_name=dataset_name,
                    description=local_dataset.get("description"),
                    metadata={
                        **local_dataset.get("metadata", {}),
                        "source": "t-ai-agent-ops",
                        "sync_time": datetime.now().isoformat(),
                    },
                )

            # 同步数据集项目
            local_items = local_dataset.get("items", [])
            logger.info(f"Syncing {len(local_items)} dataset items to Langfuse")

            for item in local_items:
                await self.create_dataset_item(
                    dataset_name=dataset_name,
                    input=item.get("input", {}),
                    expected_output=item.get("expected_output") or item.get("expectedOutput"),
                    metadata=item.get("metadata", {}),
                    source_trace_id=item.get("trace_id"),
                    source_observation_id=item.get("observation_id"),
                )

            logger.info(f"Successfully synced dataset '{dataset_name}' to Langfuse")
            return langfuse_dataset

        except Exception as e:
            logger.error(f"Failed to sync dataset to Langfuse: {str(e)}")
            raise

    # 评测相关方法
    async def create_trace(
        self,
        name: str,
        input: Optional[Dict[str, Any]] = None,
        output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """创建追踪记录"""
        trace_id = str(uuid.uuid4())
        request_data = {"id": trace_id, "name": name, "timestamp": datetime.now().astimezone().isoformat()}

        if input is not None:
            request_data["input"] = input
        if output is not None:
            request_data["output"] = output
        if metadata is not None:
            request_data["metadata"] = metadata
        if tags is not None:
            request_data["tags"] = tags
        if user_id is not None:
            request_data["userId"] = user_id
        if session_id is not None:
            request_data["sessionId"] = session_id

        result = await self._make_request("POST", "/traces", json=request_data)
        return {"id": trace_id, **result}

    async def create_span(
        self,
        trace_id: str,
        name: str,
        input: Optional[Dict[str, Any]] = None,
        output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """创建跨度记录"""
        span_id = str(uuid.uuid4())
        request_data = {
            "id": span_id,
            "traceId": trace_id,
            "name": name,
            "startTime": (start_time or datetime.now()).astimezone().isoformat().replace("+00:00", "Z"),
        }

        if input is not None:
            request_data["input"] = input
        if output is not None:
            request_data["output"] = output
        if metadata is not None:
            request_data["metadata"] = metadata
        if end_time is not None:
            request_data["endTime"] = end_time.astimezone().isoformat().replace("+00:00", "Z")

        result = await self._make_request("POST", "/spans", json=request_data)
        return {"id": span_id, **result}

    async def create_score(
        self, trace_id: str, name: str, value: float, comment: Optional[str] = None, data_type: str = "NUMERIC"
    ) -> Dict[str, Any]:
        """创建评分记录"""
        request_data = {"traceId": trace_id, "name": name, "value": value, "dataType": data_type}

        if comment is not None:
            request_data["comment"] = comment

        return await self._make_request("POST", "/scores", json=request_data)

    async def create_evaluation_run(
        self, name: str, dataset_name: str, metadata: Optional[Dict[str, Any]] = None, description: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建数据集评测运行"""
        request_data = {
            "name": name,
            "datasetName": dataset_name,
        }
        if description is not None:
            request_data["description"] = description
        if metadata is not None:
            request_data["metadata"] = metadata

        logger.info(f"Creating dataset run: {name} for dataset: {dataset_name}")
        return await self._make_request("POST", "/dataset-runs", json=request_data)

    async def create_dataset_run_item(
        self, run_id: str, dataset_item_id: str, trace_id: str, metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """创建数据集运行项目"""
        request_data = {
            "datasetRunId": run_id,
            "datasetItemId": dataset_item_id,
            "traceId": trace_id,
        }
        if metadata is not None:
            request_data["metadata"] = metadata

        return await self._make_request("POST", "/dataset-run-items", json=request_data)

    async def run_integrated_evaluation(
        self,
        evaluation_task_id: str,
        dataset_name: str,
        agent_config: Dict[str, Any],
        evaluation_results: List[Dict[str, Any]],
        metrics: Dict[str, float],
    ) -> Dict[str, Any]:
        """运行完整的集成评测"""
        try:
            # 1. 创建评测运行
            run = await self.create_evaluation_run(
                name=f"评测运行 {evaluation_task_id}",
                dataset_name=dataset_name,
                metadata={
                    "evaluation_task_id": evaluation_task_id,
                    "agent_config": agent_config,
                    "metrics": metrics,
                    "timestamp": datetime.now().isoformat(),
                },
                description=f"Agent评测运行 - {agent_config.get('name', 'Unknown Agent')}",
            )
            run_id = run.get("id")

            # 2. 为每个评测结果创建运行项目
            for result in evaluation_results:
                dataset_item_id = result.get("dataset_item_id")
                trace_id = result.get("trace_id")

                if dataset_item_id and trace_id:
                    await self.create_dataset_run_item(
                        run_id=run_id,
                        dataset_item_id=dataset_item_id,
                        trace_id=trace_id,
                        metadata={
                            "score": result.get("score"),
                            "latency": result.get("latency"),
                            "error": result.get("error"),
                            "timestamp": datetime.now().isoformat(),
                        },
                    )

            return run

        except Exception as e:
            logger.error(f"Failed to run integrated evaluation: {str(e)}")
            raise

    async def run_evaluation(self, dataset_name: str, trace_id: str, evaluation_type: str = "custom") -> Dict[str, Any]:
        """运行数据集评测（简单版本）"""
        request_data = {"datasetName": dataset_name, "traceId": trace_id, "evaluationType": evaluation_type}

        return await self._make_request("POST", "/evaluations", json=request_data)
