from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger
from t_ai_agent.ai_proxy_client_factory import ai_proxy_client_factory

from ..utils.timezone_utils import china_now


class ScoringService:
    def __init__(self):
        pass

    async def evaluate_response(
        self,
        actual_output: str,
        expected_output: Any,
        input_question: str,
        evaluation_criteria: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """使用 LLM 评估响应质量

        Args:
            actual_output: 实际输出的响应
            expected_output: 期望的输出
            input_question: 输入的问题
            evaluation_criteria: 评估标准列表，如果为 None，则使用默认标准

        Returns:
            包含评分和详细评估的字典
        """
        # 默认评估标准
        default_criteria = [
            "准确性：回答是否准确地解决了问题，是否与参考答案一致",
            "完整性：是否完整地回答了问题的所有方面",
            "相关性：回答是否与问题直接相关，没有偏离主题",
            "清晰度：表达是否清晰，容易理解",
            "专业性：是否展现了专业知识和深度",
        ]

        criteria = evaluation_criteria or default_criteria

        # 构建评估提示
        prompt = self._build_evaluation_prompt(
            actual_output=actual_output,
            expected_output=expected_output,
            input_question=input_question,
            criteria=criteria,
        )

        try:
            # 获取 AI 客户端
            client = await ai_proxy_client_factory.get_client()
            # 调用 LLM 进行评估
            response = await client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的 AI 评估专家，负责评估 AI 回答的质量。请严格按照指定的格式输出评估结果。",
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.3,  # 保持评估的一致性
                max_tokens=1000,
                response_format={"type": "json_object"},
            )

            logger.debug(f"################ evaluate_response: {response.choices[0].message.content}")
            # 解析评估结果
            evaluation = self._parse_evaluation_result(response.choices[0].message.content)
            logger.info(f"################ evaluation: {evaluation}")

            return {
                "scores": evaluation["scores"],
                "scoreDetails": evaluation["scoreDetails"],
                "overallScore": evaluation["overallScore"],
                "feedback": evaluation["feedback"],
                "timestamp": china_now().isoformat(),
            }

        except Exception as e:
            logger.error(f"评估失败: {str(e)}")
            return {
                "scores": {},
                "scoreDetails": {},
                "overallScore": 0.0,
                "feedback": f"评估过程出错: {str(e)}",
                "timestamp": china_now().isoformat(),
            }

    def _build_evaluation_prompt(
        self, actual_output: str, expected_output: Any, input_question: str, criteria: List[str]
    ) -> str:
        """构建评估提示"""
        prompt = f"""作为一个专业的 AI 评估专家，请对以下回答进行评估：

问题：
{input_question}

实际回答：
{actual_output}

参考答案：
{expected_output}

请根据以下标准进行评估：
{chr(10).join(f"- {criterion}" for criterion in criteria)}

请以 JSON 格式输出评估结果，格式如下：

{{
    "scores": {{
        "标准1": {{
            "score": 8.5,  // 0-10分
            "reason": "详细的评分理由"
        }},
        "标准2": {{
            "score": 7.0,
            "reason": "详细的评分理由"
        }}
        // ... 其他标准
    }},
    "overall_score": 7.8,  // 0-10分的总体评分
    "feedback": "详细的评估反馈和改进建议"
}}

注意：
1. 评分要客观公正，基于事实和专业判断
2. 提供具体的例子支持你的评分
3. 给出建设性的改进建议
4. 严格按照 JSON 格式输出，确保格式正确
"""
        return prompt

    def _parse_evaluation_result(self, evaluation_text: str) -> Dict[str, Any]:
        """解析评估结果文本"""
        try:
            # 尝试直接解析 JSON
            import json
            import re

            # 清理可能的注释
            cleaned_text = re.sub(r"//.*?\n", "\n", evaluation_text)
            # 提取 JSON 部分
            json_match = re.search(r"\{[\s\S]*\}", cleaned_text)
            if json_match:
                result = json.loads(json_match.group())

                # 验证结果格式
                if not isinstance(result, dict):
                    raise ValueError("评估结果必须是一个字典")

                if "scores" not in result or "overall_score" not in result or "feedback" not in result:
                    raise ValueError("评估结果缺少必要字段")

                # 验证分数范围
                if not 0 <= result["overall_score"] <= 10:
                    raise ValueError("总体评分必须在 0-10 范围内")

                for criterion, score_data in result["scores"].items():
                    if not isinstance(score_data, dict):
                        raise ValueError(f"标准 {criterion} 的评分数据格式错误")
                    if "score" not in score_data or "reason" not in score_data:
                        raise ValueError(f"标准 {criterion} 缺少分数或理由")
                    if not 0 <= score_data["score"] <= 10:
                        raise ValueError(f"标准 {criterion} 的分数必须在 0-10 范围内")

                return {
                    "scores": {criterion: score_data["score"] for criterion, score_data in result["scores"].items()},
                    "scoreDetails": {
                        criterion: score_data["reason"] for criterion, score_data in result["scores"].items()
                    },
                    "overallScore": result["overall_score"],
                    "feedback": result["feedback"],
                }

            else:
                raise ValueError("无法在响应中找到有效的 JSON")

        except Exception as e:
            logger.error(f"解析评估结果失败: {str(e)}")
            return {"scores": {}, "scoreDetails": {}, "overallScore": 0.0, "feedback": f"解析评估结果失败: {str(e)}"}
