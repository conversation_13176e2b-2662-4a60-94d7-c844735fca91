import os
from functools import lru_cache
from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker


class DatabaseConfig:
    """数据库配置类"""

    def __init__(self):
        self.user = os.getenv("MYSQL_USERNAME")
        self.password = os.getenv("MYSQL_PASSWORD")
        self.host = os.getenv("MYSQL_HOST")
        self.port = int(os.getenv("MYSQL_PORT", "3306"))
        self.database = os.getenv("MYSQL_DATABASE_TCS", "trantor_dev_agent_ops")

    @property
    def database_url(self) -> str:
        """获取数据库URL"""
        return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"


@lru_cache()
def get_db_config() -> DatabaseConfig:
    """获取数据库配置"""
    return DatabaseConfig()


@lru_cache()
def get_engine():
    """获取数据库引擎"""
    config = get_db_config()
    return create_engine(
        config.database_url,
        pool_pre_ping=True,  # 自动检测断开的连接
        pool_recycle=3600,  # 一小时后回收连接
        pool_size=5,  # 连接池大小
        max_overflow=10,  # 最大溢出连接数
    )


SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=get_engine())


def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
