from loguru import logger
from t_ai_agent.agent_warmup import start_warmup_in_background
from t_ai_web import web_start
from t_ai_web.configuration.debugger import setup_debugger
from t_ai_web.configuration.logger import setup_logger
from t_ai_web.configuration.tracing import setup_tracing


def setup_application():
    """初始化应用程序配置"""

    # 设置日志配置
    setup_logger()

    setup_debugger()

    # 设置 tracing
    setup_tracing()

    # 初始化 Langfuse
    # if not setup_langfuse():
    #     logger.warning("Langfuse initialization failed, continuing without <PERSON><PERSON>")

    start_warmup_in_background()


if __name__ == "__main__":
    # 初始化应用程序配置
    setup_application()

    # 启动Web服务器
    logger.info("Starting t-ai2 web server...")
    web_start()
